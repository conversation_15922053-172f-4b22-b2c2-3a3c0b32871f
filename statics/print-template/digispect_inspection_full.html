<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link
      href="https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap"
      rel="stylesheet"
      type="text/css"
    />
    <style>
      body {
        font-family: "Poppins", sans-serif;
        margin: 0;
        padding: 0;
        background-color: #fff;
        color: #0c162c;
      }

      * {
        box-sizing: border-box;
      }

      .page {
        /* width: 210mm;
        height: 330mm; */
        width: 954px;
        /* height: 1497.78px; */
        margin: auto;
        background: white;
      }

      p {
        margin: 0;
      }

      .bg-primary-50 {
        background-color: #185fff;
      }

      .bg-primary-10 {
        background-color: #f1f6ff;
      }

      .text-grey-50 {
        color: #89869a;
      }

      .text-white {
        color: white;
      }

      .border-gray-100 {
        border: 1px solid #f3f4f6;
      }

      .text-xxxs {
        font-size: 0.45rem;
      }

      .text-xxs {
        font-size: 0.6rem;
        line-height: 1rem;
      }

      .text-xs {
        font-size: 0.75rem;
      }

      .text-sm {
        font-size: 0.875rem;
      }

      .text-lg {
        font-size: 1.125rem;
      }

      .font-bold {
        font-weight: 700;
      }

      .font-semibold {
        font-weight: 600;
      }

      .mb-1 {
        margin-bottom: 4px;
      }

      .mb-2 {
        margin-bottom: 8px;
      }

      .ml-18-px {
        margin-left: 18px;
      }

      .mr-10 {
        margin-right: 40px;
      }

      .py-1 {
        padding-top: 4px;
        padding-bottom: 4px;
      }

      .py-2 {
        padding-top: 8px;
        padding-bottom: 8px;
      }

      .py-5 {
        padding-top: 20px;
        padding-bottom: 20px;
      }

      .px-2 {
        padding-left: 8px;
        padding-right: 8px;
      }

      .px-4 {
        padding-left: 16px;
        padding-right: 16px;
      }

      .px-6 {
        padding-left: 24px;
        padding-right: 24px;
      }

      .img-tyre {
        text-align: center;
        border-radius: 4px;
        width: 50px;
        height: 50px;
      }

      .header-container {
        display: table;
        width: 100%;
        margin-bottom: 8px;
      }

      .header-container__img-div {
        width: 20%;
        display: table-cell;
        vertical-align: top;
      }

      .header-container__img-div-wrap {
        display: -webkit-box; 
        -webkit-box-pack: center;
      }

      .header-container-img {
        height: 82px;
        margin-right: 10px;
      }

      .header-container__center {
        width: 30%;
        display: table-cell;
        vertical-align: top;
      }

      .header-container__center-information:after {
        content: "";
        display: table;
        clear: both;
      }

      .header-container__center-information-column {
        float: left;
        width: 50%;
      }

      .detail-container:after {
        content: "";
        display: table;
        clear: both;
        table-layout: fixed;
      }

      .detail-container__item {
        width: 100%;
        display: table-cell;
        vertical-align: top;
      }

      .illustration {
        text-align: center;
      }

      .illustration-container:after {
        content: "";
        display: table;
        clear: both;
      }

      .illustration-container-item {
        display: table-cell;
        text-align: center;
      }

      .detail-container__item-tyre-information:after {
        content: "";
        display: table;
        clear: both;
      }

      .detail-container__item-tyre-information-column-left {
        float: left;
        width: 40%;
      }

      .detail-container__item-tyre-information-column-right {
        float: left;
        width: 60%;
      }

      .detail-container__item-tyre-information-column-right-notes-colon {
        float: left;
        width: 4%;
      }

      .detail-container__item-tyre-information-column-right-notes-value {
        float: left;
        width: 56%;
      }

      .detail-container__item-tyre-image:after {
        content: "";
        display: table;
        clear: both;
      }

      .detail-container__item-tyre-image-item {
        display: inline-block;
      }

      .detail-container__item-tyre-image-item-img {
        object-fit: cover;
        border-radius: 2px;
        width: 33.6px;
        height: 33.6px;
        margin: 2px;
      }

      .detail-container__item-note:after {
        content: "";
        display: table;
        clear: both;
      }

      .detail-container__item-note {
        .column-left {
          float: left;
          width: 30%;
        }

        .column-right {
          float: left;
          width: 70%;
        }
      }

      .detail-container__item-tread-depth-tyre {
        text-align: center;
        width: 126.4px;
      }

      .detail-container__item-tread-depth-tyre-img {
        width: 126.4px;
      }

      .tyreIllustration-item { 
        width: 126.4px;
      }

      .bg-yellow {
        background-color: yellowgreen !important;
      }

      .container-sizer {
        width: 158.74px;
      }

      /* NEW STYLE */
      .border-black-30 {
        border: 1px solid #E7E8EA;
      }

      .text-black-70 {
        color: #0C162C;
      }

      .text-center {
        text-align: center;
      }

      .text-right {
        text-align: right;
      }

      .text-left {
        text-align: left;
      }

      .mb-6-px {
        margin-bottom: 6px;
      }

      .mt-6-px {
        margin-top: 6px
      }

      .mb-12-px {
        margin-bottom: 12px;
      }

      .text-lh-1 {
        line-height: 1;
      }

      .detail-container__item__row {
        display: -webkit-;
        -webkit-box-orient: horizontal;
        -webkit-box-pack: center;
        -webkit-box-align: center;
        text-align: center;
        width: 100%;
        margin-bottom: 12px;
      }

      .detail-container__item-spare-tyre-card {
        border-radius: 12px; 
        border: 1.35px solid #BDD1FF; 
        width: 338px;
        height: 316px;
        display: inline-block;
      }

      .detail-container__item-spare-tyre-placeholder-card { 
        border-radius: 12px; 
        border: 1.35px solid #BDD1FF; 
        width: 338px;
        height: 242px;
        display: inline-block;
      }

      .detail-container__item-tyre-card-cell {
        display: inline-block;
        max-height: 444px;
      }

      .detail-container__item-tyre-card {
        border-radius: 12px; 
        border: 1.35px solid #BDD1FF; 
        width: 218px;
        height: 444px;
        display: inline-block;
      }

      .detail-container__item-tyre-card-header {
        display: table;
        width: 100%;
        background-color: #F1F6FF;
        padding: 8px 12px;
        border-top-left-radius: inherit;
        border-top-right-radius: inherit;
      }

      .tyre-card-header__section {
        width: 50%;
        display: table-cell;
      }

      .tyre-card-header__section__tyre-pos {
        display: table;
        width: 100%;
        align-items: center;
      }

      .tyre-card-header__section__tyre-pos-label {
        display: table-cell;
        width: 66%;
      }

      .tyre-card-header__section__tyre-pos-indicator-container {
        display: table-cell;
        width: 34%;
      }

      .tyre-card-header__section__spare-tyre-pos-label {
        display: table-cell;
        width: 40%;
      }

      .tyre-card-header__section__spare-tyre-pos-indicator-container {
        display: table-cell;
        width: 60%;
      }

      .tyre-card-header__section__tyre-pos-indicator-border {
        display: inline-block;
        margin-left: 4px;
        padding: 2px;
        height: 24px;
        width: 24px;
        background-color: #B9FF01;
        border-radius: 50%;
        vertical-align: middle;
        text-align: center;
      }

      .tyre-card-header__section__tyre-pos-indicator {
        height: 20px;
        width: 20px;
        background-color: #F8FFE6;
        border-radius: 50%;
        vertical-align: middle;
      }

      .spare-tyre-card-info__row {
        display: table;
        width: 100%;
      }

      .spare-tyre-card-info__col-brand {
        display: table-cell;
        width: 30%;
        vertical-align: top;
      }

      .spare-tyre-card-info__col-illustration {
        display: table-cell;
        width: 70%;
      }

      .detail-container__item-tyre-card-info {
        padding: 4px 12px 12px 12px;
      }

      .detail-container__item-tyre-card-info-portrait {
        padding: 4px 12px 6px 12px;
      }

      .tyre-card-info__tyre-illustration-section {
        display: table;
        width: 100%;
      }

      .tyre-card-info__tyre-illustration-section__tyre {
        width: 75%;
        display: table-cell;
      }

      .tyre-card-info__tyre-illustration-section__tyre-img {
        width: 152px;
      }

      .tyre-card-info__tyre-illustration-section__tyre-placeholder-img-container {
        text-align: center;
      }

      .tyre-card-info__tyre-illustration-section__tyre-placeholder-img {
        width: 114px;
      }

      .tyre-card-info__tyre-illustration-section__spare-tyre-img {
        width: 178px;
      }

      .tyre-card-info__tyre-illustration-section__spare-tyre-placeholder-img {
        width: 132px;
      }

      .tyre-card-info__tyre-pressure-section {
        width: 25%;
        display: table-cell;
        vertical-align: bottom;
      }

      .tyre-card-info__tyre-pressure-section-container {
        width: 40px;
        border-radius: 8px;
        padding: 6px 4px 8px 4px;
        background-color: #185FFF;
        text-align: center;
      }

      .tyre-card-info__tyre-pressure-section-container-disable {
        width: 40px;
        border-radius: 8px;
        padding: 6px 4px 8px 4px;
        background-color: #BABCC3;
        text-align: center;
      }

      .tyre-pressure-section-container__pressure-icon {
        height: 30px; 
        width: 30px;
      }

      .tyre-card-info__attachment-section {
        display: table;
        /* align-items: center; */
        /* justify-content: space-between; */
        margin-top: 4px;
        width: 100%;
        table-layout: fixed;
      }

      .tyre-card-info__attachment-section-img-container {
        display: table-cell;
        text-align: center;
        vertical-align: middle;
      }

      .tyre-card-info__attachment-section-img {
        height: 60px;
        width: 60px;
        border-radius: 8px;
      }

      .detail-container__item-axle-card-border {
        display: inline-block;
        padding: 1.35px;
        width: 218px;
        height: 348px;
        background-color: #BDD1FF;
        border-radius: 12px;
      }

      .detail-container__item-axle-card {
        border-radius: 12px;
        width: 216.65px;
        height: 346px;
        background-color: #F1F6FF;
        padding: 8px 12px;
        display: inline-block;
      }

      .inspection-result__row { 
        display: table;
        width: 100%;
      }

      .inspection-result__col {
        display: table-cell;
        width: 25%;
        padding: 3px 6px;
      }

      .inspection-result__col-mark {
        float: left;
        height: 16px;
        width: 16px;
      }

      .inspection-result__col-label {
        margin-left: 2px; float: left;
      }

      .card-spacer {
        width: 45px;
        height: 2px;
      }

      .two-tyre-axle-config-spacer {
        width: 138px;
      }

      .two-tyre-axle-config-center-spacer {
        width: 218px;
      }

      .two-tyre-spacer {
        width: 224.5px;
      }

      .spare-tyre-card-info__col-remark {
        display: table-cell;
        width: 37%;
        padding-left: 8px;
        text-align: left;
      }

      .overflow-elipsis-tyre-container {
        width: 194px;
      }

      .overflow-elipsis-spare-tyre-title-container {
        width: 93px;
      }

      .overflow-elipsis-spare-tyre-remark-container {
        width: 107px;
      }

      .overflow-elipsis {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .serial-num-overflow-container {
        width: 96px;
        display: inline-block;
      }

      .max-lines-2 {
        line-clamp: 2;
        -webkit-line-clamp: 2;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        white-space: normal;
      }

      .max-lines-3 {
        line-clamp: 3;
        -webkit-line-clamp: 3;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        white-space: normal;
      }

      .spare-tyre-card-info__col-attachment {
        display: table-cell;
        width: 63%;
      }

      .spare-tyre-card-info__col-findings {
        display: table-cell;
        width: 100%;
      }

      .tyre-card-info__tyre-pressure-section {
        align-items: end;
      }

      .axle-configuration-container {
        height: 300px;
        padding-top: 18px;
        display: -webkit-box;
        -webkit-box-pack: center;
        -webkit-box-align: center;
      }

      .tyre-base-svg-container {
        margin: -22px 0 -16px -10px;
      }
      /* END NEW STYLE */

      /* VEHICLE ATTACHMENT STYLE */
      .vehicle-attachment-card-margin {
        margin-left: 36px;
      }
      .detail-container__item-vehicle-attachment-card { 
        border-radius: 12px; 
        border: 1.35px solid #E3FF98; 
        width: 416px;
        display: inline-block;
      }
      .detail-container__item-vehicle-attachment-header {
        display: table;
        width: 100%;
        background-color: #F8FFE6;
        padding: 8px 12px;
        border-top-left-radius: inherit;
        border-top-right-radius: inherit;
      }
      .detail-container__item-vehicle-attachment-body {
        padding: 4px;
      }
      .vehicle-attachment__display-section {
        display: table;
        width: 100%;
        table-layout: fixed;
      }
      .vehicle-attachment__display-section-img-container {
        display: table-cell;
        text-align: center;
        vertical-align: middle;
      }
      .vehicle-attachment__display-section-img {
        height: 131px;
        width: 131px;
        border-radius: 8px;
      }
      .detail-container__item-vehicle-attachment-cell {
        display: table-cell;
        vertical-align: middle;
      }
      /* END OF VEHICLE ATTACHMENT STYLE */

      .finding-label-container {
        display: inline-block; 
        background-color: #e9e9e9; 
        border-radius: 4px; 
        padding: 4px; 
        margin-right: 4px;
        margin-bottom: 4px;
      }
            
      @media print {
        body,
        .page {
          margin: 0;
          -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
        }

        .pagebreak-inside {
          page-break-inside: avoid;
        }

        @page {
          margin-top: 30px;
          margin-bottom: 0;
        }
      }
    </style>
  </head>
  <body>
    <!-- START OF HEADER -->
    <div class="page">
      <div class="header-container">
        <div class="header-container__img-div">
          <div class="header-container__img-div-wrap">
            <img
              src="{{ .ClientPhoto }}"
              alt="Client Logo"
              class="header-container-img"
            />  
          </div>
        </div>
        <div>
          <div id="header-title">
            <p class="text-lg font-bold text-center">CATATAN PEMERIKSAAN</p>
          </div>
          <!-- INSPECTION HEADER INFO LEFT COL -->
          <div class="header-container__center text-black-70">
            <div class="header-container__center-information">
              {{ if ne .TicketNumber "" }}
              <div id="work-order-header-info">
                <div class="header-container__center-information-column">
                  <p class="text-xs">No. Work Order</p>
                </div>
                <div class="header-container__center-information-column">
                  <p class="text-xs">: {{ .TicketNumber }}</p>
                </div>
              </div>   
              {{ end }}
              <div id="inspection-no-header-info">
                <div class="header-container__center-information-column">
                  <p class="text-xs">No. Inspeksi</p>
                </div>
                <div class="header-container__center-information-column">
                  <p class="text-xs">: {{ .AssetInspection.InspectionNumber }}</p>
                </div>
              </div>
              <div id="date-header-info">
                <div class="header-container__center-information-column">
                  <p class="text-xs">Tanggal</p>
                </div>
                <div class="header-container__center-information-column">
                  <p class="text-xs">: {{ .AssetInspection.UpdatedAt.Format "2 Jan 2006 15:04" }}</p>
                </div>
              </div>
              <div id="odometer-header-info">
                <div class="header-container__center-information-column">
                  <p class="text-xs">Odometer/Hourmeter</p>
                </div>
                <div class="header-container__center-information-column">
                  <p class="text-xs">: {{ printf "%.0f" .Odometer }} {{ .OdometerUnit }}</p>
                </div>
              </div>
            </div>
          </div>
          <!-- INSPECTION HEADER INFO RIGHT  COL -->
          <div class="header-container__center text-black-70">
            <div class="header-container__center-information">
              <div id="ref-no-header-info">
                <div class="header-container__center-information-column">
                  <p class="text-xs">Pelat No./No. Seri</p>
                </div>
                <div class="header-container__center-information-column">
                  <p class="text-xs">: {{ .ReferenceNumber }}</p>
                </div>
              </div>
              {{ if ne .AssetInspectionVehicle.PartnerOwnerName "" }}
              <div id="cust-name-header-info">
                <div class="header-container__center-information-column">
                  <p class="text-xs">Nama Pelanggan</p>
                </div>
                <div class="header-container__center-information-column">
                  <p class="text-xs">: {{ .AssetInspectionVehicle.PartnerOwnerName }}</p>
                </div>
              </div>
              {{ end }}
              {{ if ne .BrandModelLabel "" }}
              <div id="brand-model-header-info">
                <div class="header-container__center-information-column">
                  <p class="text-xs">Merek & Model</p>
                </div>
                <div class="header-container__center-information-column">
                  <p class="text-xs">: 
                    {{ .BrandModelLabel }}
                  </p>
                </div>
              </div>
              {{ end }}
            </div>
          </div>
        </div>
        <div class="header-container__img-div">
          {{ if ne .SecondaryClientPhoto "" }}
          <div class="header-container__img-div-wrap">
            <img
              src="{{ .SecondaryClientPhoto }}"
              alt=""
              class="header-container-img"
            />
          </div>
          {{ end }}
        </div>
      </div>
    </div>
    <!-- END OF HEADER -->
    <!-- START OF BODY -->
    <div>
      <div class="bg-primary-10 px-2 py-2 mb-12-px">
        <p class="text-sm font-semibold text-black-70">Inspeksi Ban</p>
      </div>
      {{ range .TyreLayoutDataArr }}
      <div class="detail-container__item__row">
        {{ range . }}
        {{ if eq .LayoutType "AXLE_CONFIGURATION" }}
        <div class="detail-container__item-tyre-card-cell text-center">
          <div class="detail-container__item-axle-card-border">
            <div class="detail-container__item detail-container__item-axle-card pagebreak-inside">
              <p class="text-xs font-semibold text-black-70 text-center">Ilustrasi Posisi Ban</p>
              <div class="axle-configuration-container">
                <div id="axle-configuration-zoom-scale" style="zoom: {{ $.AxleConfigurationZoom }};">
                {{ range $idx, $item := $.AxleConfigurationItems }}
                  {{ $item }}<br>
                {{ end }}
                </div>
              </div>
            </div>
          </div>
        </div>
        {{ end }}
        {{ if eq .LayoutType "CARD_SPACER" }}
        <div class="detail-container__item-tyre-card-cell text-center">
          <div class="card-spacer"></div>
        </div>
        {{ end }}
        {{ if eq .LayoutType "AXLE_SIDE_SPACER" }}
        <div class="detail-container__item-tyre-card-cell text-left">
          <div class="two-tyre-axle-config-spacer"></div>
        </div>
        {{ end }}
        {{ if eq .LayoutType "AXLE_SPACER" }}
        <div class="detail-container__item-tyre-card-cell text-left">
          <div class="two-tyre-axle-config-center-spacer"></div>
        </div>
        {{ end }}
        {{ if eq .LayoutType "TWO_TYRE_SIDE_SPACER" }}
        <div class="detail-container__item-tyre-card-cell text-left">
          <div class="two-tyre-spacer"></div>
        </div>
        {{ end }}
        {{ if eq .LayoutType "TYRE_PLACEHOLDER" }}
        <div class="detail-container__item-tyre-card-cell text-left">
          <div class="detail-container__item detail-container__item-tyre-card pagebreak-inside">
            <div class="detail-container__item-tyre-card-header">
              <div class="tyre-card-header__section">
                <div class="tyre-card-header__section__tyre-pos">
                  <div class="tyre-card-header__section__tyre-pos-label">
                    <p class="text-xs font-semibold text-black-70">Ban Posisi </p>
                  </div>
                  <div class="tyre-card-header__section__tyre-pos-indicator-container">
                    <div class="tyre-card-header__section__tyre-pos-indicator-border">
                      <div class="text-xs font-semibold text-black-70 text-center tyre-card-header__section__tyre-pos-indicator">
                        {{ .TyreCount }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="text-xs text-black-70 tyre-card-header__section text-right">-</div>
            </div>
            <div class="detail-container__item-tyre-card-info-portrait">
              <div class="overflow-elipsis-tyre-container">
                <p class="text-xs text-black-70 text-center mb-6-px overflow-elipsis">- / -</p>
              </div>
              <div class="tyre-card-info__tyre-illustration-section mb-6-px">
                <div class="text-xs tyre-card-info__tyre-illustration-section__tyre">
                  <!-- TREAD WAVE ILLUSTRATION PLACEHOLDER -->
                  <div style="display: table; width: 100%;">
                    <!-- START OF TREAD WAVE SECTION -->
                    <div 
                      class="tread-wave-section" 
                      style="
                        display: -webkit-box;
                        -webkit-box-orient: horizontal;
                        -webkit-box-pack: center;
                        -webkit-box-align: center;
                      "
                    >
                      <div class="tyrewave-parent-container" style="zoom: 0.6;">
                        <div style="width: {{ $.TyreWavePlaceholderSvg.TyreWaveWidth }}px;">
                          <!-- MM LABEL -->
                          <div
                            class="tread-uom"
                            style="
                              font-size: 12px;
                              text-align: center;
                              margin-bottom: 12px;
                              font-style: italic;
                              line-height: 18px;
                              color: #89869a;
                            "
                          >
                            Tapak Ban (mm)
                          </div>
                          <!-- TREAD VALUES -->
                          <div
                            class="tread-value"
                            style="
                              display: -webkit-box;
                              -webkit-box-pack: justify;
                              margin: 0 18px 10px 18px;
                            "
                          >
                            {{ range $idx, $item := $.TyreWavePlaceholderSvg.TreadDepths }}
                            <div
                              id="treadDepthsId-{{ $idx }}"
                              style="
                                background-color: white;
                                border: 1.5px solid #eef0f7;
                                border-radius: 8px;
                                color: black;
                                font-size: 12px;
                                font-weight: 400;
                                width: 38px;
                                height: 28px;
                                text-align: center;
                                line-height: 26px;
                              "
                            >
                              {{ if gt $item 0.0 }}
                                {{ printf "%.1f" $item }} 
                              {{ else }}
                                -
                              {{ end }}
                            </div>
                            {{ end }}
                          </div>
                          <!-- TREAD WAVE -->
                          <div
                            class="tyre-tread"
                            style="
                              display: -webkit-box;
                              -webkit-box-pack: justify;
                              width: 100%;
                              height: 30px;
                              max-height: 30px;
                              margin-bottom: 2px;
                            "
                          >
                            {{ range $.TyreWavePlaceholderSvg.TyreTreads }}
                            <div>
                              {{ .Svg }}
                            </div>
                            {{ end }}
                          </div>
                          <!-- Tread Base -->
                          <div style="display: -webkit-box; margin-top: -2px;">
                            <div
                              style="
                                position: absolute;
                                width: {{ $.TyreWavePlaceholderSvg.TyreWaveWidth }}px;
                                display: -webkit-box;
                                -webkit-box-pack: center;
                              "
                            >
                              <div
                                style="
                                  color: black;
                                  font-size: 10px;
                                  font-weight: 400;
                                  line-height: 18px;
                                "
                              >
                                {{ if $.TyreWavePlaceholderSvg.ShowOTD }}
                                OTD: {{ $.TyreWavePlaceholderSvg.OTDLabel }}
                                {{ end }}
                              </div>
                            </div>
                      
                            <div class="base-tread" style="width: 100%;">
                              {{ $.TyreWavePlaceholderSvg.BaseTread }}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <!-- END OF TREAD WAVE SECTION -->
                    <!-- Average RTD -->
                    <div>
                      <p class="text-xs text-center">
                        <span class="text-grey-50">Rata-rata&nbsp:&nbsp</span>
                        <span class="text-black-70">
                          -
                        </span>
                      </p>
                    </div>
                  </div>
                </div>
                <div class="tyre-card-info__tyre-pressure-section" style="padding-left: 7.8px;">
                  <div class="tyre-card-info__tyre-pressure-section-container-disable">
                    <img src="{{ $.PressureDisabledImg }}" alt="Pressure Icon" class="tyre-pressure-section-container__pressure-icon">
                    <p class="text-xs text-black-70 font-semibold text-center text-lh-1">-</p>
                    <p class="text-xs text-black-70 text-center text-lh-1">psi</p>
                  </div>
                </div>
              </div>
              <div class="mb-4-px">
                <p class="text-xs text-grey-50 text-left">Gambar</p>
                <div class="tyre-card-info__attachment-section">
                  <p class="text-xs text-black-70 text-left">-</p>
                </div>
              </div>
              <div>
                <p class="text-xs text-grey-50 text-left">Catatan</p>
                <div class="overflow-elipsis-tyre-container">
                  <p class="text-xs text-black-70 text-left overflow-elipsis max-lines-2">-</p>
                </div>
              </div>
              <div>
                <p class="text-xs text-grey-50 text-left">Temuan / Kondisi Ban</p>
                <div class="overflow-elipsis-tyre-container">
                  <p class="text-xs text-black-70">-</p>
                </div>
              </div>
            </div>
          </div>
        </div>
        {{ end }}
        {{ if eq .LayoutType "TYRE" }}
        <div class="detail-container__item-tyre-card-cell text-left">
          <div class="detail-container__item detail-container__item-tyre-card pagebreak-inside">
            <div class="detail-container__item-tyre-card-header">
              <div class="tyre-card-header__section">
                <div class="tyre-card-header__section__tyre-pos">
                  <div class="tyre-card-header__section__tyre-pos-label">
                    <p class="text-xs font-semibold text-black-70">Ban Posisi </p>
                  </div>
                  <div class="tyre-card-header__section__tyre-pos-indicator-container">
                    <div class="tyre-card-header__section__tyre-pos-indicator-border">
                      <div class="text-xs font-semibold text-black-70 text-center tyre-card-header__section__tyre-pos-indicator">
                        {{ printf "%.0f" .AssetInspectionTyre.AssetInspectionTyre.TyrePosition }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="text-xs text-black-70 tyre-card-header__section text-right">
                <div class="serial-num-overflow-container">
                  <p>
                    #{{ .AssetInspectionTyre.SerialNo }}
                  </p>
                </div>
              </div>
            </div>
            <div class="detail-container__item-tyre-card-info-portrait">
              {{ if ne .AssetInspectionTyre.Rfid "" }}
              <div class="overflow-elipsis-tyre-container">
                <p class="text-xs text-black-70 text-center mb-4-px overflow-elipsis">
                  <span class="text-grey-50">RFID :</span> 
                  {{ .AssetInspectionTyre.Rfid }}
                </p>
              </div>
              {{ end }}
              <div class="overflow-elipsis-tyre-container">
                <p class="text-xs text-black-70 text-center mb-6-px overflow-elipsis">
                  {{ .AssetInspectionTyre.BrandName }} / {{ .AssetInspectionTyre.TyreSize }}
                </p>
              </div>
              <div class="tyre-card-info__tyre-illustration-section mb-6-px">
                <div class="text-xs tyre-card-info__tyre-illustration-section__tyre">
                  <!-- TREAD WAVE ILLUSTRATION -->
                  <div style="display: table; width: 100%;">
                    <!-- START OF TREAD WAVE SECTION -->
                    <div 
                      class="tread-wave-section" 
                      style="
                        display: -webkit-box;
                        -webkit-box-orient: horizontal;
                        -webkit-box-pack: center;
                        -webkit-box-align: center;
                      "
                    >
                      <div class="tyrewave-parent-container" style="zoom: 0.6;">
                        <div style="width: {{ .AssetInspectionTyre.TyreWaveSvg.TyreWaveWidth }}px;">
                          <!-- MM LABEL -->
                          <div
                            class="tread-uom"
                            style="
                              font-size: 12px;
                              text-align: center;
                              margin-bottom: 12px;
                              font-style: italic;
                              line-height: 18px;
                              color: #89869a;
                            "
                          >
                            Tapak Ban (mm)
                          </div>
                          <!-- TREAD VALUES -->
                          <div
                            class="tread-value"
                            style="
                              display: -webkit-box;
                              -webkit-box-pack: justify;
                              margin: 0 18px 10px 18px;
                            "
                          >
                            {{ range $idx, $item := .AssetInspectionTyre.TyreWaveSvg.TreadDepths }}
                            <div
                              id="treadDepthsId-{{ $idx }}"
                              style="
                                background-color: white;
                                border: 1.5px solid #eef0f7;
                                border-radius: 8px;
                                color: black;
                                font-size: 12px;
                                font-weight: 400;
                                width: 38px;
                                height: 28px;
                                text-align: center;
                                line-height: 26px;
                              "
                            >
                              {{ if gt $item 0.0 }}
                                {{ printf "%.1f" $item }} 
                              {{ else }}
                                -
                              {{ end }}
                            </div>
                            {{ end }}
                          </div>
                          <!-- TREAD WAVE -->
                          <div
                            class="tyre-tread"
                            style="
                              display: -webkit-box;
                              -webkit-box-pack: justify;
                              width: 100%;
                              height: 30px;
                              max-height: 30px;
                              margin-bottom: 2px;
                            "
                          >
                            {{ range .AssetInspectionTyre.TyreWaveSvg.TyreTreads }}
                            <div>
                              {{ .Svg }}
                            </div>
                            {{ end }}
                          </div>
                          <!-- Tread Base -->
                          <div style="display: -webkit-box; margin-top: -2px;">
                            <div
                              style="
                                position: absolute;
                                width: {{ .AssetInspectionTyre.TyreWaveSvg.TyreWaveWidth }}px;
                                display: -webkit-box;
                                -webkit-box-pack: center;
                              "
                            >
                              <div
                                style="
                                  color: black;
                                  font-size: 10px;
                                  font-weight: 400;
                                  line-height: 18px;
                                "
                              >
                                {{ if .AssetInspectionTyre.TyreWaveSvg.ShowOTD }}
                                OTD: {{ .AssetInspectionTyre.TyreWaveSvg.OTDLabel }}
                                {{ end }}
                              </div>
                            </div>
                      
                            <div class="base-tread" style="width: 100%;">
                              {{ .AssetInspectionTyre.TyreWaveSvg.BaseTread }}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <!-- END OF TREAD WAVE SECTION -->
                    <!-- Average RTD -->
                    <div>
                      <p class="text-xs text-center">
                        <span class="text-grey-50">Rata-rata&nbsp:&nbsp</span>
                        <span class="text-black-70">
                          {{ if eq .AssetInspectionTyre.AverageRTD 0.0 }} 
                            -
                          {{ else }}
                            {{ printf "%.1f" .AssetInspectionTyre.AverageRTD }} 
                            {{ if .AssetInspectionTyre.TyreWaveSvg.ShowOTD }}
                              {{ if gt .AssetInspectionTyre.RemainingTUR 0.0 }}
                                ({{ printf "%.0f" .AssetInspectionTyre.RemainingTUR }}%)
                              {{ end }}
                            {{ end }}
                          {{ end }}
                        </span>
                      </p>
                    </div>
                  </div>
                </div>
                <div class="tyre-card-info__tyre-pressure-section">
                  {{ if gt .AssetInspectionTyre.AssetInspectionTyre.Pressure 0.0 }}
                  <div class="tyre-card-info__tyre-pressure-section-container">
                    <img src="{{ $.PressureEnabledImg }}" alt="Pressure Icon" class="tyre-pressure-section-container__pressure-icon">
                    <p class="text-xs text-white font-semibold text-center text-lh-1">
                      {{ printf "%.0f" .AssetInspectionTyre.AssetInspectionTyre.Pressure }}
                    </p>
                    <p class="text-xs text-white text-center text-lh-1">psi</p>
                  </div>
                  {{ else }}
                  <div class="tyre-card-info__tyre-pressure-section-container-disable">
                    <img src="{{ $.PressureDisabledImg }}" alt="Pressure Icon" class="tyre-pressure-section-container__pressure-icon">
                    <p class="text-xs text-black-70 font-semibold text-center text-lh-1">-</p>
                    <p class="text-xs text-black-70 text-center text-lh-1">psi</p>
                  </div>
                  {{ end }}
                </div>
              </div>
              <div class="mb-4-px">
                <p class="text-xs text-grey-50 text-left">Gambar</p>
                <div class="tyre-card-info__attachment-section">
                  {{ if gt (len .AssetInspectionTyre.Attachments) 0 }}
                    {{ range .AssetInspectionTyre.Attachments }}
                    <div class="tyre-card-info__attachment-section-img-container">
                      <img src="{{ .Path }}" alt="Attachment" class="tyre-card-info__attachment-section-img"/>
                    </div>
                    {{ end }}
                    {{ if eq (len .AssetInspectionTyre.Attachments) 1 }}
                    <div class="tyre-card-info__attachment-section-img-container">
                    </div>
                    <div class="tyre-card-info__attachment-section-img-container">
                    </div>
                    {{ end }}
                    {{ if eq (len .AssetInspectionTyre.Attachments) 2 }}
                    <div class="tyre-card-info__attachment-section-img-container">
                    </div>
                    {{ end }}
                  {{ else }}
                    <p class="text-xxs text-black-70 text-left">-</p>
                  {{ end }}
                </div>
              </div>
              <div>
                <p class="text-xs text-grey-50 text-left">Catatan</p>
                <div class="overflow-elipsis-tyre-container">
                  <p class="text-xs text-black-70 text-left overflow-elipsis max-lines-2">
                    {{ if .AssetInspectionTyre.AssetInspectionTyre.Remark }}
                      {{ .AssetInspectionTyre.AssetInspectionTyre.Remark }}
                    {{else}}
                      -
                    {{end}}
                  </p>
                </div>
              </div>
              <div>
                <p class="text-xs text-grey-50 text-left">Temuan / Kondisi Ban</p>
                <div class="overflow-elipsis-tyre-container">
                  {{ if gt (len .AssetInspectionTyre.FindingLabels) 0 }}
                  <span>
                    {{ range .AssetInspectionTyre.FindingLabels }}
                    <div class="finding-label-container">
                      <p class="text-xs text-black-70">{{ . }}</p>
                    </div>
                    {{ end }}
                  </span>
                  {{ else }}
                  <p class="text-xs text-black-70">-</p>
                  {{ end }}
                </div>
              </div>
            </div>
          </div>
        </div>
        {{ end }}
        {{ if eq .LayoutType "SPARE_TYRE" }}
        <div class="detail-container__item-tyre-card-cell text-center">
          <div class="detail-container__item detail-container__item-spare-tyre-card pagebreak-inside">
            <div class="detail-container__item-tyre-card-header">
              <div class="tyre-card-header__section">
                <div class="tyre-card-header__section__tyre-pos">
                  <div class="tyre-card-header__section__spare-tyre-pos-label">
                    <p class="text-xs font-semibold text-black-70">Ban Posisi </p>
                  </div>
                  <div class="tyre-card-header__section__spare-tyre-pos-indicator-container text-left">
                    <div class="tyre-card-header__section__tyre-pos-indicator-border">
                      <div class="text-xs font-semibold text-black-70 text-center tyre-card-header__section__tyre-pos-indicator">
                        {{ printf "%.0f" .AssetInspectionTyre.AssetInspectionTyre.TyrePosition }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="text-xs text-black-70 tyre-card-header__section text-right">
                {{ if ne .AssetInspectionTyre.AssetInspectionTyre.AssetTyre.Asset.SerialNumber "" }}
                  #{{ .AssetInspectionTyre.AssetInspectionTyre.AssetTyre.Asset.SerialNumber }}
                {{ else if ne .AssetInspectionTyre.AssetInspectionTyre.CustomSerialNumber "" }}
                  #{{ .AssetInspectionTyre.AssetInspectionTyre.CustomSerialNumber }}
                {{ else }}
                  #-  
                {{ end }}
              </div>
            </div>
            <div class="detail-container__item-tyre-card-info">
              <div class="spare-tyre-card-info__row">
                <div class="spare-tyre-card-info__col-brand">
                  {{ if ne .AssetInspectionTyre.Rfid "" }}
                  <div class="overflow-elipsis-spare-tyre-title-container">
                    <p class="text-xs text-black-70 mb-6-px text-left overflow-elipsis">
                      <span class="text-grey-50">RFID :</span> 
                      <br> {{ .AssetInspectionTyre.Rfid }}
                    </p>
                  </div>
                  {{ end }}
                  <div class="overflow-elipsis-spare-tyre-title-container">
                    <p class="text-xs text-black-70 mb-12-px text-left overflow-elipsis">
                      {{ .AssetInspectionTyre.BrandName }} /<br> {{ .AssetInspectionTyre.TyreSize }}
                    </p>
                  </div>
                </div>
                <div class="spare-tyre-card-info__col-illustration">
                  <div class="tyre-card-info__tyre-illustration-section mb-12-px">
                    <div class="text-xs tyre-card-info__tyre-illustration-section__tyre">
                      <!-- TREAD WAVE ILLUSTRATION -->
                      <div style="display: table; width: 100%;">
                        <!-- START OF TREAD WAVE SECTION -->
                        <div 
                          class="tread-wave-section" 
                          style="
                            display: -webkit-box;
                            -webkit-box-orient: horizontal;
                            -webkit-box-pack: center;
                            -webkit-box-align: center;
                          "
                        >
                          <div class="tyrewave-parent-container" style="zoom: 0.6;">
                            <div style="width: {{ .AssetInspectionTyre.TyreWaveSvg.TyreWaveWidth }}px;">
                              <!-- MM LABEL -->
                              <div
                                class="tread-uom"
                                style="
                                  font-size: 12px;
                                  text-align: center;
                                  margin-bottom: 12px;
                                  font-style: italic;
                                  line-height: 18px;
                                  color: #89869a;
                                "
                              >
                                Tapak Ban (mm)
                              </div>
                              <!-- TREAD VALUES -->
                              <div
                                class="tread-value"
                                style="
                                  display: -webkit-box;
                                  -webkit-box-pack: justify;
                                  margin: 0 18px 10px 18px;
                                "
                              >
                                {{ range $idx, $item := .AssetInspectionTyre.TyreWaveSvg.TreadDepths }}
                                <div
                                  id="treadDepthsId-{{ $idx }}"
                                  style="
                                    background-color: white;
                                    border: 1.5px solid #eef0f7;
                                    border-radius: 8px;
                                    color: black;
                                    font-size: 12px;
                                    font-weight: 400;
                                    width: 38px;
                                    height: 28px;
                                    text-align: center;
                                    line-height: 26px;
                                  "
                                >
                                  {{ if gt $item 0.0 }}
                                    {{ printf "%.1f" $item }} 
                                  {{ else }}
                                    -
                                  {{ end }}
                                </div>
                                {{ end }}
                              </div>
                              <!-- TREAD WAVE -->
                              <div
                                class="tyre-tread"
                                style="
                                  display: -webkit-box;
                                  -webkit-box-pack: justify;
                                  width: 100%;
                                  height: 30px;
                                  max-height: 30px;
                                  margin-bottom: 2px;
                                "
                              >
                                {{ range .AssetInspectionTyre.TyreWaveSvg.TyreTreads }}
                                <div>
                                  {{ .Svg }}
                                </div>
                                {{ end }}
                              </div>
                              <!-- Tread Base -->
                              <div style="display: -webkit-box; margin-top: -2px;">
                                <div
                                  style="
                                    position: absolute;
                                    width: {{ .AssetInspectionTyre.TyreWaveSvg.TyreWaveWidth }}px;
                                    display: -webkit-box;
                                    -webkit-box-pack: center;
                                  "
                                >
                                  <div
                                    style="
                                      color: black;
                                      font-size: 10px;
                                      font-weight: 400;
                                      line-height: 18px;
                                    "
                                  >
                                    {{ if .AssetInspectionTyre.TyreWaveSvg.ShowOTD }}
                                    OTD: {{ .AssetInspectionTyre.TyreWaveSvg.OTDLabel }}
                                    {{ end }}
                                  </div>
                                </div>
                          
                                <div class="base-tread" style="width: 100%;">
                                  {{ .AssetInspectionTyre.TyreWaveSvg.BaseTread }}
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <!-- END OF TREAD WAVE SECTION -->
                        <!-- Average RTD -->
                        <div>
                          <p class="text-xs text-center">
                            <span class="text-grey-50">Rata-rata&nbsp:&nbsp</span>
                            <span class="text-black-70">
                              {{ if eq .AssetInspectionTyre.AverageRTD 0.0 }} 
                                -
                              {{ else }}
                                {{ printf "%.1f" .AssetInspectionTyre.AverageRTD }} 
                                {{ if .AssetInspectionTyre.TyreWaveSvg.ShowOTD }}
                                  {{ if gt .AssetInspectionTyre.RemainingTUR 0.0 }}
                                    ({{ printf "%.0f" .AssetInspectionTyre.RemainingTUR }}%)
                                  {{ end }}
                                {{ end }}
                              {{ end }}
                            </span>
                          </p>
                        </div>
                      </div>
                    </div>
                    <div class="tyre-card-info__tyre-pressure-section">
                      {{ if gt .AssetInspectionTyre.AssetInspectionTyre.Pressure 0.0 }}
                      <div class="tyre-card-info__tyre-pressure-section-container">
                        <img src="{{ $.PressureEnabledImg }}" alt="Pressure Icon" class="tyre-pressure-section-container__pressure-icon">
                        <p class="text-xs text-white font-semibold text-center text-lh-1">
                          {{ printf "%.0f" .AssetInspectionTyre.AssetInspectionTyre.Pressure }}
                        </p>
                        <p class="text-xs text-white text-center text-lh-1">psi</p>
                      </div>
                      {{ else }}
                      <div class="tyre-card-info__tyre-pressure-section-container-disable">
                        <img src="{{ $.PressureDisabledImg }}" alt="Pressure Icon" class="tyre-pressure-section-container__pressure-icon">
                        <p class="text-xs text-black-70 font-semibold text-center text-lh-1">-</p>
                        <p class="text-xs text-black-70 text-center text-lh-1">psi</p>
                      </div>
                      {{ end }}
                    </div>
                  </div>
                </div>
              </div>
              <div class="spare-tyre-card-info__row">
                <div class="spare-tyre-card-info__col-attachment">
                  <p class="text-xs text-grey-50 text-left">Gambar</p>
                  <div class="tyre-card-info__attachment-section">
                    {{ if gt (len .AssetInspectionTyre.Attachments) 0 }}
                      {{ range .AssetInspectionTyre.Attachments }}
                      <div class="tyre-card-info__attachment-section-img-container">
                        <img src="{{ .Path }}"
                          alt="Attachment"
                          class="tyre-card-info__attachment-section-img"
                        />
                      </div>
                      {{ end }}
                      {{ if eq (len .AssetInspectionTyre.Attachments) 1 }}
                      <div class="tyre-card-info__attachment-section-img-container">
                      </div>
                      <div class="tyre-card-info__attachment-section-img-container">
                      </div>
                      {{ end }}
                      {{ if eq (len .AssetInspectionTyre.Attachments) 2 }}
                      <div class="tyre-card-info__attachment-section-img-container">
                      </div>
                      {{ end }}
                    {{ else }}
                      <p class="text-xxs text-black-70 text-left">-</p>
                    {{ end }}
                  </div>
                </div>
                <div class="spare-tyre-card-info__col-remark">
                  <p class="text-xs text-grey-50 text-left">Catatan</p>
                  <div class="overflow-elipsis-spare-tyre-remark-container">
                    <p class="text-xs text-black-70 text-left">
                      {{ .AssetInspectionTyre.Remark }}
                    </p>
                  </div>
                </div>
              </div>
              <div class="spare-tyre-card-info__row">
                <div class="spare-tyre-card-info__col-findings">
                  <p class="text-xs text-grey-50 text-left">Temuan / Kondisi Ban</p>
                  {{ if gt (len .AssetInspectionTyre.FindingLabels) 0 }}
                  <div style="width: 100%; text-align: left;">
                    <span>
                      {{ range .AssetInspectionTyre.FindingLabels }}
                      <div class="finding-label-container">
                        <p class="text-xs text-black-70">{{ . }}</p>
                      </div>
                      {{ end }}
                    </span>
                  </div>
                  {{ else }}
                  <p class="text-xs text-black-70 text-left">-</p>
                  {{ end }}
                </div>
              </div>
            </div>
          </div>
          {{ if eq $.SpareTyreShowAttachmentPos .TyreCount }}
          <div class="detail-container__item detail-container__item-vehicle-attachment-card pagebreak-inside vehicle-attachment-card-margin">
            <div class="detail-container__item-vehicle-attachment-header">
              <p class="text-xs text-left font-semibold text-black-70">Gambar Inspeksi Kendaraan</p>
            </div>
            <div class="detail-container__item-vehicle-attachment-body">
              <div>
                <div class="vehicle-attachment__display-section">
                  {{ if gt (len $.VehicleAttachments) 0 }}
                    {{ range $.VehicleAttachments }}
                    <div class="vehicle-attachment__display-section-img-container">
                      <img src="{{ .Path }}"
                        alt="Attachment"
                        class="vehicle-attachment__display-section-img"
                      />
                    </div>
                    {{ end }}
                    {{ if eq (len $.VehicleAttachments) 1 }}
                    <div class="vehicle-attachment__display-section-img-container"></div>
                    {{ end }}
                    {{ if eq (len $.VehicleAttachments) 2 }}
                    <div class="vehicle-attachment__display-section-img-container"></div>
                    {{ end }}
                  {{ end }}
                  {{ if eq (len $.VehicleAttachments) 0 }}
                    <p class="text-xs text-black-70 text-left">-</p>
                  {{ end }}
                </div>
                <div class="vehicle-attachment__remark-section" style="margin-top: 8px;">
                  <p class="text-xs text-grey-50 text-left">Catatan Inspeksi Kendaraan</p>
                  <p class="text-xs text-black-70 text-left">
                    {{ if ne $.AssetInspection.Remark "" }}
                      {{ $.AssetInspection.Remark }}
                    {{ else }}
                      -
                    {{ end }}
                  </p>
                </div>
              </div>
            </div>
          </div>
          {{ end }}
        </div>
        {{ end }}
        {{ if eq .LayoutType "SPARE_TYRE_PLACEHOLDER" }}
        <div class="detail-container__item-tyre-card-cell text-center">
          <div class="detail-container__item detail-container__item-spare-tyre-placeholder-card pagebreak-inside">
            <div class="detail-container__item-tyre-card-header">
              <div class="tyre-card-header__section">
                <div class="tyre-card-header__section__tyre-pos">
                  <div class="tyre-card-header__section__spare-tyre-pos-label">
                    <p class="text-xs font-semibold text-black-70">Ban Posisi </p>
                  </div>
                  <div class="tyre-card-header__section__spare-tyre-pos-indicator-container text-left">
                    <div class="tyre-card-header__section__tyre-pos-indicator-border">
                      <div class="text-xs font-semibold text-black-70 text-center tyre-card-header__section__tyre-pos-indicator">
                        {{ .TyreCount }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="text-xs text-black-70 tyre-card-header__section text-right">-</div>
            </div>
            <div class="detail-container__item-tyre-card-info">
              <div class="spare-tyre-card-info__row">
                <div class="spare-tyre-card-info__col-brand">
                  <div class="overflow-elipsis-spare-tyre-title-container">
                    <p class="text-xs text-black-70 mb-12-px text-left overflow-elipsis">- / -</p>
                  </div>
                </div>
                <div class="spare-tyre-card-info__col-illustration">
                  <div class="tyre-card-info__tyre-illustration-section mb-12-px">
                    <div class="text-xs tyre-card-info__tyre-illustration-section__tyre">
                      <!-- TREAD WAVE ILLUSTRATION PLACEHOLDER -->
                      <div style="display: table; width: 100%;">
                        <!-- START OF TREAD WAVE SECTION -->
                        <div 
                          class="tread-wave-section" 
                          style="
                            display: -webkit-box;
                            -webkit-box-orient: horizontal;
                            -webkit-box-pack: center;
                            -webkit-box-align: center;
                          "
                        >
                          <div class="tyrewave-parent-container" style="zoom: 0.6;">
                            <div style="width: {{ $.TyreWavePlaceholderSvg.TyreWaveWidth }}px;">
                              <!-- MM LABEL -->
                              <div
                                class="tread-uom"
                                style="
                                  font-size: 12px;
                                  text-align: center;
                                  margin-bottom: 12px;
                                  font-style: italic;
                                  line-height: 18px;
                                  color: #89869a;
                                "
                              >
                                Tapak Ban (mm)
                              </div>
                              <!-- TREAD VALUES -->
                              <div
                                class="tread-value"
                                style="
                                  display: -webkit-box;
                                  -webkit-box-pack: justify;
                                  margin: 0 18px 10px 18px;
                                "
                              >
                                {{ range $idx, $item := $.TyreWavePlaceholderSvg.TreadDepths }}
                                <div
                                  id="treadDepthsId-{{ $idx }}"
                                  style="
                                    background-color: white;
                                    border: 1.5px solid #eef0f7;
                                    border-radius: 8px;
                                    color: black;
                                    font-size: 12px;
                                    font-weight: 400;
                                    width: 38px;
                                    height: 28px;
                                    text-align: center;
                                    line-height: 26px;
                                  "
                                >
                                  {{ if gt $item 0.0 }}
                                    {{ printf "%.1f" $item }} 
                                  {{ else }}
                                    -
                                  {{ end }}
                                </div>
                                {{ end }}
                              </div>
                              <!-- TREAD WAVE -->
                              <div
                                class="tyre-tread"
                                style="
                                  display: -webkit-box;
                                  -webkit-box-pack: justify;
                                  width: 100%;
                                  height: 30px;
                                  max-height: 30px;
                                  margin-bottom: 2px;
                                "
                              >
                                {{ range $.TyreWavePlaceholderSvg.TyreTreads }}
                                <div>
                                  {{ .Svg }}
                                </div>
                                {{ end }}
                              </div>
                              <!-- Tread Base -->
                              <div style="display: -webkit-box; margin-top: -2px;">
                                <div
                                  style="
                                    position: absolute;
                                    width: {{ $.TyreWavePlaceholderSvg.TyreWaveWidth }}px;
                                    display: -webkit-box;
                                    -webkit-box-pack: center;
                                  "
                                >
                                  <div
                                    style="
                                      color: black;
                                      font-size: 10px;
                                      font-weight: 400;
                                      line-height: 18px;
                                    "
                                  >
                                    {{ if $.TyreWavePlaceholderSvg.ShowOTD }}
                                    OTD: {{ $.TyreWavePlaceholderSvg.OTDLabel }}
                                    {{ end }}
                                  </div>
                                </div>
                          
                                <div class="base-tread" style="width: 100%;">
                                  {{ $.TyreWavePlaceholderSvg.BaseTread }}
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <!-- END OF TREAD WAVE SECTION -->
                        <!-- Average RTD -->
                        <div>
                          <p class="text-xs text-center">
                            <span class="text-grey-50">Rata-rata&nbsp:&nbsp</span>
                            <span class="text-black-70">
                              -
                            </span>
                          </p>
                        </div>
                      </div>
                    </div>
                    <div class="tyre-card-info__tyre-pressure-section" style="padding-left: 14px;">
                      <div class="tyre-card-info__tyre-pressure-section-container-disable">
                        <img src="{{ $.PressureDisabledImg }}" alt="Pressure Icon" class="tyre-pressure-section-container__pressure-icon">
                        <p class="text-xs text-black-70 font-semibold text-center text-lh-1">-</p>
                        <p class="text-xs text-black-70 text-center text-lh-1">psi</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="spare-tyre-card-info__row">
                <div class="spare-tyre-card-info__col-attachment">
                  <p class="text-xs text-grey-50 text-left">Gambar</p>
                  <p class="text-xs text-black-70 text-left">-</p>
                </div>
                <div class="spare-tyre-card-info__col-remark">
                  <p class="text-xs text-grey-50 text-left">Catatan</p>
                  <div class="overflow-elipsis-spare-tyre-remark-container">
                    <p class="text-xs text-black-70 text-left">-</p>
                  </div>
                </div>
              </div>
              <div class="spare-tyre-card-info__row">
                <div class="spare-tyre-card-info__col-findings">
                  <p class="text-xs text-grey-50 text-left">Temuan / Kondisi Ban</p>
                  <p class="text-xs text-black-70 text-left">-</p>
                </div>
              </div>
            </div>
          </div>
          {{ if eq $.SpareTyreShowAttachmentPos .TyreCount }}
          <div class="detail-container__item detail-container__item-vehicle-attachment-card pagebreak-inside vehicle-attachment-card-margin">
            <div class="detail-container__item-vehicle-attachment-header">
              <p class="text-xs text-left font-semibold text-black-70">Gambar Inspeksi Kendaraan</p>
            </div>
            <div class="detail-container__item-vehicle-attachment-body">
              <div>
                <div class="vehicle-attachment__display-section">
                  {{ if gt (len $.VehicleAttachments) 0 }}
                    {{ range $.VehicleAttachments }}
                    <div class="vehicle-attachment__display-section-img-container">
                      <img src="{{ .Path }}"
                        alt="Attachment"
                        class="vehicle-attachment__display-section-img"
                      />
                    </div>
                    {{ end }}
                    {{ if eq (len $.VehicleAttachments) 1 }}
                    <div class="vehicle-attachment__display-section-img-container"></div>
                    {{ end }}
                    {{ if eq (len $.VehicleAttachments) 2 }}
                    <div class="vehicle-attachment__display-section-img-container"></div>
                    {{ end }}
                  {{ end }}
                  {{ if eq (len $.VehicleAttachments) 0 }}
                    <p class="text-xs text-black-70 text-left">-</p>
                  {{ end }}
                </div>
                <div class="vehicle-attachment__remark-section" style="margin-top: 8px;">
                  <p class="text-xs text-grey-50 text-left">Catatan Inspeksi Kendaraan</p>
                  <p class="text-xs text-black-70 text-left">
                    {{ if ne $.AssetInspection.Remark "" }}
                      {{ $.AssetInspection.Remark }}
                    {{ else }}
                      -
                    {{ end }}
                  </p>
                </div>
              </div>
            </div>
          </div>
          {{ end }}
        </div>
        {{ end }}
        {{ end }}
      </div>
      {{ end }}
      {{ if not .HasSpareTyre }}
      <div class="detail-container__item__row">
        <div class="detail-container__item-tyre-card-cell text-center">
          <div class="detail-container__item detail-container__item-vehicle-attachment-card pagebreak-inside">
            <div class="detail-container__item-vehicle-attachment-header">
              <p class="text-xs text-left font-semibold text-black-70">Gambar Inspeksi Kendaraan</p>
            </div>
            <div class="detail-container__item-vehicle-attachment-body">
              <div>
                <div class="vehicle-attachment__display-section">
                  {{ if gt (len $.VehicleAttachments) 0 }}
                    {{ range $.VehicleAttachments }}
                    <div class="vehicle-attachment__display-section-img-container">
                      <img src="{{ .Path }}"
                        alt="Attachment"
                        class="vehicle-attachment__display-section-img"
                      />
                    </div>
                    {{ end }}
                    {{ if eq (len $.VehicleAttachments) 1 }}
                    <div class="vehicle-attachment__display-section-img-container"></div>
                    {{ end }}
                    {{ if eq (len $.VehicleAttachments) 2 }}
                    <div class="vehicle-attachment__display-section-img-container"></div>
                    {{ end }}
                  {{ end }}
                  {{ if eq (len $.VehicleAttachments) 0 }}
                    <p class="text-xs text-black-70 text-left">-</p>
                  {{ end }}
                </div>
                <div class="vehicle-attachment__remark-section" style="margin-top: 8px;">
                  <p class="text-xs text-grey-50 text-left">Catatan Inspeksi Kendaraan</p>
                  <p class="text-xs text-black-70 text-left">
                    {{ if ne $.AssetInspection.Remark "" }}
                      {{ $.AssetInspection.Remark }}
                    {{ else }}
                      -
                    {{ end }}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      {{ end }}
      <!-- START OF INSPEKSI LANJUTAN -->
      {{ if .ShowFollowupInspectionSection }}
      <div class="bg-primary-10 px-2 py-2 mt-6-px border-black-30">
        <p class="text-sm font-semibold text-black-70">Inspeksi Lanjutan</p>
      </div>
      <div class="inspection-result__row">
        <div class="border-black-30 inspection-result__col">
          {{ if .FailedVisualChecking }}
          <img src="{{ .CheckImg }}" alt="Check" class="inspection-result__col-mark">
          {{ else }}
          <img src="{{ .CrossImg }}" alt="Cross" class="inspection-result__col-mark">
          {{ end }}
          <p class="text-xs text-black-70 inspection-result__col-label">Ditemukan Bocor/Lubang?</p>
        </div>
        <div class="border-black-30 inspection-result__col">
          {{ if .TireTreadAndRimDamage }}
          <img src="{{ .CheckImg }}" alt="Check" class="inspection-result__col-mark">
          {{ else }}
          <img src="{{ .CrossImg }}" alt="Cross" class="inspection-result__col-mark">
          {{ end }}
          <p class="text-xs text-black-70 inspection-result__col-label">Kerusakan Tapak & Velg Ban</p>
        </div>
        <div class="border-black-30 inspection-result__col">
          {{ if .RequireRotationTyre }}
          <img src="{{ .CheckImg }}" alt="Check" class="inspection-result__col-mark">
          {{ else }}
          <img src="{{ .CrossImg }}" alt="Cross" class="inspection-result__col-mark">
          {{ end }}
          <p class="text-xs text-black-70 inspection-result__col-label">Dibutuhkan Rotasi Ban</p>
        </div>
        <div class="border-black-30 inspection-result__col">
          {{ if .RequireSpooringVehicle }}
          <img src="{{ .CheckImg }}" alt="Check" class="inspection-result__col-mark">
          {{ else }}
          <img src="{{ .CrossImg }}" alt="Cross" class="inspection-result__col-mark">
          {{ end }}
          <p class="text-xs text-black-70 inspection-result__col-label">Dibutuhkan Spooring</p>
        </div>
      </div>
      {{ end }}
      <!-- END OF INSPEKSI LANJUTAN -->
    </div>
    <!-- END OF BODY -->
  </body>
</html>
