package dtos

import (
	"assetfindr/internal/app/log/models"
	userModel "assetfindr/internal/app/user-identity/models"
	"assetfindr/pkg/common/commonmodel"
	"time"

	"github.com/jackc/pgtype"
	"gopkg.in/guregu/null.v4"
)

type LogListReq struct {
	IDs []string `form:"id"`
}

type GetLogsReq struct {
	commonmodel.ListCursorRequest
	ReferenceCode string `form:"reference_code"`
	ReferenceID   string `form:"reference_id"`
	StartDate     string `form:"start_date"`
	EndDate       string `form:"end_date"`
	CategoryCode  string `form:"category_code"`
	ActionBy      string `form:"action_by"`
}

type LogV2 struct {
	LogID               string       `json:"log_id"`
	TableNames          string       `json:"table_name"`
	PreviousValue       pgtype.JSONB `json:"previous_value"`
	NewValue            pgtype.JSONB `json:"new_value"`
	ParsedPreviousValue pgtype.JSONB `json:"parsed_previous_value"`
	ParsedNewValue      pgtype.JSONB `json:"parsed_new_value"`
	IsParsed            bool         `json:"is_parsed"`
	ParsedAt            null.Time    `json:"parsed_at"`
	ClientID            string       `json:"client_id"`
	LogCreatedAt        time.Time    `json:"log_created_at"`
	LogCreatedBy        string       `json:"log_created_by"`
	LogUsername         string       `json:"log_username"`
	CategoryCode        string       `json:"category_code"`
	CategoryLabel       string       `json:"category_label"`
	RefID               string       `json:"ref_id"`
	ReferenceCode       string       `json:"reference_code"`
	ReferenceID         string       `json:"reference_id"`
	RefCreatedAt        time.Time    `json:"ref_created_at"`
	RefCreatedBy        string       `json:"ref_created_by"`
	RefUsername         string       `json:"ref_username"`
}

func BuildGetLogsV2Response(
	logs []models.LogV2,
	mapsUserIds map[string]userModel.User,
) []LogV2 {
	response := make([]LogV2, 0, len(logs))

	for _, log := range logs {
		username := ""
		if log.CreatedBy != "" {
			username = mapsUserIds[log.CreatedBy].GetName()
		}

		if log.PreviousValue.Status == pgtype.Undefined {
			log.PreviousValue.Status = pgtype.Null
		}

		if log.NewValue.Status == pgtype.Undefined {
			log.NewValue.Status = pgtype.Null
		}

		if log.ParsedNewValue.Status == pgtype.Undefined {
			log.ParsedNewValue.Status = pgtype.Null
		}

		if log.ParsedPreviousValue.Status == pgtype.Undefined {
			log.ParsedPreviousValue.Status = pgtype.Null
		}

		for _, ref := range log.LogReferenceV2s {
			refUsername := ""
			if ref.CreatedBy != "" {
				refUsername = mapsUserIds[ref.CreatedBy].GetName()
			}

			response = append(response, LogV2{
				LogID:               log.ID,
				TableNames:          log.TableNames,
				PreviousValue:       log.PreviousValue,
				NewValue:            log.NewValue,
				ParsedPreviousValue: log.ParsedPreviousValue,
				ParsedNewValue:      log.ParsedNewValue,
				IsParsed:            log.IsParsed,
				ParsedAt:            log.ParsedAt,
				ClientID:            log.ClientID,
				LogCreatedAt:        log.CreatedAt,
				LogCreatedBy:        log.CreatedBy,
				LogUsername:         username,
				CategoryCode:        log.CategoryCode,
				CategoryLabel:       log.LogCategory.Label,
				RefID:               ref.ID,
				ReferenceCode:       ref.ReferenceCode,
				ReferenceID:         ref.ReferenceID,
				RefCreatedAt:        ref.CreatedAt,
				RefCreatedBy:        ref.CreatedBy,
				RefUsername:         refUsername,
			})
		}
	}

	return response
}
