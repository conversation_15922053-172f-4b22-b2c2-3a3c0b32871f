package usecase

import (
	assetConstants "assetfindr/internal/app/asset/constants"
	assetModel "assetfindr/internal/app/asset/models"
	"assetfindr/internal/app/log/constants"
	"assetfindr/internal/app/log/dtos"
	"assetfindr/internal/app/log/models"
	userModel "assetfindr/internal/app/user-identity/models"
	"assetfindr/pkg/common/commonlogger"
	"assetfindr/pkg/common/commonmodel"
	"assetfindr/pkg/common/helpers/authhelpers"
	"assetfindr/pkg/common/helpers/timehelpers"
	"context"
	"fmt"
	"time"

	"github.com/jackc/pgtype"
	"gopkg.in/guregu/null.v4"
)

func (uc *LogUseCase) ParseLogs(ctx context.Context) {
	logs, err := uc.logRepo.GetLogV2s(ctx, uc.DB.DB(), models.LogV2Condition{
		Where: models.LogV2Where{
			IsParsed: null.BoolFrom(false),
		},
		Preload: models.LogV2Preload{
			NonAssetLogReferenceV2: true,
		},
	})
	if err != nil {
		return
	}

	for _, log := range logs {
		parser, err := uc.GetParserMap(ctx, &log)
		if err != nil {
			commonlogger.Warnf("error get parser log for log id: %s, err : %v", log.ID, err)
			continue
		}

		// CATEGORY CODE
		categoryCode, err := uc.GetCategoryCodeMap(ctx, &log)
		if err != nil {
			commonlogger.Warnf("error get log category code for log id: %s, err : %v", log.ID, err)
			continue
		}
		log.CategoryCode = categoryCode

		prevData, newData, err := uc.ParseLog(ctx, &log, parser)
		if err != nil {
			commonlogger.Warnf("error parse log for log id: %s, err : %v", log.ID, err)
			continue
		}

		log.ParsedPreviousValue.Set(&prevData)
		log.ParsedNewValue.Set(&newData)
		err = uc.logRepo.ParseLog(ctx, uc.DB.DB(), &log)
		if err != nil {
			commonlogger.Warnf("error insert parsed log for log id: %s, err : %v", log.ID, err)
			continue
		}
	}
}

func (uc *LogUseCase) GetParserMap(ctx context.Context, log *models.LogV2) (map[string]logParser, error) {
	switch log.TableNames {
	case constants.TABLE_NAME_ASSET:
		return AssetParser, nil
	case constants.TABLE_NAME_ASSET_TRANSACTIONS:
		if len(log.LogReferenceV2s) == 0 {
			return nil, fmt.Errorf("asset transaction have no reference")
		}

		var assetTransactionID string
		for _, ref := range log.LogReferenceV2s {
			if ref.ReferenceCode == constants.ASSET_TRANSACTION_LOG_REFERENCE_CODE {
				assetTransactionID = ref.ReferenceID
			}
		}
		assetTransaction, err := uc.assetTransactionRepo.GetAssetTransaction(ctx, uc.DB.DB(), assetModel.AssetTransactionCondition{
			Where: assetModel.AssetTransactionWhere{
				ID: assetTransactionID,
			},
		})
		if err != nil {
			return nil, err
		}

		switch assetTransaction.TypeCode {
		case assetConstants.ASSET_TRANSACTION_TYPE_PURCHASE:
			return AssetTransactionPurchaseParser, nil
		case assetConstants.ASSET_TRANSACTION_TYPE_RENT:
			return AssetTransactionRentParser, nil
		case assetConstants.ASSET_TRANSACTION_TYPE_WARRANTY:
			return AssetTransactionWarrantyParser, nil
		case assetConstants.ASSET_TRANSACTION_TYPE_INSURANCE:
			return AssetTransactionInsuranceParser, nil
		case assetConstants.ASSET_TRANSACTION_TYPE_EXPENSE:
			return AssetTransactionExpenseParser, nil
		}

		return nil, fmt.Errorf("parser map for asset transaction type: %s not found", assetTransaction.TypeCode)
	case constants.TABLE_NAME_ASSET_COMPONENTS:
		return AssetComponent, nil
	case constants.TABLE_NAME_ASSET_TYRE:
		return AssetTyre, nil
	case constants.TABLE_NAME_ASSET_VEHICLE:
		return AssetVehicle, nil
	case constants.TABLE_NAME_ASSET_TYRES_TREAD:
		return Retread, nil
	default:
		commonlogger.Warnf("not found parser for log id: %s, table: %s", log.ID, log.TableNames)
		return nil, fmt.Errorf("not found map paser for table: %s", log.TableNames)
	}
}

func (uc *LogUseCase) GetCategoryCodeMap(ctx context.Context, log *models.LogV2) (string, error) {
	switch log.TableNames {
	case constants.TABLE_NAME_ASSET:
		return constants.ASSET_DETAIL_LOG_CATEGORY_CODE, nil
	case constants.TABLE_NAME_ASSET_TRANSACTIONS:
		if len(log.LogReferenceV2s) == 0 {
			return "", fmt.Errorf("asset transaction have no reference")
		}

		var assetTransactionID string
		for _, ref := range log.LogReferenceV2s {
			if ref.ReferenceCode == constants.ASSET_TRANSACTION_LOG_REFERENCE_CODE {
				assetTransactionID = ref.ReferenceID
			}
		}
		assetTransaction, err := uc.assetTransactionRepo.GetAssetTransaction(ctx, uc.DB.DB(), assetModel.AssetTransactionCondition{
			Where: assetModel.AssetTransactionWhere{
				ID: assetTransactionID,
			},
		})
		if err != nil {
			return "", err
		}

		switch assetTransaction.TypeCode {
		case assetConstants.ASSET_TRANSACTION_TYPE_PURCHASE:
			return constants.PURCHASE_INFORMATION_LOG_CATEGORY_CODE, nil
		case assetConstants.ASSET_TRANSACTION_TYPE_RENT:
			return constants.RENT_INFORMATION_LOG_CATEGORY_CODE, nil
		case assetConstants.ASSET_TRANSACTION_TYPE_WARRANTY:
			return constants.WARRANTY_INFORMATION_LOG_CATEGORY_CODE, nil
		case assetConstants.ASSET_TRANSACTION_TYPE_INSURANCE:
			return constants.INSURANCE_INFORMATION_LOG_CATEGORY_CODE, nil
		case assetConstants.ASSET_TRANSACTION_TYPE_EXPENSE:
			return constants.EXPENSE_INFORMATION_LOG_CATEGORY_CODE, nil
		}

		return "", fmt.Errorf("category code map for asset transaction type: %s not found", assetTransaction.TypeCode)
	case constants.TABLE_NAME_ASSET_COMPONENTS:
		return constants.ASSET_COMPONENTS_LOG_CATEGORY_CODE, nil
	case constants.TABLE_NAME_ASSET_TYRE:
		return constants.ASSET_DETAIL_LOG_CATEGORY_CODE, nil
	case constants.TABLE_NAME_ASSET_VEHICLE:
		return constants.ASSET_DETAIL_LOG_CATEGORY_CODE, nil
	case constants.TABLE_NAME_ASSET_TYRES_TREAD:
		return constants.RETREAD_LOG_CATEGORY_CODE, nil
	default:
		return "", fmt.Errorf("not found category code for table: %s", log.TableNames)
	}
}

func (uc *LogUseCase) GetLogV2s(ctx context.Context, req dtos.GetLogsReq) (*commonmodel.ListCursorResponse, error) {
	if req.ReferenceCode == "" || req.ReferenceID == "" {
		return &commonmodel.ListCursorResponse{
			PageSize:   req.PageSize,
			NextCursor: "",
			Data:       []dtos.LogReference{},
		}, nil
	}

	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	startDateTime, _ := timehelpers.ParseDateFilter(req.StartDate, false)
	endDateTime, _ := timehelpers.ParseDateFilter(req.EndDate, true)

	logs, err := uc.logRepo.GetLogV2List(ctx, uc.DB.DB(),
		models.GetLogV2ListParam{
			ListCursorRequest: req.ListCursorRequest,
			Cond: models.LogV2Condition{
				Where: models.LogV2Where{
					StartDate:     startDateTime,
					EndDate:       endDateTime,
					CategoryCode:  req.CategoryCode,
					ActionBy:      req.ActionBy,
					ReferenceCode: req.ReferenceCode,
					ReferenceID:   req.ReferenceID,
					ClientID:      claim.GetLoggedInClientID(),
				},
				Columns: []string{},
				Preload: models.LogV2Preload{
					LogReferenceV2WithRefCodeAndID: true,
					LogCategory:                    true,
				},
			},
		})
	if err != nil {
		return nil, err
	}

	var nextCursor string
	mapsUserIds := map[string]userModel.User{}
	if len(logs) > 0 {
		t := logs[len(logs)-1].CreatedAt.Format(time.RFC3339Nano)
		nextCursor = t

		userIds := []string{}
		for _, log := range logs {
			userIds = append(userIds, log.CreatedBy)
		}

		users := []userModel.User{}
		err = uc.userRepo.GetUsersByIds(ctx, uc.DB.DB(), &users, userIds)
		if err != nil {
			return nil, err
		}
		for _, val := range users {
			mapsUserIds[val.ID] = val
		}
	}

	resp := dtos.BuildGetLogsV2Response(logs, mapsUserIds)

	return &commonmodel.ListCursorResponse{
		PageSize:   req.PageSize,
		NextCursor: nextCursor,
		Data:       resp,
	}, nil
}

func (uc *LogUseCase) GetLogReferenceV2s(ctx context.Context, req dtos.GetLogReferencesReq) (*commonmodel.ListCursorResponse, error) {
	if req.ReferenceCode == "" {
		return &commonmodel.ListCursorResponse{
			PageSize:   req.PageSize,
			NextCursor: "",
			Data:       []dtos.LogReference{},
		}, nil
	}

	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	logRefs, err := uc.logRepo.GetLogReferenceV2List(ctx, uc.DB.DB(),
		models.GetLogReferenceV2ListParam{
			ListCursorRequest: req.ListCursorRequest,
			Cond: models.LogReferenceV2Condition{
				Where: models.LogReferenceV2Where{
					ReferenceCode:      req.ReferenceCode,
					ReferenceID:        req.ReferenceID,
					ClientID:           claim.GetLoggedInClientID(),
					OnlyParsedLog:      true,
					HideBlankParsedLog: true,
				},
				Columns:     []string{},
				IsForUpdate: false,
				Preload: models.LogReferenceV2Preload{
					Log: true,
				},
			},
		})
	if err != nil {
		return nil, err
	}

	var nextCursor string
	mapsUserIds := map[string]userModel.User{}
	if len(logRefs) > 0 {
		t := logRefs[len(logRefs)-1].CreatedAt.Format(time.RFC3339Nano)
		nextCursor = t

		userIds := []string{}
		for _, logRef := range logRefs {
			userIds = append(userIds, logRef.CreatedBy)
		}

		users := []userModel.User{}
		err = uc.userRepo.GetUsersByIds(ctx, uc.DB.DB(), &users, userIds)
		if err != nil {
			return nil, err
		}
		for _, val := range users {
			mapsUserIds[val.ID] = val
		}
	}

	resp := make([]dtos.LogReference, 0, len(logRefs))
	for _, logRef := range logRefs {
		username := ""
		if logRef.CreatedBy != "" {
			username = mapsUserIds[logRef.CreatedBy].GetName()
		}

		if logRef.Log.PreviousValue.Status == pgtype.Undefined {
			logRef.Log.PreviousValue.Status = pgtype.Null
		}

		if logRef.Log.NewValue.Status == pgtype.Undefined {
			logRef.Log.NewValue.Status = pgtype.Null
		}

		if logRef.Log.ParsedNewValue.Status == pgtype.Undefined {
			logRef.Log.ParsedNewValue.Status = pgtype.Null
		}

		if logRef.Log.ParsedPreviousValue.Status == pgtype.Undefined {
			logRef.Log.ParsedPreviousValue.Status = pgtype.Null
		}

		resp = append(resp, dtos.LogReference{
			ID:            logRef.ID,
			LogID:         logRef.LogID,
			ReferenceCode: logRef.ReferenceCode,
			ReferenceID:   logRef.ReferenceID,
			ClientID:      logRef.ClientID,
			CreatedAt:     logRef.CreatedAt,
			CreatedBy:     logRef.CreatedBy,
			UserName:      username,
			Log:           logRef.Log,
		})
	}

	return &commonmodel.ListCursorResponse{
		PageSize:   req.PageSize,
		NextCursor: nextCursor,
		Data:       resp,
	}, nil
}
