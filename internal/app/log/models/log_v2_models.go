package models

import (
	"assetfindr/pkg/common/commonmodel"
	"time"

	"github.com/jackc/pgtype"
	"gopkg.in/guregu/null.v4"
)

// LogV2 represents the log_logs table
type LogV2 struct {
	ID                  string           `gorm:"primaryKey" json:"id"`
	TableNames          string           `gorm:"column:table_name" json:"table_name"`
	PreviousValue       pgtype.JSONB     `gorm:"type:jsonb" json:"previous_value"`
	NewValue            pgtype.JSONB     `gorm:"type:jsonb" json:"new_value"`
	ParsedPreviousValue pgtype.JSONB     `gorm:"type:jsonb" json:"parsed_previous_value"`
	ParsedNewValue      pgtype.JSONB     `gorm:"type:jsonb" json:"parsed_new_value"`
	IsParsed            bool             `gorm:"default:false" json:"is_parsed"`
	ParsedAt            null.Time        `json:"parsed_at"`
	ClientID            string           `json:"client_id"`
	CreatedAt           time.Time        `json:"created_at"`
	CreatedBy           string           `json:"created_by"`
	LogReferenceV2s     []LogReferenceV2 `json:"references,omitempty" gorm:"foreignKey:LogID;references:ID"`
	CategoryCode        string           `json:"category_code" gorm:"default:null"`
	LogCategory         LogCategory      `json:"log_category,omitempty" gorm:"foreignKey:CategoryCode;references:Code"`
}

func (LogV2) TableName() string {
	return "log_logs"
}

// LogReferenceV2 represents the log_log_references table
type LogReferenceV2 struct {
	ID            string    `gorm:"primaryKey" json:"id"`
	LogID         string    `json:"log_id"`
	ReferenceCode string    `json:"reference_code"`
	ReferenceID   string    `json:"reference_id"`
	ClientID      string    `json:"client_id"`
	CreatedAt     time.Time `json:"created_at"`
	CreatedBy     string    `json:"created_by,omitempty"`
	Log           LogV2     `json:"log" gorm:"foreignKey:LogID;references:ID"`
}

func (LogReferenceV2) TableName() string {
	return "log_log_references"
}

type LogV2Where struct {
	ID            string
	ClientID      string
	IsParsed      null.Bool
	ReferenceCode string
	ReferenceID   string
	StartDate     time.Time
	EndDate       time.Time
	CategoryCode  string
	ActionBy      string
}

type LogV2Preload struct {
	NonAssetLogReferenceV2         bool
	LogReferenceV2                 bool
	LogCategory                    bool
	LogReferenceV2WithRefCodeAndID bool
}

type LogV2Condition struct {
	Where   LogV2Where
	Preload LogV2Preload
	Columns []string
}

type GetLogV2ListParam struct {
	commonmodel.ListCursorRequest
	Cond LogV2Condition
}

type LogReferenceV2Where struct {
	ID                 string
	ReferenceCode      string
	ReferenceID        string
	ClientID           string
	OnlyParsedLog      bool
	HideBlankParsedLog bool
}

type LogReferenceV2Preload struct {
	Log bool
}

type LogReferenceV2Condition struct {
	Where       LogReferenceV2Where
	Preload     LogReferenceV2Preload
	Columns     []string
	IsForUpdate bool
}

type GetLogReferenceV2ListParam struct {
	commonmodel.ListCursorRequest
	Cond LogReferenceV2Condition
}
