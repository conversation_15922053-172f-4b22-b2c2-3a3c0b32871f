package persistence

import (
	"assetfindr/internal/app/user-identity/constants"
	"assetfindr/internal/app/user-identity/models"
	"assetfindr/internal/app/user-identity/repository"
	"assetfindr/internal/errorhandler"
	"assetfindr/internal/infrastructure/database"
	"assetfindr/pkg/common/commonmodel"
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type permissionRepository struct{}

func NewPermissionRepository() repository.PermissionRepository {
	return &permissionRepository{}
}

func (r *permissionRepository) GetPermissionGroups(ctx context.Context, dB database.DBI, cond models.PermissionGroupCondition) ([]models.PermissionGroup, error) {
	groups := []models.PermissionGroup{}
	query := dB.GetOrm().Model(&groups)

	enrichGroupQueryWithWhere(query, cond.Where)
	enrichGroupQueryWithPreload(query, cond)

	err := query.Find(&groups).Error
	if err != nil {
		return nil, err
	}

	return groups, nil
}

func (r *permissionRepository) GetPermissionCategories(ctx context.Context, dB database.DBI) ([]models.PermissionCategory, error) {
	permissionCategories := []models.PermissionCategory{}
	err := dB.GetOrm().
		Model(&permissionCategories).
		Order("display_sequence ASC").
		Preload("Permissions", func(db *gorm.DB) *gorm.DB {
			return db.Order("display_sequence ASC")
		}).
		Find(&permissionCategories).
		Error
	return permissionCategories, err

}

func enrichCategoryQueryWithWhere(query *gorm.DB, where models.PermissionCategoryWhere) {
	if len(where.ClientPackageCodes) > 0 {
		query.Where("client_package_code IN ?", where.ClientPackageCodes)
	}
}

func enrichCategoryQueryWithPreload(query *gorm.DB, preload models.PermissionCategoryPreload) {
	if preload.Permissions {
		query.Preload("Permissions", func(db *gorm.DB) *gorm.DB {
			return db.Order("display_sequence ASC")
		})
	}
}

func (r *permissionRepository) GetPermissionCategoriesV2(ctx context.Context, dB database.DBI, cond models.PermissionCategoryCondition) ([]models.PermissionCategory, error) {
	permissionCategories := []models.PermissionCategory{}
	query := dB.GetOrm().Model(&permissionCategories)

	enrichCategoryQueryWithWhere(query, cond.Where)
	enrichCategoryQueryWithPreload(query, cond.Preload)

	query.Order("display_sequence ASC")

	err := query.Find(&permissionCategories).Error
	return permissionCategories, err

}

func (r *permissionRepository) CreatePermissionGroup(ctx context.Context, dB database.DBI, group *models.PermissionGroup) error {
	return dB.GetTx().Create(group).Error
}

func (r *permissionRepository) CreatePermissionGroupRights(ctx context.Context, dB database.DBI, rights []models.PermissionGroupRight) error {
	return dB.GetTx().Create(&rights).Error
}

func (r *permissionRepository) UpsertPermissionGroupRights(ctx context.Context, dB database.DBI, rights []models.PermissionGroupRight) error {
	return dB.GetTx().
		Clauses(
			clause.OnConflict{
				Columns: []clause.Column{
					{Name: "permission_group_id"},
					{Name: "permission_category_code"},
				},
				UpdateAll: true,
			}).
		Create(&rights).Error
}

func enrichGroupQueryWithWhere(query *gorm.DB, where models.PermissionGroupWhere) {
	if where.ClientID != "" {
		query.Where("uis_permission_groups.client_id = ?", where.ClientID)
	}

	if where.ID != "" {
		query.Where("uis_permission_groups.id = ?", where.ID)
	}

	if where.NotAdminType {
		query.Where(
			"uis_permission_groups.permission_group_type_code != ?",
			constants.PERMISSION_GROUP_TYPE_ADMIN,
		)
	}

	if where.PermissionGroupTypeCode != "" {
		query.Where(
			"uis_permission_groups.permission_group_type_code = ?",
			where.PermissionGroupTypeCode,
		)
	}
}

func (r *permissionRepository) GetPermissionGroupList(ctx context.Context, dB database.DBI, param models.GetPermissionGroupListParam) (int, []models.PermissionGroup, error) {
	var totalRecords int64
	groups := []models.PermissionGroup{}
	query := dB.GetOrm().Model(&groups)

	enrichGroupQueryWithWhere(query, param.Cond.Where)
	enrichGroupQueryWithPreload(query, param.Cond)

	if param.SearchKeyword != "" {
		query.Where("LOWER(uis_permission_groups.group_name) LIKE LOWER(?)", "%"+param.SearchKeyword+"%")
	}

	err := query.Count(&totalRecords).Error
	if err != nil {
		return 0, nil, err
	}

	if totalRecords <= 0 {
		return int(totalRecords), groups, nil
	}

	offset := (param.PageNo - 1) * param.PageSize
	err = query.Offset(offset).Limit(param.PageSize).Find(&groups).Error
	if err != nil {
		return 0, nil, err
	}

	return int(totalRecords), groups, nil
}

func enrichGroupQueryWithPreload(query *gorm.DB, condition models.PermissionGroupCondition) {
	if condition.Preload.GroupRights {
		query.Preload("GroupRights")
	} // GroupRights

	if condition.Preload.ActiveUserClient {
		query.Preload("ActiveUserClient", func(db *gorm.DB) *gorm.DB {
			return db.Joins(`JOIN uis_users ON 
			uis_users.id = uis_user_clients.user_id 
			AND uis_users.deleted_at IS NULL
			AND uis_users.status_code = ?`, constants.STATUS_ACTIVE)
		})
	} // ActiveUser

}

func (r *permissionRepository) GetPermissionGroup(ctx context.Context, dB database.DBI, cond models.PermissionGroupCondition) (*models.PermissionGroup, error) {
	group := models.PermissionGroup{}
	query := dB.GetOrm().Model(&group)

	enrichGroupQueryWithWhere(query, cond.Where)
	enrichGroupQueryWithPreload(query, cond)

	err := query.First(&group).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errorhandler.ErrDataNotFound("permission group")
		}

		return nil, err
	}

	return &group, nil
}

func (r *permissionRepository) UpdatePermissionGroup(ctx context.Context, dB database.DBI, group *models.PermissionGroup) error {
	return dB.GetTx().Model(&models.PermissionGroup{
		ModelV2: commonmodel.ModelV2{ID: group.ID},
	}).Updates(group).Error
}

func (r *permissionRepository) DeletePermissionGroup(ctx context.Context, dB database.DBI, id string) error {
	return dB.GetTx().Delete(&models.PermissionGroup{}, "id = ?", id).Error
}

func (r *permissionRepository) UpdatePermissionGroupRight(ctx context.Context, dB database.DBI, right *models.PermissionGroupRight) error {
	return dB.GetTx().
		Model(&models.PermissionGroupRight{}).
		Where("permission_group_id = ?", right.PermissionGroupID).
		Where("permission_category_code = ?", right.PermissionCategoryCode).
		Select("IsEnable", "PermissionCodes").
		Updates(right).Error
}
