package repository

import (
	"assetfindr/internal/app/user-identity/models"
	"assetfindr/internal/infrastructure/database"
	"context"
)

type PermissionRepository interface {
	GetPermissionCategories(ctx context.Context, dB database.DBI) ([]models.PermissionCategory, error)
	GetPermissionCategoriesV2(ctx context.Context, dB database.DBI, cond models.PermissionCategoryCondition) ([]models.PermissionCategory, error)
	CreatePermissionGroup(ctx context.Context, dB database.DBI, group *models.PermissionGroup) error
	CreatePermissionGroupRights(ctx context.Context, dB database.DBI, rghts []models.PermissionGroupRight) error
	UpsertPermissionGroupRights(ctx context.Context, dB database.DBI, rghts []models.PermissionGroupRight) error
	GetPermissionGroupList(ctx context.Context, dB database.DBI, param models.GetPermissionGroupListParam) (int, []models.PermissionGroup, error)
	GetPermissionGroup(ctx context.Context, dB database.DBI, cond models.PermissionGroupCondition) (*models.PermissionGroup, error)
	GetPermissionGroups(ctx context.Context, dB database.DBI, cond models.PermissionGroupCondition) ([]models.PermissionGroup, error)
	UpdatePermissionGroup(ctx context.Context, dB database.DBI, group *models.PermissionGroup) error
	DeletePermissionGroup(ctx context.Context, dB database.DBI, id string) error
	UpdatePermissionGroupRight(ctx context.Context, dB database.DBI, rights *models.PermissionGroupRight) error
}
