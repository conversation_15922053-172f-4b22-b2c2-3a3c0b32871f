package usecase

import (
	assetConstant "assetfindr/internal/app/asset/constants"
	assetModel "assetfindr/internal/app/asset/models"
	assetRepository "assetfindr/internal/app/asset/repository"

	storageUseCase "assetfindr/internal/app/storage/usecase"
	"assetfindr/internal/app/user-identity/constants"
	internalConstants "assetfindr/internal/constants"
	"strings"

	notifConstants "assetfindr/internal/app/notification/constants"
	notificationDtos "assetfindr/internal/app/notification/dtos"
	notificationUsecase "assetfindr/internal/app/notification/usecase"
	"time"

	financeUseCase "assetfindr/internal/app/finance/usecase"
	"assetfindr/internal/app/user-identity/dtos"
	"assetfindr/internal/app/user-identity/models"

	"assetfindr/internal/app/user-identity/repository"
	"assetfindr/internal/errorhandler"
	"assetfindr/internal/infrastructure/database"
	"assetfindr/internal/infrastructure/firebaseApp"
	"assetfindr/pkg/common/commonlogger"
	"assetfindr/pkg/common/commonmodel"
	"assetfindr/pkg/common/helpers"
	"assetfindr/pkg/common/helpers/authhelpers"
	"assetfindr/pkg/common/helpers/contexthelpers"
	"assetfindr/pkg/common/helpers/sqlhelpers"
	"assetfindr/pkg/common/helpers/tmplhelpers"
	"context"
	"errors"

	"firebase.google.com/go/v4/auth"
	"github.com/dgrijalva/jwt-go"
	"github.com/spf13/viper"
	"gopkg.in/guregu/null.v4"
	"gorm.io/gorm"
)

type UserUseCase struct {
	DB                         database.DBUsecase
	UserRepository             repository.UserRepository
	PermissionRepository       repository.PermissionRepository
	financeUseCase             financeUseCase.FinanceUseCase
	attachmentUseCase          *storageUseCase.AttachmentUseCase
	permissionUseCase          *PermissionUseCase
	notifUseCase               *notificationUsecase.NotificationUseCase
	clientRepo                 repository.ClientRepository
	departmentRepo             repository.DepartmentRepository
	locationRepo               assetRepository.LocationRepository
	customAssetCategoryRepo    assetRepository.CustomAssetCategoryRepository
	assetInspectionFindingRepo assetRepository.AssetInspectionFindingRepository
	targetTyreRemovalRepo      assetRepository.VehicleTargetTyreRemovalRepository
}

func NewUserUseCase(
	DB database.DBUsecase,
	userRepo repository.UserRepository,
	permissionRepo repository.PermissionRepository,
	attachmentUseCase *storageUseCase.AttachmentUseCase,
	financeUseCase financeUseCase.FinanceUseCase,
	clientRepo repository.ClientRepository,
	departmentRepo repository.DepartmentRepository,
	locationRepo assetRepository.LocationRepository,
	customAssetCategoryRepo assetRepository.CustomAssetCategoryRepository,
	assetInspectionFindingRepo assetRepository.AssetInspectionFindingRepository,
	targetTyreRemovalRepo assetRepository.VehicleTargetTyreRemovalRepository,
) *UserUseCase {
	return &UserUseCase{
		DB:                         DB,
		UserRepository:             userRepo,
		PermissionRepository:       permissionRepo,
		attachmentUseCase:          attachmentUseCase,
		financeUseCase:             financeUseCase,
		clientRepo:                 clientRepo,
		departmentRepo:             departmentRepo,
		locationRepo:               locationRepo,
		customAssetCategoryRepo:    customAssetCategoryRepo,
		assetInspectionFindingRepo: assetInspectionFindingRepo,
		targetTyreRemovalRepo:      targetTyreRemovalRepo,
	}
}

func (uc *UserUseCase) SetNotifUseCase(notifUseCase *notificationUsecase.NotificationUseCase) {
	uc.notifUseCase = notifUseCase
}

func (uc *UserUseCase) SetPermissionUseCase(permissionUseCase *PermissionUseCase) {
	uc.permissionUseCase = permissionUseCase
}

func (uc *UserUseCase) UserRegister(ctx context.Context, requestData *dtos.UserRegisterRequest) (dtos.UserAuthResponse, error) {
	var response dtos.UserAuthResponse

	var user models.User
	err := uc.UserRepository.GetUserByField(ctx, uc.DB.DB(), &user, constants.EMAIL_FIELD_NAME, requestData.Email)
	// expected error as if not error then email is already registered
	if err == nil && user.IsActive() {
		return response, errorhandler.ErrUserAlreadyRegistered
	}

	if err != gorm.ErrRecordNotFound {
		commonlogger.Errorf("Error registering user to firebase: %v\n", err)
		return response, err
	}

	err = nil

	// Begin db transaction
	tX, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return response, err
	}

	defer func() {
		tX.Rollback()
	}()

	requestData.ClientAlias = strings.ToLower(requestData.ClientAlias)
	client := models.Client{
		Name:           requestData.CompanyName,
		IsParentClient: true,
		BusinessSector: requestData.BusinessSector,
		ReferralSource: requestData.ReferralSource,
		StatusCode:     constants.STATUS_PENDING,
		ClientAlias:    requestData.ClientAlias,
	}
	err = uc.UserRepository.CreateClient(ctx, tX.DB(), &client)
	if err != nil {
		if strings.Contains(err.Error(), "duplicate key value violates unique constraint") {
			return response, errorhandler.ErrDataAlreadyExist("client with sub domain " + requestData.ClientAlias)
		}

		commonlogger.Errorf("Error create client to database: %v\n", err)
		return response, err
	}

	params := (&auth.UserToCreate{}).
		Email(requestData.Email).
		Password(requestData.Password)

	firebaseUserRecord, err := firebaseApp.GetAuthClient().CreateUser(context.Background(), params)
	if err != nil {
		if strings.Contains(err.Error(), "EMAIL_EXISTS") {
			return response, errorhandler.ErrDataAlreadyExist("email " + requestData.Email)
		}

		commonlogger.Errorf("Error registering user to firebase: %v\n", err)
		return response, errors.New("Failed registering user, please try again later")
	}

	user = models.User{
		FirebaseUserID: firebaseUserRecord.UID,
		FirstName:      requestData.FirstName,
		LastName:       requestData.LastName,
		Email:          requestData.Email,
		UserRoleCode:   constants.USER_DEFAULT_USER_ROLE,
		StatusCode:     constants.STATUS_ACTIVE,
		PhoneNumber:    requestData.PhoneNumber,
	}

	err = uc.UserRepository.CreateUser(ctx, tX.DB(), &user)
	if err != nil {
		commonlogger.Errorf("Error registering user to database: %v\n", err)
		return response, errors.New("Failed registering user, please try again later")
	}

	categories, err := uc.PermissionRepository.GetPermissionCategories(ctx, uc.DB.DB())
	if err != nil {
		return response, err
	}

	group := &models.PermissionGroup{
		GroupName:               "Admin",
		Description:             "For Admin (Full Access)",
		PermissionGroupTypeCode: constants.PERMISSION_GROUP_TYPE_ADMIN,
	}
	group.ClientID = client.ID
	group.CreatedBy = user.ID
	err = uc.PermissionRepository.CreatePermissionGroup(ctx, tX.DB(), group)
	if err != nil {
		return response, err
	}

	rights := make([]models.PermissionGroupRight, 0, len(categories))
	for _, category := range categories {
		permissionCodes := make([]string, 0, len(category.Permissions))
		for _, permission := range category.Permissions {
			permissionCodes = append(permissionCodes, permission.Code)
		}
		right := models.PermissionGroupRight{
			PermissionGroupID:      group.ID,
			PermissionCategoryCode: category.Code,
			PermissionCodes:        permissionCodes,
			IsEnable:               true,
		}
		right.ClientID = client.ID
		right.CreatedBy = user.ID

		rights = append(rights, right)
	}

	err = uc.PermissionRepository.CreatePermissionGroupRights(ctx, tX.DB(), rights)
	if err != nil {
		return response, err
	}

	userClient := models.UserClient{
		UserID:            user.ID,
		ClientID:          client.ID,
		PermissionGroupID: group.ID,
	}
	err = uc.UserRepository.CreateUserClient(ctx, tX.DB(), &userClient)
	if err != nil {
		commonlogger.Errorf("Error create user client to database: %v\n", err)
		return response, errors.New("Failed registering user, please try again later")
	}

	accounts, err := uc.financeUseCase.CreateDefaultAccounts(ctx, tX.DB(), client.ID)
	if err != nil {
		return response, err
	}

	err = uc.financeUseCase.CreateDefaultAccountTransactions(ctx, tX.DB(), accounts, client.ID)
	if err != nil {
		return response, err
	}

	department := &models.Department{
		ModelV2:        commonmodel.ModelV2{},
		Name:           "General",
		DepartmentCode: "GEN",
		ContactUserID:  null.StringFrom(user.ID),
		StatusCode:     "ACTIVE",
	}
	department.ClientID = client.ID
	department.CreatedBy = user.ID
	department.UpdatedBy = user.ID
	err = uc.departmentRepo.CreateDepartment(ctx, tX.DB(), department)
	if err != nil {
		return response, err
	}

	loc := &assetModel.Location{
		Name:                "General",
		IsInventoryLocation: sqlhelpers.NullBool(true),
		Address:             "General Location Address",
		Floor:               sqlhelpers.NullString(""),
		Unit:                sqlhelpers.NullString(""),
		StatusCode:          "ACTIVE",
		Description:         sqlhelpers.NullString(""),
		PICUserID:           user.ID,
		MapLat:              0,
		MapLong:             0,
	}
	loc.ClientID = client.ID
	loc.CreatedBy = user.ID
	loc.UpdatedBy = user.ID
	err = uc.locationRepo.CreateLocation(ctx, tX.DB(), loc)
	if err != nil {
		return response, err
	}

	err = uc.customAssetCategoryRepo.CreateCustomAssetCategory(ctx, tX.DB(), &assetModel.CustomAssetCategory{
		ModelV2: commonmodel.ModelV2{
			CreatedBy: user.ID,
			UpdatedBy: user.ID,
			ClientID:  client.ID,
		},
		Name:              "Tyre",
		AssetCategoryCode: assetConstant.ASSET_CATEGORY_TYRE_CODE,
		Description:       "Tyre Default Category",
	})
	if err != nil {
		return response, err
	}

	// INSERT DEFAULT FINDING DATA
	err = uc.assetInspectionFindingRepo.CreateDefaultFindings(ctx, tX.DB(), client.ID, user.ID)
	if err != nil {
		return response, err
	}

	// Commit db transaction
	err = tX.Commit()
	if err != nil {
		return response, err
	}

	group.GroupRights = rights
	permissionGroupDetail := constructPersmissionGroupResp(group)

	tokenParam := tokenClaimParam{
		user:             user,
		loggedInClientID: userClient.ClientID,
		loggedInClient:   client,
		clients:          []models.UserClient{userClient},
		permissionGroup:  permissionGroupDetail,
	}
	jwtSignedToken, err := GenerateJWTSignedToken(tokenParam)
	if err != nil {
		commonlogger.Errorf("Error in generating jwt token for registered user: %v\n", err)
		return response, err
	}

	clients := make([]models.Client, 0)
	newClient := dtos.Client{}
	newClient.Set(client)
	clients = append(clients, client)
	UserResponse := dtos.UserLoginResponse{}
	UserResponse.Set(user, clients)

	response = dtos.UserAuthResponse{
		JwtToken:        jwtSignedToken,
		PermissionGroup: permissionGroupDetail,
		User:            UserResponse,
	}

	defaultTargetRemoval := &assetModel.VehicleTargetTyreRemoval{
		ModelV2: commonmodel.ModelV2{
			CreatedBy: user.ID,
			UpdatedBy: user.ID,
			ClientID:  client.ID,
		},
		TargetRTD: 0,
	}

	err = uc.targetTyreRemovalRepo.CreateVehicleTargetTyreRemoval(ctx, uc.DB.DB(), defaultTargetRemoval)
	if err != nil {
		return response, err
	}

	// send notification from here
	// email <EMAIL>
	go uc.notifyAdminOnUserRegistered(contexthelpers.WithoutCancel(ctx), &user, &client)
	go uc.notifyUserOnUserRegistered(contexthelpers.WithoutCancel(ctx), &user, &client)

	return response, nil
}

func (uc *UserUseCase) notifyUserOnUserRegistered(
	ctx context.Context,
	user *models.User,
	client *models.Client,
) {
	title := "Welcome to Assetfindr! Your Journey to Optimized Asset Management Begins Now."
	body :=
		`Welcome to Assetfindr! Your account is now active, and we're excited to assist you in maximizing your asset management efficiency. <br><br>
		Expect a call from our team shortly to guide you through the activation process and help tailor the Assetfindr platform to meet your specific business needs.<br><br>
		<img src="https://app.assetfindr.com/email_images/new-registration-illustration.png" alt="Welcome to AssetFindr"><br><br>
		We look forward to empowering your business with seamless Asset Maintenance Management.<br><br>
		Should you have any questions or need assistance along the way, our dedicated support team is here to help. Simply reach out to us at <a href = "mailto:<EMAIL>"><EMAIL></a>.<br><br>
		<a href = "mailto:<EMAIL>"><button style="background-color: #165FFF; color: white; border: none; border-radius: 5px; padding: 10px 20px; cursor: pointer;">Contact Us</button></a><br>`

	notifItem := notificationDtos.CreateNotificationItem{
		UserID:            user.ID,
		SourceCode:        notifConstants.NOTIFICATION_SOURCE_CODE_IDENTITY_AUTHENTICATION,
		SourceReferenceID: client.ID,
		TargetReferenceID: "",
		TargetURL:         "",
		MessageHeader:     title,
		MessageBody:       body,
		ClientID:          client.ID,
	}

	_ = uc.notifUseCase.CreateRegisterNotification(ctx, notificationDtos.CreateNotificationReq{
		Items:           []notificationDtos.CreateNotificationItem{notifItem},
		SendToEmail:     true,
		SendToPushNotif: false,
	})
}

func (uc *UserUseCase) notifyAdminOnUserRegistered(
	ctx context.Context,
	user *models.User,
	client *models.Client,
) {
	title, _ := tmplhelpers.ParseStringTemplate(
		"New Client Registered - {{.ClientName}}",
		struct{ ClientName string }{
			ClientName: client.Name,
		})

	body, _ := tmplhelpers.ParseStringTemplate(
		`Registration details <br>
		Company Name: {{.Name}} <br>
		First Name: {{.FirstName}} <br>
		Last Name: {{.LastName}} <br>
		Email: {{.Email}} <br>
		Phone Number: {{.PhoneNumber}} <br>
		Business Sector: {{.BusinessSector}} <br>
		Referral Source: {{.ReferralSource}} <br>
		Subdomain: {{.ClientAlias}} <br>`,
		struct{ Name, FirstName, LastName, Email, PhoneNumber, BusinessSector, ReferralSource, ClientAlias string }{
			Name:           client.Name,
			FirstName:      user.FirstName,
			LastName:       user.LastName,
			Email:          user.Email,
			PhoneNumber:    user.PhoneNumber,
			BusinessSector: client.BusinessSector,
			ReferralSource: client.ReferralSource,
			ClientAlias:    client.ClientAlias,
		})

	notifItem := notificationDtos.CreateNotificationItem{
		UserID:            constants.ASSETFINDR_CUSTOMER_SERVICE_USER_ID,
		SourceCode:        notifConstants.NOTIFICATION_SOURCE_CODE_IDENTITY_AUTHENTICATION,
		SourceReferenceID: client.ID,
		TargetReferenceID: "",
		TargetURL:         "",
		MessageHeader:     title,
		MessageBody:       body,
		ClientID:          constants.ASSETFINDR_CLIENT_ID,
	}

	_ = uc.notifUseCase.CreateNotification(ctx, notificationDtos.CreateNotificationReq{
		Items:           []notificationDtos.CreateNotificationItem{notifItem},
		SendToEmail:     true,
		SendToPushNotif: false,
	})
}

func (uc *UserUseCase) notifyUserOnUserCreated(
	ctx context.Context,
	user *dtos.CreateUserReq,
	client *models.Client,
	userID string,
) {
	title := "Subject: Your Account is Now Active 🚀 Let’s Get Started!"

	body, _ := tmplhelpers.ParseStringTemplate(
		`Welcome to Assetfindr! Your subscription is now active, unlocking a range of features to find, manage, and optimize your assets.<br><br>
		Here are your login details: <br><br>
		Company Name: <b>{{.Name}}</b> <br>
		Your Email: <b>{{.Email}}</b> <br>
		Password: <b>{{.Password}}</b> <br>
		Website Link: <b>{{.Alias}}.assetfindr.com</b> <br><br>
		<a href = "app.assetfindr.com"><button style="background-color: #165FFF; color: white; border: none; border-radius: 5px; padding: 10px 20px; cursor: pointer;">Login AssetFindr</button></a><br><br>
		Explore the full suite of features to boost your business operations. For any questions or assistance, feel free to reach out to our support team at <a href = "mailto:<EMAIL>"><EMAIL></a>.<br><br>
		Thanks for choosing Assetfindr—we're excited to help optimize your asset management!`,
		struct{ Name, Email, Password, Alias string }{
			Name:     client.Name,
			Email:    user.Email,
			Password: user.Password,
			Alias:    client.ClientAlias,
		})

	notifItem := notificationDtos.CreateNotificationItem{
		UserID:            userID,
		SourceCode:        notifConstants.NOTIFICATION_SOURCE_CODE_IDENTITY_AUTHENTICATION,
		SourceReferenceID: client.ID,
		TargetReferenceID: "",
		TargetURL:         "",
		MessageHeader:     title,
		MessageBody:       body,
		ClientID:          client.ID,
	}

	_ = uc.notifUseCase.CreateUserNotification(ctx, notificationDtos.CreateNotificationReq{
		Items:           []notificationDtos.CreateNotificationItem{notifItem},
		SendToEmail:     true,
		SendToPushNotif: false,
	})
}

func (uc *UserUseCase) UserLoginWithFirebaseToken(ctx context.Context, requestData *dtos.UserLoginWithFirebaseRequest) (dtos.UserAuthResponse, error) {
	var response dtos.UserAuthResponse

	var err error
	firebaseToken, err := firebaseApp.GetAuthClient().VerifyIDToken(context.Background(), requestData.UserFirebaseToken)
	if err != nil {
		commonlogger.Errorf("Failed in validating firebase auth login token: %v\n", err)
		return response, err
	}

	requestData.ClientAlias = strings.ToLower(requestData.ClientAlias)

	var user models.User
	err = uc.UserRepository.GetUserByClientAliasAndFirebaseId(ctx, uc.DB.DB(), &user, requestData.ClientAlias, firebaseToken.UID)
	if err != nil {
		// User is not exist in database
		if err == gorm.ErrRecordNotFound {
			return response, errors.New("Ops, You haven't register. Please register or contact support")
		}
		return response, err
	}

	if !user.IsActive() {
		return response, errorhandler.ErrNotAllowed("trying to login in inactive user, please contact your administrator")
	}

	userClients, err := uc.UserRepository.GetUserClients(ctx, uc.DB.DB(), models.UserClientCondition{
		Where: models.UserClientWhere{
			UserID: user.ID,
		},
		Preload: models.UserClientPreload{Client: true},
		//get all data from user_client with preload
		// Columns: []string{constants.CLIENT_ID_FIELD_NAME},
	})
	if err != nil {
		return response, err
	}

	loggedInUserClient := models.UserClient{}
	for _, val := range userClients {
		if val.Client.ClientAlias == requestData.ClientAlias {
			loggedInUserClient = val
			break
		}
	}
	if loggedInUserClient.ClientID == "" {
		return response, errorhandler.ErrNotAllowed("Login Failed: We can't find your account. Please try again or contact us for support.")
	}
	permissionGroup, _ := uc.permissionUseCase.getPermissionGroup(ctx, loggedInUserClient.PermissionGroupID, "")
	// if err != nil {
	// 	return response, err
	// }

	jwtToken, err := GenerateJWTSignedToken(tokenClaimParam{
		user:             user,
		clients:          userClients,
		permissionGroup:  *permissionGroup,
		loggedInClientID: loggedInUserClient.ClientID,
		loggedInClient:   loggedInUserClient.Client,
	})
	if err != nil {
		commonlogger.Errorf("Error in generating jwt token for registered user: %v\n", err)
		return response, err
	}

	clients := make([]models.Client, 0)
	for _, userClient := range userClients {
		clients = append(clients, userClient.Client)
	}

	photoUrl, _ := helpers.GenerateCloudStorageSignedURL(user.Photo, time.Duration(24*4))
	if user.Photo != "" {
		user.Photo = photoUrl
	}

	UserResponse := dtos.UserLoginResponse{}
	UserResponse.Set(user, clients)

	response = dtos.UserAuthResponse{
		User:             UserResponse,
		PermissionGroup:  *permissionGroup,
		JwtToken:         jwtToken,
		LoggedInClientID: loggedInUserClient.ClientID,
	}

	// LOG AUTHENTICATION
	authLog := models.AuthenticationLog{
		UserID:   user.ID,
		LogType:  constants.AUTHENTICATION_LOG_TYPE_LOGIN,
		ClientID: loggedInUserClient.ClientID,
	}
	if requestData.DeviceTypeRef != "" {
		authLog.DeviceTypeRef = null.StringFrom(requestData.DeviceTypeRef)
	}
	err = uc.UserRepository.CreateAuthenticationLog(ctx, uc.DB.DB(), &authLog)
	if err != nil {
		commonlogger.Errorf("Error in logging user authentication: %v\n", err)
		return response, err
	}

	return response, nil
}

func GenerateTokenClaimsFromModels(param tokenClaimParam) authhelpers.JwtTokenClaims {
	user := param.user
	userClients := param.clients
	clientIDs := make([]string, 0, len(userClients))
	for i := range userClients {
		clientIDs = append(clientIDs, userClients[i].ClientID)
	}

	rights := make([]authhelpers.PermissionRight, 0, len(param.permissionGroup.PermissionRights))
	for _, right := range param.permissionGroup.PermissionRights {
		rights = append(rights, authhelpers.PermissionRight{
			PermissionCategoryCode: right.PermissionCategoryCode,
			IsEnable:               right.IsEnable,
			PermissionCodes:        right.PermissionCodes,
		})
	}

	group := authhelpers.PermissionGroup{
		ID:                      param.permissionGroup.ID,
		GroupName:               param.permissionGroup.GroupName,
		Description:             param.permissionGroup.Description,
		PermissionGroupTypeCode: param.permissionGroup.PermissionGroupTypeCode,
		PermissionRights:        rights,
	}

	return authhelpers.JwtTokenClaims{
		StandardClaims: jwt.StandardClaims{
			ExpiresAt: time.Now().Add(24 * time.Hour).Unix(),
		},
		UserID:              user.ID,
		FirstName:           user.FirstName,
		LastName:            user.LastName,
		Email:               user.Email,
		FirebaseUserID:      user.FirebaseUserID,
		ClientIDs:           clientIDs,
		PermissionGroup:     group,
		LoggedInClientID:    param.loggedInClientID,
		OptimaxPackageCodes: param.loggedInClient.OptimaxPackageCodes,
	}
}

type tokenClaimParam struct {
	user             models.User
	loggedInClientID string
	loggedInClient   models.Client
	clients          []models.UserClient
	permissionGroup  dtos.PermissionGroupDetail
}

func GenerateJWToken(param tokenClaimParam) *jwt.Token {
	return jwt.NewWithClaims(jwt.SigningMethodHS256, GenerateTokenClaimsFromModels(param))
}

func GenerateJWTSignedToken(param tokenClaimParam) (string, error) {

	// Fetch the value of the environment variable
	jwtKey := []byte(viper.GetString(internalConstants.CONFIG_JWT_SECRET_KEY))

	token := GenerateJWToken(param)

	// Sign the token with your secret key
	signedToken, err := token.SignedString(jwtKey)

	if err != nil {
		return "", err
	}

	return signedToken, nil
}

func (uc *UserUseCase) CreateUserDevice(ctx context.Context, req dtos.CreateUserDeviceReq) (*commonmodel.CreateResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	device := models.UserDevice{
		UserID:              claim.UserID,
		FirebaseDeviceToken: req.FirebaseDeviceToken,
		DeviceTypeCode:      req.DeviceTypeCode,
		DeviceTypeRef:       req.DeviceTypeRef,
		StatusCode:          constants.DEVICE_STATUS_CODE_ACTIVE,
		ClientID:            claim.GetLoggedInClientID(),
		LastActivityDate:    time.Now(),
	}
	err = uc.UserRepository.CreateUserDevice(ctx, uc.DB.DB(), &device)
	if err != nil {
		return nil, err
	}

	return &commonmodel.CreateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: device.ID,
		Data:        nil,
	}, nil
}

func (uc *UserUseCase) Logout(ctx context.Context, req dtos.LogoutReq) error {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return err
	}

	device, err := uc.UserRepository.GetUserDevice(ctx, uc.DB.DB(), models.UserDeviceCondition{
		Where: models.UserDeviceWhere{
			UserID:              claim.UserID,
			FirebaseDeviceToken: req.FirebaseDeviceToken,
		},
	})
	if err != nil {
		return nil
	}

	if device.StatusCode == constants.DEVICE_STATUS_CODE_INACTIVE {
		return nil
	}

	updateDevice := models.UserDevice{StatusCode: constants.DEVICE_STATUS_CODE_INACTIVE}
	updateDevice.ID = device.ID

	err = uc.UserRepository.UpdateUserDevice(ctx, uc.DB.DB(), &updateDevice)
	if err != nil {
		return err
	}

	// LOG AUTHENTICATION
	authLog := models.AuthenticationLog{
		UserID:   claim.UserID,
		LogType:  constants.AUTHENTICATION_LOG_TYPE_LOGOUT,
		ClientID: claim.GetLoggedInClientID(),
	}
	if req.DeviceTypeRef != "" {
		authLog.DeviceTypeRef = null.StringFrom(req.DeviceTypeRef)
	}
	err = uc.UserRepository.CreateAuthenticationLog(ctx, uc.DB.DB(), &authLog)
	if err != nil {
		commonlogger.Errorf("Error in logging user authentication: %v\n", err)
		return err
	}

	return nil
}

func (uc *UserUseCase) CreateUser(ctx context.Context, req *dtos.CreateUserReq) (*commonmodel.CreateResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	if req.DepartmentID == "" && req.NewDepartment.Name == "" {
		err = errorhandler.ErrBadRequest("DEPARTMENT_ID_OR_NEW_DEPARTMENT_MUST_BE_PROVIDED")
		return nil, err
	}

	_, err = uc.PermissionRepository.GetPermissionGroup(ctx, uc.DB.DB(), models.PermissionGroupCondition{
		Where: models.PermissionGroupWhere{
			ID:       req.PermissionGroupID,
			ClientID: claim.GetLoggedInClientID(),
		},
	})
	if err != nil {
		return nil, err
	}

	user, err := uc.UserRepository.GetUser(ctx, uc.DB.DB(), models.UserCondition{
		Where: models.UserWhere{
			Email: req.Email,
		},
	})
	if err == nil {
		return nil, errorhandler.ErrUserAlreadyRegistered
	}

	if !errorhandler.IsErrNotFound(err) {
		commonlogger.Warnf("Error when create user: %v\n", err)
		return nil, errors.New("Failed to create user, please try again later")
	}

	if req.Photo != "" {
		destPhoto, err := uc.attachmentUseCase.MoveUserPhotoStorage(ctx, claim.GetLoggedInClientID(), req.Photo)
		if err != nil {
			return nil, err
		}

		req.Photo = destPhoto
	}

	err = nil

	params := (&auth.UserToCreate{}).Email(req.Email).Password(req.Password)
	firebaseUserRecord, err := firebaseApp.GetAuthClient().CreateUser(ctx, params)
	if err != nil {
		commonlogger.Warnf("Error create user to firebase: %v\n", err)
		return nil, errors.New("Failed create user to firebase, please try again later")
	}

	// Begin db transaction
	tX, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return nil, err
	}

	defer tX.Rollback()

	user = &models.User{
		FirstName:      req.FirstName,
		LastName:       req.LastName,
		Email:          req.Email,
		FirebaseUserID: firebaseUserRecord.UID,
		StatusCode:     constants.STATUS_ACTIVE,
		PhoneNumber:    req.PhoneNumber,
		ReferenceID:    req.ReferenceID,
		ReferenceRole:  req.ReferenceRole,
		Photo:          req.Photo,
		UserRoleCode:   constants.USER_DEFAULT_USER_ROLE,
	}

	err = uc.UserRepository.CreateUser(ctx, tX.DB(), user)
	if err != nil {
		commonlogger.Errorf("Error registering user to database: %v\n", err)
		return nil, errors.New("Failed registering user, please try again later")
	}

	userClient := models.UserClient{
		UserID:            user.ID,
		ClientID:          claim.GetLoggedInClientID(),
		PermissionGroupID: req.PermissionGroupID,
		DepartmentID:      null.StringFrom(req.DepartmentID),
	}
	err = uc.UserRepository.CreateUserClient(ctx, tX.DB(), &userClient)
	if err != nil {
		commonlogger.Errorf("Error create user client to database: %v\n", err)
		return nil, errors.New("Failed registering user, please try again later")
	}

	// Commit db transaction
	err = tX.Commit()
	if err != nil {
		return nil, err
	}

	// Begin 2nd db transaction
	tX, err = uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return nil, err
	}

	defer tX.Rollback()

	departmentID := req.DepartmentID
	if req.NewDepartment.Name != "" {
		newDepartment := &models.Department{
			ModelV2:        commonmodel.ModelV2{},
			Name:           req.NewDepartment.Name,
			DepartmentCode: req.NewDepartment.DepartmentCode,
			ContactUserID:  null.StringFrom(user.ID),
			StatusCode:     req.NewDepartment.StatusCode,
		}
		newDepartment.ClientID = claim.GetLoggedInClientID()
		newDepartment.CreatedBy = claim.UserID
		newDepartment.UpdatedBy = claim.UserID

		err = uc.departmentRepo.CreateDepartment(ctx, uc.DB.DB(), newDepartment)
		if err != nil {
			commonlogger.Errorf("Error creating department to database: %v\n", err)
			return nil, errors.New("Failed registering user, please try again later")
		}

		departmentID = newDepartment.ID
	}
	userClient.DepartmentID = null.StringFrom(departmentID)

	err = uc.UserRepository.UpdateUserClient(ctx, tX.DB(), &userClient)
	if err != nil {
		commonlogger.Errorf("Error updating user client to database: %v\n", err)
		return nil, errors.New("Failed registering user, please try again later")
	}

	// Commit 2nd db transaction
	err = tX.Commit()
	if err != nil {
		return nil, err
	}

	clients, _ := uc.UserRepository.GetUserClients(ctx, uc.DB.DB(), models.UserClientCondition{
		Where: models.UserClientWhere{
			UserID: user.ID,
		},
		Preload: models.UserClientPreload{Client: true},
	})

	go uc.notifyUserOnUserCreated(contexthelpers.WithoutCancel(ctx), req, &clients[0].Client, user.ID)

	return &commonmodel.CreateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: user.ID,
		Data:        nil,
	}, nil
}

func (uc *UserUseCase) UpdateUser(ctx context.Context, id string, req *dtos.UpdateUserReq) (*commonmodel.UpdateResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	user, err := uc.UserRepository.GetUser(ctx, uc.DB.DB(), models.UserCondition{
		Where: models.UserWhere{
			ID:       id,
			ClientID: claim.GetLoggedInClientID(),
		},
	})
	if err != nil {
		return nil, err
	}

	tX, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return nil, err
	}

	defer tX.Rollback()

	// get ongoing update user permission group
	userClient, err := uc.UserRepository.GetUserClient(ctx, uc.DB.DB(), models.UserClientCondition{
		Where: models.UserClientWhere{
			UserID: id,
		},
	})
	if err != nil {
		return nil, err
	}
	updatedUserClient := models.UserClient{
		UserID:   id,
		ClientID: userClient.ClientID,
	}
	if req.PermissionGroupID != "" && userClient.PermissionGroupID != req.PermissionGroupID {
		_, err = uc.PermissionRepository.GetPermissionGroup(ctx, uc.DB.DB(), models.PermissionGroupCondition{
			Where: models.PermissionGroupWhere{
				ID:       req.PermissionGroupID,
				ClientID: claim.GetLoggedInClientID(),
			},
		})
		if err != nil {
			return nil, err
		}

		updatedUserClient.PermissionGroupID = req.PermissionGroupID
	}
	if userClient.DepartmentID != req.DepartmentID {
		updatedUserClient.DepartmentID = req.DepartmentID

		// search dept where this userID as PIC
		dept, _ := uc.departmentRepo.GetDepartment(ctx, uc.DB.DB(), models.DepartmentCondition{
			Where: models.DepartmentWhere{
				ContactUserID: user.ID,
			},
		})
		if dept != nil {
			err = uc.departmentRepo.UpdateDepartment(ctx, uc.DB.DB(), userClient.DepartmentID.String, &models.Department{ContactUserID: null.StringFrom("")})
			if err != nil {
				return nil, err
			}
		}

	}
	err = uc.UserRepository.UpdateUserClient(ctx, tX.DB(), &updatedUserClient)
	if err != nil {
		return nil, err
	}
	// move ongoing update user photo
	if req.Photo != "" && req.Photo != user.Photo {
		destPhoto, err := uc.attachmentUseCase.MoveUserPhotoStorage(ctx, claim.GetLoggedInClientID(), req.Photo)
		if err != nil {
			return nil, err
		}

		req.Photo = destPhoto
	}

	isAnyUpdate, updateUser := checkUpdateUser(user, *req)
	if isAnyUpdate {
		err = uc.UserRepository.UpdateUser(ctx, tX.DB(), updateUser)
		if err != nil {
			commonlogger.Errorf("Error registering user to database: %v\n", err)
			return nil, err
		}
	}

	err = tX.Commit()
	if err != nil {
		return nil, err
	}

	return &commonmodel.UpdateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: user.ID,
		Data:        nil,
	}, nil
}

func (uc *UserUseCase) UpdateUserEmail(ctx context.Context, req dtos.ChangeUserEmailReq) (*commonmodel.UpdateResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	user, err := uc.UserRepository.GetUser(ctx, uc.DB.DB(), models.UserCondition{
		Where: models.UserWhere{
			ID:       req.UserID,
			ClientID: claim.GetLoggedInClientID(),
		},
	})
	if err != nil {
		return nil, err
	}

	if user.Email == req.NewEmail {
		return &commonmodel.UpdateResponse{
			Success:     true,
			Message:     "Success",
			ReferenceID: user.ID,
			Data:        nil,
		}, nil
	}

	firebaseUserToUpdate := (&auth.UserToUpdate{}).Email(req.NewEmail)
	_, err = firebaseApp.GetAuthClient().UpdateUser(ctx, user.FirebaseUserID, firebaseUserToUpdate)
	if err != nil {
		return nil, err
	}

	updateUser := models.User{
		Email: req.NewEmail,
	}
	updateUser.ID = req.UserID

	err = uc.UserRepository.UpdateUser(ctx, uc.DB.DB(), &updateUser)
	if err != nil {
		commonlogger.Errorf("Error registering user to database: %v\n", err)
		return nil, err
	}

	return &commonmodel.UpdateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: user.ID,
		Data:        nil,
	}, nil
}

func checkUpdateUser(existingUser *models.User, req dtos.UpdateUserReq) (bool, *models.User) {
	isAnyUpdate := false
	updateUser := models.User{}
	updateUser.ID = existingUser.ID
	if req.FirstName != "" && req.FirstName != existingUser.FirstName {
		isAnyUpdate = true
		updateUser.FirstName = req.FirstName
	}

	if req.LastName != "" && req.LastName != existingUser.LastName {
		isAnyUpdate = true
		updateUser.LastName = req.LastName
	}

	if req.PhoneNumber != "" && req.PhoneNumber != existingUser.PhoneNumber {
		isAnyUpdate = true
		updateUser.PhoneNumber = req.PhoneNumber
	}

	if req.Photo != "" && req.Photo != existingUser.Photo {
		isAnyUpdate = true
		updateUser.Photo = req.Photo
	}

	if req.ReferenceID != "" && req.ReferenceID != existingUser.ReferenceID {
		isAnyUpdate = true
		updateUser.ReferenceID = req.ReferenceID
	}

	if req.ReferenceRole != "" && req.ReferenceRole != existingUser.ReferenceID {
		isAnyUpdate = true
		updateUser.ReferenceRole = req.ReferenceRole
	}

	return isAnyUpdate, &updateUser
}

func (uc *UserUseCase) GetUserList(ctx context.Context, req dtos.GetUserListReq) (*commonmodel.ListResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	count, users, err := uc.UserRepository.GetUserList(ctx, uc.DB.DB(), models.GetUserListParam{
		ListRequest: req.ListRequest,
		Cond: models.UserCondition{
			Where: models.UserWhere{
				ClientID:      claim.GetLoggedInClientID(),
				IsShowAll:     req.IsShowAll,
				DepartmentIDs: req.DepartmentIDs,
			},
			Preload: models.UserPreload{
				CurrentUserClientGroup: true,
			},
		},
	})
	if err != nil {
		return nil, err
	}

	resp := make([]dtos.GetUserListResp, 0, len(users))
	for _, user := range users {
		item := dtos.GetUserListResp{
			ID:             user.ID,
			FirstName:      user.FirstName,
			LastName:       user.LastName,
			ReferenceID:    user.ReferenceID,
			ReferenceRole:  user.ReferenceRole,
			Location:       "",
			GroupName:      user.CurrentUserClient.Group.GroupName,
			PhoneNumber:    user.PhoneNumber,
			StatusCode:     user.StatusCode,
			DepartmentID:   user.CurrentUserClient.DepartmentID.String,
			DepartmentName: user.CurrentUserClient.Department.Name,
			Email:          user.Email,
		}

		resp = append(resp, item)
	}

	return &commonmodel.ListResponse{
		TotalRecords: count,
		PageSize:     req.PageSize,
		PageNo:       req.PageNo,
		Data:         resp,
	}, nil
}

func (uc *UserUseCase) GetUserListFromAdmin(ctx context.Context, clientID string, req dtos.GetUserListFromAdminReq) (*commonmodel.ListResponse, error) {
	count, users, err := uc.UserRepository.GetUserList(ctx, uc.DB.DB(), models.GetUserListParam{
		ListRequest: req.ListRequest,
		Cond: models.UserCondition{
			Where: models.UserWhere{
				ClientID:  clientID,
				IsShowAll: req.IsShowAll,
			},
		},
	})
	if err != nil {
		return nil, err
	}

	resp := make([]dtos.GetUserListResp, 0, len(users))
	for _, user := range users {
		item := dtos.GetUserListResp{
			ID:            user.ID,
			FirstName:     user.FirstName,
			LastName:      user.LastName,
			ReferenceID:   user.ReferenceID,
			ReferenceRole: user.ReferenceRole,
			Location:      "",
			PhoneNumber:   user.PhoneNumber,
			StatusCode:    user.StatusCode,
		}

		resp = append(resp, item)
	}

	return &commonmodel.ListResponse{
		TotalRecords: count,
		PageSize:     req.PageSize,
		PageNo:       req.PageNo,
		Data:         resp,
	}, nil
}

func (uc *UserUseCase) GetUser(ctx context.Context, ID string) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	user, err := uc.UserRepository.GetUser(ctx, uc.DB.DB(), models.UserCondition{
		Where: models.UserWhere{
			ID:       ID,
			ClientID: claim.GetLoggedInClientID(),
		},
		Preload: models.UserPreload{
			CurrentUserClientGroup: true,
		},
	})
	if err != nil {
		return nil, err
	}

	photoUrl, _ := helpers.GenerateCloudStorageSignedURL(user.Photo, time.Duration(24))
	if user.Photo != "" {
		user.Photo = photoUrl
	}
	resp := dtos.GetUserDetailResp{
		ID:             user.ID,
		FirstName:      user.FirstName,
		LastName:       user.LastName,
		Email:          user.Email,
		PhoneNumber:    user.PhoneNumber,
		ReferenceID:    user.ReferenceID,
		ReferenceRole:  user.ReferenceRole,
		Location:       "",
		Department:     "",
		GroupID:        user.CurrentUserClient.Group.ID,
		GroupName:      user.CurrentUserClient.Group.GroupName,
		StatusCode:     user.StatusCode,
		Photo:          user.Photo,
		DepartmentID:   user.CurrentUserClient.DepartmentID.String,
		DepartmentName: user.CurrentUserClient.Department.Name,
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: ID,
		Data:        resp,
	}, nil
}

func (uc *UserUseCase) DeleteUser(ctx context.Context, id string) (*commonmodel.DeleteResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	if claim.UserID == id {
		return nil, errorhandler.ErrBadRequest("You can't delete your own account")
	}

	deleted, err := uc.UserRepository.GetUser(ctx, uc.DB.DB(), models.UserCondition{
		Where: models.UserWhere{
			ID:       id,
			ClientID: claim.GetLoggedInClientID(),
		},
	})
	if err != nil {
		return nil, err
	}

	_, err = uc.departmentRepo.GetDepartment(ctx, uc.DB.DB(), models.DepartmentCondition{
		Where: models.DepartmentWhere{
			ContactUserID: id,
		},
		Columns:     []string{},
		IsForUpdate: false,
	})
	if err != nil && !errorhandler.IsErrNotFound(err) {
		return nil, err
	}

	if err == nil {
		return nil, errorhandler.ErrBadRequest("The user is currently a contact person for other department, change contact person from department")
	}

	updateUser := &models.User{
		StatusCode: constants.STATUS_CLOSED,
	}
	updateUser.ID = id

	err = uc.UserRepository.UpdateUser(ctx, uc.DB.DB(), updateUser)
	if err != nil {
		return nil, err
	}

	err = firebaseApp.GetAuthClient().DeleteUser(ctx, deleted.FirebaseUserID)
	if err != nil {
		return nil, err
	}

	return &commonmodel.DeleteResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: id,
	}, nil
}

func (uc *UserUseCase) ChangeUserPassword(ctx context.Context, req dtos.ChangeUserPasswordReq) (*commonmodel.UpdateResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	user, err := uc.UserRepository.GetUser(ctx, uc.DB.DB(), models.UserCondition{
		Where: models.UserWhere{
			ID:       req.UserID,
			ClientID: claim.GetLoggedInClientID(),
		},
	})
	if err != nil {
		return nil, err
	}

	firebaseUserToUpdate := (&auth.UserToUpdate{}).Password(req.NewPassword)
	_, err = firebaseApp.GetAuthClient().UpdateUser(ctx, user.FirebaseUserID, firebaseUserToUpdate)
	if err != nil {
		return nil, err
	}

	return &commonmodel.UpdateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: req.UserID,
		Data:        nil,
	}, nil
}

func (uc *UserUseCase) CheckClientAliasAvailibility(ctx context.Context, clientAlias string) (*commonmodel.DetailResponse, error) {
	clientAlias = strings.ToLower(clientAlias)
	_, err := uc.UserRepository.GetClientByAlias(ctx, uc.DB.DB(), clientAlias)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return &commonmodel.DetailResponse{
				Success:     true,
				Message:     "Client alias is available",
				ReferenceID: clientAlias,
			}, nil
		}
		return nil, err
	}
	return &commonmodel.DetailResponse{
		Success:     false,
		Message:     "Client alias is not available",
		ReferenceID: clientAlias,
	}, nil
}

func (uc *UserUseCase) AddUsersToSubClient(ctx context.Context, req dtos.AddUsersToSubClientReq) (*commonmodel.CreateResponse, error) {
	_, err := uc.clientRepo.GetLinkedClient(ctx, uc.DB.DB(), req.ParentClientID, req.ChildClientID)
	if err != nil {
		return nil, err
	}

	group, err := uc.PermissionRepository.GetPermissionGroup(ctx, uc.DB.DB(), models.PermissionGroupCondition{
		Where: models.PermissionGroupWhere{
			ClientID:                req.ParentClientID,
			PermissionGroupTypeCode: constants.PERMISSION_GROUP_TYPE_ADMIN,
		},
	})
	if err != nil {
		return nil, err
	}

	users, err := uc.UserRepository.GetUsersV2(ctx, uc.DB.DB(), models.UserCondition{
		Where: models.UserWhere{
			IDs:      req.UserIDs,
			ClientID: req.ParentClientID,
		},
	})
	if err != nil {
		return nil, err
	}

	if len(users) != len(req.UserIDs) {
		return nil, errorhandler.ErrBadRequest("SOME_USER_ISN'T_FOUND")
	}

	userClients := make([]models.UserClient, 0, len(users))
	for _, user := range users {
		userClients = append(userClients, models.UserClient{
			UserID:            user.ID,
			ClientID:          req.ChildClientID,
			PermissionGroupID: group.ID,
		})
	}

	err = uc.clientRepo.CreateUserClients(ctx, uc.DB.DB(), userClients)
	if err != nil {
		return nil, err
	}

	return &commonmodel.CreateResponse{
		Success:     true,
		Message:     "success",
		ReferenceID: "",
		Data:        nil,
	}, nil
}
