package usecase

import (
	"assetfindr/internal/app/user-identity/constants"
	"assetfindr/internal/app/user-identity/dtos"
	"assetfindr/internal/app/user-identity/models"
	"assetfindr/internal/app/user-identity/repository"
	"assetfindr/internal/errorhandler"
	"assetfindr/internal/infrastructure/database"
	"assetfindr/pkg/common/commonmodel"
	"assetfindr/pkg/common/helpers/authhelpers"
	"context"
	"reflect"
)

type PermissionUseCase struct {
	DB                   database.DBUsecase
	PermissionRepository repository.PermissionRepository
	UserRepo             repository.UserRepository
}

func NewPermissionUseCase(
	DB database.DBUsecase,
	permissionRepo repository.PermissionRepository,
	userRepo repository.UserRepository,
) *PermissionUseCase {
	return &PermissionUseCase{
		DB:                   DB,
		PermissionRepository: permissionRepo,
		UserRepo:             userRepo,
	}
}

func (uc *PermissionUseCase) UpsertPermissionGroupAdmin(ctx context.Context) (*commonmodel.DetailResponse, error) {
	permissionGroups, err := uc.PermissionRepository.GetPermissionGroups(ctx, uc.DB.DB(), models.PermissionGroupCondition{
		Where: models.PermissionGroupWhere{
			PermissionGroupTypeCode: constants.PERMISSION_GROUP_TYPE_ADMIN,
		},
	})
	if err != nil {
		return nil, err
	}

	categories, err := uc.PermissionRepository.GetPermissionCategories(ctx, uc.DB.DB())
	if err != nil {
		return nil, err
	}

	for i := range permissionGroups {
		rights := make([]models.PermissionGroupRight, 0, len(categories))
		for _, category := range categories {
			permissionCodes := make([]string, 0, len(category.Permissions))
			for _, permission := range category.Permissions {
				permissionCodes = append(permissionCodes, permission.Code)
			}
			right := models.PermissionGroupRight{
				PermissionGroupID:      permissionGroups[i].ID,
				PermissionCategoryCode: category.Code,
				PermissionCodes:        permissionCodes,
				IsEnable:               true,
			}
			right.ClientID = permissionGroups[i].ClientID
			right.CreatedBy = permissionGroups[i].CreatedBy

			rights = append(rights, right)
		}

		err = uc.PermissionRepository.UpsertPermissionGroupRights(ctx, uc.DB.DB(), rights)
		if err != nil {
			return nil, err
		}
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        nil,
	}, nil
}

func (uc *PermissionUseCase) GetPermissionCategories(ctx context.Context, filterByClientPackage string) (*commonmodel.ListResponse, error) {
	clientPackages := []string{}
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	if filterByClientPackage == "true" {
		// TODO: will change this from claim, for now not worth
		client, err := uc.UserRepo.GetClient(ctx, uc.DB.DB(), models.ClientCondition{
			Where: models.ClientWhere{
				ID: claim.GetLoggedInClientID(),
			},
		})
		clientPackages = client.ClientPackages

		if err != nil {
			return nil, err
		}
	}

	permissionCategories, err := uc.PermissionRepository.GetPermissionCategoriesV2(ctx, uc.DB.DB(), models.PermissionCategoryCondition{
		Where: models.PermissionCategoryWhere{
			ClientPackageCodes: clientPackages,
		},
		Preload: models.PermissionCategoryPreload{
			Permissions: true,
		},
	})
	if err != nil {
		return nil, err
	}

	return &commonmodel.ListResponse{
		TotalRecords: len(permissionCategories),
		PageSize:     len(permissionCategories),
		PageNo:       1,
		Data:         permissionCategories,
	}, nil
}

func (uc *PermissionUseCase) CreatePermissionGroup(ctx context.Context, req dtos.UpsertPermissionGroupReq) (*commonmodel.CreateResponse, error) {
	tx, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return nil, err
	}

	defer tx.Rollback()

	group := &models.PermissionGroup{
		GroupName:               req.GroupName,
		Description:             req.Description,
		PermissionGroupTypeCode: constants.PERMISSION_GROUP_TYPE_GENERAL,
	}
	err = uc.PermissionRepository.CreatePermissionGroup(ctx, tx.DB(), group)
	if err != nil {
		return nil, err
	}

	rights := make([]models.PermissionGroupRight, 0, len(req.PermissionRights))
	for _, right := range req.PermissionRights {
		rights = append(rights, models.PermissionGroupRight{
			PermissionGroupID:      group.ID,
			PermissionCategoryCode: right.PermissionCategoryCode,
			PermissionCodes:        right.PermissionCodes,
			IsEnable:               right.IsEnable,
		})
	}

	err = uc.PermissionRepository.CreatePermissionGroupRights(ctx, tx.DB(), rights)
	if err != nil {
		return nil, err
	}

	err = tx.Commit()
	if err != nil {
		return nil, err
	}

	return &commonmodel.CreateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: group.ID,
		Data:        nil,
	}, nil
}

func (uc *PermissionUseCase) UpdatePermissionGroup(ctx context.Context, ID string, req dtos.UpsertPermissionGroupReq) (*commonmodel.UpdateResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	group, err := uc.PermissionRepository.GetPermissionGroup(ctx, uc.DB.DB(), models.PermissionGroupCondition{
		Where: models.PermissionGroupWhere{
			ID:           ID,
			ClientID:     claim.GetLoggedInClientID(),
			NotAdminType: true,
		},
		Preload: models.PermissionGroupPreload{
			GroupRights: true,
		},
	})
	if err != nil {
		return nil, err
	}

	isAnyGroupUpdate, updateGroup := checkUpdateGroup(group, req)

	newRights, updateRights := checkUpdateGroupRights(group, req)
	isAnyGroupRightsUpdate := len(updateRights) > 0
	isAnyNewGroupRights := len(newRights) > 0

	if !isAnyGroupUpdate && !isAnyGroupRightsUpdate && !isAnyNewGroupRights {
		return &commonmodel.UpdateResponse{
			Success:     true,
			Message:     "Success",
			ReferenceID: ID,
			Data:        nil,
		}, nil
	}

	tx, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return nil, err
	}

	defer tx.Rollback()

	if isAnyGroupRightsUpdate {
		for _, right := range updateRights {
			err = uc.PermissionRepository.UpdatePermissionGroupRight(ctx, tx.DB(), &right)
			if err != nil {
				return nil, err
			}
		}
	}

	if isAnyNewGroupRights {
		err = uc.PermissionRepository.CreatePermissionGroupRights(ctx, tx.DB(), newRights)
		if err != nil {
			return nil, err
		}
	}

	if isAnyGroupUpdate || isAnyGroupRightsUpdate {
		err = uc.PermissionRepository.UpdatePermissionGroup(ctx, tx.DB(), updateGroup)
		if err != nil {
			return nil, err
		}
	}

	err = tx.Commit()
	if err != nil {
		return nil, err
	}

	return &commonmodel.UpdateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: ID,
		Data:        nil,
	}, nil
}

func checkUpdateGroup(
	before *models.PermissionGroup,
	req dtos.UpsertPermissionGroupReq,
) (bool, *models.PermissionGroup) {
	isAnyUpdate := false
	updatePermissionGroup := models.PermissionGroup{
		GroupName:   req.GroupName,
		Description: req.Description,
	}
	updatePermissionGroup.ID = before.ID
	if req.GroupName != "" && req.GroupName != before.GroupName {
		isAnyUpdate = true
		updatePermissionGroup.GroupName = req.GroupName
	}

	if req.Description != "" && req.Description != before.Description {
		isAnyUpdate = true
		updatePermissionGroup.Description = req.Description
	}

	return isAnyUpdate, &updatePermissionGroup
}

func checkUpdateGroupRights(
	before *models.PermissionGroup,
	req dtos.UpsertPermissionGroupReq,
) (newGroupRights []models.PermissionGroupRight, updateGroupRights []models.PermissionGroupRight) {
	mapBefore := map[string]models.PermissionGroupRight{}
	for i := range before.GroupRights {
		mapBefore[before.GroupRights[i].PermissionCategoryCode] = before.GroupRights[i]
	}

	newGroupRights = []models.PermissionGroupRight{}
	updateGroupRights = make([]models.PermissionGroupRight, 0, len(before.GroupRights))
	for _, reqRight := range req.PermissionRights {
		isAnyUpdate := false
		rightBefore, ok := mapBefore[reqRight.PermissionCategoryCode]
		if !ok {
			newGroupRights = append(newGroupRights, models.PermissionGroupRight{
				PermissionGroupID:      before.ID,
				PermissionCategoryCode: reqRight.PermissionCategoryCode,
				PermissionCodes:        reqRight.PermissionCodes,
				IsEnable:               reqRight.IsEnable,
			})
			continue
		}
		rightAfter := models.PermissionGroupRight{
			PermissionGroupID:      rightBefore.PermissionGroupID,
			PermissionCategoryCode: rightBefore.PermissionCategoryCode,
			PermissionCodes:        rightBefore.PermissionCodes,
			IsEnable:               rightBefore.IsEnable,
		}

		if rightBefore.IsEnable != reqRight.IsEnable {
			isAnyUpdate = true
			rightAfter.IsEnable = reqRight.IsEnable
		}

		if !reflect.DeepEqual([]string(rightBefore.PermissionCodes), reqRight.PermissionCodes) {
			isAnyUpdate = true
			rightAfter.PermissionCodes = reqRight.PermissionCodes
		}

		if isAnyUpdate {
			updateGroupRights = append(updateGroupRights, rightAfter)
		}
	}

	return newGroupRights, updateGroupRights
}

func (uc *PermissionUseCase) GetPermissionGroupList(ctx context.Context, req commonmodel.ListRequest) (*commonmodel.ListResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	count, groups, err := uc.PermissionRepository.GetPermissionGroupList(ctx, uc.DB.DB(), models.GetPermissionGroupListParam{
		ListRequest: req,
		Cond: models.PermissionGroupCondition{
			Where: models.PermissionGroupWhere{
				ClientID: claim.GetLoggedInClientID(),
			},
			Preload: models.PermissionGroupPreload{
				GroupRights:      false,
				ActiveUserClient: true,
			},
		},
	})
	if err != nil {
		return nil, err
	}

	resp := make([]dtos.GetPermissionGroupResp, 0, len(groups))
	for _, group := range groups {
		resp = append(resp, dtos.GetPermissionGroupResp{
			ID:                      group.ID,
			GroupName:               group.GroupName,
			Description:             group.Description,
			NumActiveUser:           len(group.ActiveUserClient),
			PermissionGroupTypeCode: group.PermissionGroupTypeCode,
		})
	}

	return &commonmodel.ListResponse{
		TotalRecords: count,
		PageSize:     req.PageSize,
		PageNo:       req.PageNo,
		Data:         resp,
	}, nil
}

func (uc *PermissionUseCase) GetPermissionGroup(ctx context.Context, ID string) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	resp, err := uc.getPermissionGroup(ctx, ID, claim.GetLoggedInClientID())
	if err != nil {
		return nil, err
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: ID,
		Data:        resp,
	}, nil
}

func constructPersmissionGroupResp(group *models.PermissionGroup) dtos.PermissionGroupDetail {
	rightsResp := make([]dtos.PermissionRightDetail, 0, len(group.GroupRights))
	for _, right := range group.GroupRights {
		rightsResp = append(rightsResp, dtos.PermissionRightDetail{
			PermissionCategoryCode: right.PermissionCategoryCode,
			IsEnable:               right.IsEnable,
			PermissionCodes:        right.PermissionCodes,
		})
	}

	resp := dtos.PermissionGroupDetail{
		ID:                      group.ID,
		GroupName:               group.GroupName,
		Description:             group.Description,
		PermissionGroupTypeCode: group.PermissionGroupTypeCode,
		PermissionRights:        rightsResp,
	}

	return resp
}

func (uc *PermissionUseCase) getPermissionGroup(ctx context.Context, ID, clientID string) (*dtos.PermissionGroupDetail, error) {
	if ID == "" {
		return &dtos.PermissionGroupDetail{}, nil
	}

	group, err := uc.PermissionRepository.GetPermissionGroup(ctx, uc.DB.DB(), models.PermissionGroupCondition{
		Where: models.PermissionGroupWhere{
			ID:       ID,
			ClientID: clientID,
		},
		Preload: models.PermissionGroupPreload{
			GroupRights: true,
		},
	})
	if err != nil {
		return nil, err
	}

	resp := constructPersmissionGroupResp(group)
	return &resp, nil
}

func (uc *PermissionUseCase) DeletePermissionGroup(ctx context.Context, ID string) (*commonmodel.DeleteResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	_, err = uc.PermissionRepository.GetPermissionGroup(ctx, uc.DB.DB(), models.PermissionGroupCondition{
		Where: models.PermissionGroupWhere{
			ID:           ID,
			ClientID:     claim.GetLoggedInClientID(),
			NotAdminType: true,
		},
	})
	if err != nil {
		return nil, err
	}

	userClients, err := uc.UserRepo.GetUserClients(ctx, uc.DB.DB(), models.UserClientCondition{
		Where: models.UserClientWhere{
			PermissionGroupID: ID,
		},
		Columns: []string{},
	})
	if err != nil {
		return nil, err
	}

	if len(userClients) > 0 {
		return nil, errorhandler.ErrNotAllowed("permission group is currently in used")
	}

	err = uc.PermissionRepository.DeletePermissionGroup(ctx, uc.DB.DB(), ID)
	if err != nil {
		return nil, err
	}

	return &commonmodel.DeleteResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: ID,
	}, nil
}
