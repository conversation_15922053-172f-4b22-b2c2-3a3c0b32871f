package models

import (
	// recipeModels "assetfindr/internal/app/recipe/models"

	"assetfindr/internal/app/user-identity/constants"
	"assetfindr/pkg/common/commonmodel"

	"gorm.io/gorm"
)

type User struct {
	commonmodel.ModelV2NoClientField
	FirstName         string        `json:"first_name" gorm:"type:varchar(100)"`
	LastName          string        `json:"last_name" gorm:"type:varchar(100);default:null"`
	Email             string        `json:"email" gorm:"type:varchar(50)"`
	Photo             string        `json:"photo" gorm:"type:varchar(255);default:null"`
	FirebaseUserID    string        `json:"firebase_user_id" gorm:"type:varchar(100);unique_index"`
	UserRoleCode      string        `json:"user_role_code" gorm:"index"`
	UserRole          UserRole      `gorm:"references:Code"`
	StatusCode        string        `gorm:"type:varchar(20);not null" json:"status_code"`
	PhoneNumber       string        `gorm:"type:varchar(25);default:null" json:"phone_number"`
	ReferenceID       string        `gorm:"type:varchar(40);default:null" json:"reference_id"`
	ReferenceRole     string        `gorm:"type:varchar(50);default:null" json:"reference_role"`
	Status            UserStatus    `gorm:"foreignkey:StatusCode;association_foreignkey:Code" json:"status"`
	UserClients       *[]UserClient `gorm:"foreignkey:UserID" json:"user_clients"`
	CurrentUserClient UserClient    `gorm:"foreignkey:UserID" json:"current_user_client"`
	Devices           []UserDevice  `gorm:"foreignkey:UserID" json:"user_devices"`
}

// TableName specifies the database table name for the User model
func (u *User) TableName() string {
	return "uis_users"
}

func (u *User) BeforeCreate(tx *gorm.DB) error {
	u.SetUUID("usr")
	u.ModelV2NoClientField.BeforeCreate(tx)
	return nil
}

func (u *User) BeforeUpdate(tx *gorm.DB) error {
	u.ModelV2NoClientField.BeforeUpdate(tx)
	return nil
}

type UserCondition struct {
	Where   UserWhere
	Columns []string
	Preload UserPreload
}

type UserPreload struct {
	CurrentUserClientGroup bool
	ActiveDevices          bool
}

type UserWhere struct {
	ID                string
	IDs               []string
	ClientID          string
	Email             string
	Emails            []string
	IsShowAll         bool
	PermissionGroupID string
	WithOrmDeleted    bool
	DepartmentIDs     []string
}

type GetUserListParam struct {
	commonmodel.ListRequest
	Cond UserCondition
}

func (u User) GetName() string {
	if u.LastName == "" {
		return u.FirstName
	}

	return u.FirstName + " " + u.LastName
}

func (u User) IsActive() bool {
	return u.StatusCode == constants.STATUS_ACTIVE
}
