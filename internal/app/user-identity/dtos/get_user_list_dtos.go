package dtos

import "assetfindr/pkg/common/commonmodel"

type GetUserListReq struct {
	commonmodel.ListRequest
	IsShowAll     bool     `form:"is_show_all"`
	DepartmentIDs []string `form:"department_ids"`
}

type GetUserListFromAdminReq struct {
	commonmodel.ListRequest
	IsShowAll bool `form:"is_show_all"`
}

type GetUserListResp struct {
	ID             string `json:"id"`
	FirstName      string `json:"first_name"`
	LastName       string `json:"last_name"`
	ReferenceID    string `json:"reference_id"`
	ReferenceRole  string `json:"reference_role"`
	Location       string `json:"location"`
	GroupName      string `json:"group_name"`
	StatusCode     string `json:"status_code"`
	PhoneNumber    string `json:"phone_number"`
	DepartmentID   string `json:"department_id"`
	DepartmentName string `json:"department_name"`
	Email          string `json:"email"`
}

type GetUserDetailResp struct {
	ID             string `json:"id"`
	FirstName      string `json:"first_name"`
	LastName       string `json:"last_name"`
	Email          string `json:"email"`
	PhoneNumber    string `json:"phone_number"`
	ReferenceID    string `json:"reference_id"`
	ReferenceRole  string `json:"reference_role"`
	Location       string `json:"location"`
	Department     string `json:"department"`
	GroupID        string `json:"permission_group_id"`
	GroupName      string `json:"group_name"`
	StatusCode     string `json:"status_code"`
	Photo          string `json:"photo"`
	DepartmentID   string `json:"department_id"`
	DepartmentName string `json:"department_name"`
}
