package handler

import (
	"assetfindr/internal/app/user-identity/dtos"
	"assetfindr/internal/app/user-identity/usecase"
	"assetfindr/internal/errorhandler"
	"assetfindr/pkg/common/commonmodel"
	"assetfindr/pkg/common/helpers/accurate"
	"net/http"

	"github.com/gin-gonic/gin"
)

type PartnerHandler struct {
	partnerUseCase *usecase.PartnerUseCase
}

func NewPartnerHandler(
	partnerUseCase *usecase.PartnerUseCase,
) *PartnerHandler {
	return &PartnerHandler{
		partnerUseCase: partnerUseCase,
	}
}

func (h *PartnerHandler) GetPartners(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.PartnerListReq{}
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	req.Normalize()
	resp, err := h.partnerUseCase.GetPartners(ctx, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.J<PERSON>(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *PartnerHandler) GetPartnerCustomerList(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.PartnerCustomerListReq{}
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	req.Normalize()
	if accurate.UseAccurate(c) {
		resp, err := h.partnerUseCase.GetCustomerListAccurate(ctx, req)
		if err != nil {
			httpStatus, errMessage := errorhandler.ParseToHttpError(err)
			c.JSON(httpStatus, gin.H{"error": errMessage})
			return
		}

		c.JSON(http.StatusOK, resp)
		return
	}

	resp, err := h.partnerUseCase.GetPartnerCustomerList(ctx, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *PartnerHandler) GetPartnerCustomerAccuratePaymentTermList(c *gin.Context) {
	ctx := c.Request.Context()
	req := commonmodel.ListRequest{}
	err := c.BindQuery(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	resp := commonmodel.ListResponse{
		TotalRecords: 0,
		PageSize:     req.PageSize,
		PageNo:       req.PageNo,
		Data:         nil,
	}

	req.Normalize()
	if accurate.UseAccurate(c) {
		resp, err := h.partnerUseCase.GetPartnerCustomerAccuratePaymentTermList(ctx, req)
		if err != nil {
			httpStatus, errMessage := errorhandler.ParseToHttpError(err)
			c.JSON(httpStatus, gin.H{"error": errMessage})
			return
		}

		c.JSON(http.StatusOK, resp)
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *PartnerHandler) GetPartnerCustomer(c *gin.Context) {
	ctx := c.Request.Context()
	id := c.Param("id")

	if accurate.UseAccurate(c) {
		resp, err := h.partnerUseCase.GetPartnerCustomerFromAccurate(ctx, id)
		if err != nil {
			httpStatus, errMessage := errorhandler.ParseToHttpError(err)
			c.JSON(httpStatus, gin.H{"error": errMessage})
			return
		}

		c.JSON(http.StatusOK, resp)
		return
	}

	resp, err := h.partnerUseCase.GetPartnerCustomer(ctx, id)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *PartnerHandler) GetPartnerTypes(c *gin.Context) {
	ctx := c.Request.Context()

	resp, err := h.partnerUseCase.GetPartnerTypes(ctx)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *PartnerHandler) GetPartner(c *gin.Context) {
	ctx := c.Request.Context()
	id := c.Param("id")
	resp, err := h.partnerUseCase.GetPartner(ctx, id)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)

}

func (h *PartnerHandler) CreatePartner(c *gin.Context) {
	ctx := c.Request.Context()
	var req dtos.CreatePartner
	err := c.ShouldBindJSON(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.partnerUseCase.CreatePartner(ctx, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *PartnerHandler) CreatePartnerCustomer(c *gin.Context) {
	ctx := c.Request.Context()
	var req dtos.CreateUpdatePartnerCustomer
	err := c.ShouldBindJSON(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if accurate.UseAccurate(c) {
		resp, err := h.partnerUseCase.CreatePartnerCustomerToAccurate(ctx, req)
		if err != nil {
			httpStatus, errMessage := errorhandler.ParseToHttpError(err)
			c.JSON(httpStatus, gin.H{"error": errMessage})
			return
		}

		c.JSON(http.StatusOK, resp)
		return
	}

	resp, err := h.partnerUseCase.CreatePartnerCustomer(ctx, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *PartnerHandler) UpdatePartnerCustomer(c *gin.Context) {
	ctx := c.Request.Context()
	id := c.Param("id")
	var req dtos.CreateUpdatePartnerCustomer
	err := c.ShouldBindJSON(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if accurate.UseAccurate(c) {
		resp, err := h.partnerUseCase.UpdatePartnerCustomerToAccurate(ctx, id, req)
		if err != nil {
			httpStatus, errMessage := errorhandler.ParseToHttpError(err)
			c.JSON(httpStatus, gin.H{"error": errMessage})
			return
		}

		c.JSON(http.StatusOK, resp)
		return
	}
	resp, err := h.partnerUseCase.UpdatePartnerCustomer(ctx, id, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *PartnerHandler) UpdatePartner(c *gin.Context) {
	ctx := c.Request.Context()
	id := c.Param("id")
	var req dtos.UpdatePartner
	err := c.ShouldBindJSON(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.partnerUseCase.UpdatePartner(ctx, id, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *PartnerHandler) DeletePartner(c *gin.Context) {
	ctx := c.Request.Context()
	id := c.Param("id")

	resp, err := h.partnerUseCase.DeletetePartner(ctx, id)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}
