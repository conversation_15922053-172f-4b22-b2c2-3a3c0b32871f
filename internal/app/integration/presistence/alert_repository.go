package presistence

import (
	"assetfindr/internal/app/integration/constants"
	"assetfindr/internal/app/integration/models"
	"assetfindr/internal/app/integration/repository"
	"assetfindr/internal/errorhandler"
	"assetfindr/internal/infrastructure/database"
	"context"
	"time"

	"github.com/lib/pq"
	"gopkg.in/guregu/null.v4"
	"gorm.io/gorm"
)

type alertRepository struct{}

func NewAlertRepository() repository.AlertRepository {
	return &alertRepository{}
}

func (r *alertRepository) GetAlertConfigStatuses(ctx context.Context, dB database.DBI) ([]models.AlertConfigStatus, error) {
	alertConfigStatuses := []models.AlertConfigStatus{}
	query := dB.GetTx().Model(&alertConfigStatuses)
	err := query.Find(&alertConfigStatuses).Error
	if err != nil {
		return nil, err
	}

	return alertConfigStatuses, nil
}

func (r *alertRepository) GetAlertStateCategories(ctx context.Context, dB database.DBI) ([]models.AlertStateCategory, error) {
	alertStateCategories := []models.AlertStateCategory{}
	query := dB.GetTx().Model(&alertStateCategories)
	err := query.Find(&alertStateCategories).Error
	if err != nil {
		return nil, err
	}

	return alertStateCategories, nil
}

func (r *alertRepository) GetAlertActionTypes(ctx context.Context, dB database.DBI) ([]models.AlertActionType, error) {
	alertActionTypes := []models.AlertActionType{}
	query := dB.GetTx().Model(&alertActionTypes)
	err := query.Find(&alertActionTypes).Error
	if err != nil {
		return nil, err
	}

	return alertActionTypes, nil
}

func (r *alertRepository) GetAlertConfigCategories(ctx context.Context, dB database.DBI, assetCategoryCode string) ([]models.AlertConfigCategory, error) {
	alertConfigCategories := []models.AlertConfigCategory{}
	query := dB.GetTx().Model(&alertConfigCategories)

	if assetCategoryCode != "" {
		query.Where("asset_category_code = ?", assetCategoryCode)
	}

	err := query.Find(&alertConfigCategories).Error
	if err != nil {
		return nil, err
	}

	return alertConfigCategories, nil
}

func (r *alertRepository) GetAlertConfigSubCategories(ctx context.Context, dB database.DBI, categoryCode string) ([]models.AlertConfigSubCategory, error) {
	alertConfigSubCategories := []models.AlertConfigSubCategory{}
	query := dB.GetTx().Model(&alertConfigSubCategories)

	if categoryCode != "" {
		query.Where("category_code = ?", categoryCode)
	}

	err := query.Find(&alertConfigSubCategories).Error
	if err != nil {
		return nil, err
	}

	return alertConfigSubCategories, nil
}

func (r *alertRepository) GetAlertTriggers(ctx context.Context, dB database.DBI, subCategoryCode string) ([]models.AlertTrigger, error) {
	alertTriggers := []models.AlertTrigger{}
	query := dB.GetTx().Model(&alertTriggers)

	if subCategoryCode != "" {
		query.Where("sub_category_code = ?", subCategoryCode)
	}

	err := query.Find(&alertTriggers).Error
	if err != nil {
		return nil, err
	}

	return alertTriggers, nil
}

func (r *alertRepository) CreateAlertConfig(ctx context.Context, dB database.DBI, alertConfig *models.AlertConfig) error {
	return dB.GetTx().Create(alertConfig).Error
}

func enrichAlertConfigQueryWithWhere(query *gorm.DB, where models.AlertConfigWhere) {
	if where.ID != "" {
		query.Where("id = ?", where.ID)
	} // ID

	if where.ClientID != "" {
		query.Where("client_id = ?", where.ClientID)
	} // ClientID

	if where.AssetCategoryCode != "" {
		query.Where("asset_category_code = ?", where.AssetCategoryCode)
	} // AssetCategoryCode

	if where.AssetSubCategoryCode != "" {
		query.Where("asset_sub_category_code = ?", where.AssetSubCategoryCode)
	} // AssetSubCategoryCode

	if where.AlertCategoryCode != "" {
		query.Where("alert_category_code = ?", where.AlertCategoryCode)
	} // AlertCategoryCode

	if where.AlertSubCategoryCode != "" {
		query.Where("alert_sub_category_code = ?", where.AlertSubCategoryCode)
	} // AlertSubCategoryCode

	if len(where.AlertSubCategoryCodes) > 0 {
		query.Where("alert_sub_category_code IN ?", where.AlertSubCategoryCodes)
	} // AlertSubCategoryCodes

	if where.StatusCode != "" {
		query.Where("status_code = ?", where.StatusCode)
	} // StatusCode

	if where.AssetID != "" {
		query.Where("asset_id = ?", where.AssetID)
	} // AssetID

	if where.WithOrmDeleted {
		query.Unscoped()
	}
}

func enrichAlertConfigQueryWithPreload(query *gorm.DB, preload models.AlertConfigPreload) {
	if preload.AlertCategory {
		query.Preload("AlertCategory")
	} // AlertCategory

	if preload.Status {
		query.Preload("Status")
	} // Status

	if preload.AlertSubCategory {
		query.Preload("AlertSubCategory")
	} // AlertSubCategory

	if preload.ConfigTriggers {
		query.Preload("ConfigTriggers")
	} // ConfigTriggers

	if preload.ConfigTriggerStates {
		query.Preload("ConfigTriggers.ConfigTriggerStates")
	} // ConfigTriggerStates

	if preload.ConfigTriggersTrigger {
		query.Preload("ConfigTriggers.Trigger")
	} // ConfigTriggersTrigger
}

func (r *alertRepository) GetAlertConfig(ctx context.Context, dB database.DBI, cond models.AlertConfigCondition) (*models.AlertConfig, error) {
	integrationAccountation := models.AlertConfig{}
	query := dB.GetOrm().Model(&integrationAccountation)

	enrichAlertConfigQueryWithWhere(query, cond.Where)
	enrichAlertConfigQueryWithPreload(query, cond.Preload)

	if len(cond.Columns) > 0 {
		query.Select(cond.Columns)
	}

	err := query.First(&integrationAccountation).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errorhandler.ErrDataNotFound("alert config")
		}

		return nil, err
	}

	return &integrationAccountation, nil
}

func (r *alertRepository) GetAlertConfigs(ctx context.Context, dB database.DBI, cond models.AlertConfigCondition) ([]models.AlertConfig, error) {
	alertConfigs := []models.AlertConfig{}
	query := dB.GetOrm().Model(&alertConfigs)

	enrichAlertConfigQueryWithWhere(query, cond.Where)
	enrichAlertConfigQueryWithPreload(query, cond.Preload)

	if len(cond.Columns) > 0 {
		query.Select(cond.Columns)
	}

	err := query.Find(&alertConfigs).Error
	if err != nil {
		return nil, err
	}

	return alertConfigs, nil
}

func (r *alertRepository) GetAlertConfigList(ctx context.Context, dB database.DBI, param models.GetAlertConfigListParam) (int, []models.AlertConfig, error) {
	var totalRecords int64
	integrationAccounts := []models.AlertConfig{}
	query := dB.GetTx().Model(&integrationAccounts)
	if param.SearchKeyword != "" {
		query.Where("LOWER(name) ILIKE LOWER(?)", "%"+param.SearchKeyword+"%")
	}

	enrichAlertConfigQueryWithWhere(query, param.Cond.Where)
	enrichAlertConfigQueryWithPreload(query, param.Cond.Preload)

	err := query.Count(&totalRecords).Error
	if err != nil {
		return 0, nil, err
	}

	if totalRecords <= 0 {
		return int(totalRecords), nil, nil
	}

	query.Order("updated_at DESC")
	offset := (param.PageNo - 1) * param.PageSize
	err = query.Offset(offset).Limit(param.PageSize).Find(&integrationAccounts).Error
	if err != nil {
		return 0, nil, err
	}

	return int(totalRecords), integrationAccounts, nil
}

func enrichAlertQueryWithWhere(query *gorm.DB, where models.AlertWhere) {
	if where.ID != "" {
		query.Where("ins_alerts.id = ?", where.ID)
	} // ID

	if where.AlertConfigTriggerStateID != "" {
		query.Where("ins_alerts.alert_config_trigger_state_id = ?", where.AlertConfigTriggerStateID)
	} // AlertConfigTriggerStateID

	if where.AlertConfigTriggerID != "" {
		query.Where("ins_alerts.alert_config_trigger_id = ?", where.AlertConfigTriggerID)
	} // AlertConfigTriggerID

	if where.ClientID != "" {
		query.Where("ins_alerts.client_id = ?", where.ClientID)
	} // ClientID

	if where.StateCategoryCode != "" {
		query.Where("ins_alerts.state_category_code = ?", where.StateCategoryCode)
	} // StateCategoryCode

	if where.TriggerCode != "" {
		query.Where("ins_alerts.trigger_code = ?", where.TriggerCode)
	} // TriggerCode

	if where.AssetID != "" {
		query.Where("ins_alerts.asset_id = ? OR ins_alerts.related_asset_id = ?", where.AssetID, where.AssetID)
	} // AssetID

	if len(where.AssetIDs) > 0 {
		query.Where("ins_alerts.asset_id IN ? OR ins_alerts.related_asset_id IN ?", where.AssetIDs, where.AssetIDs)
	} // AssetIDs

	if where.AlertConfigCategoryCode != "" {
		query.Where("ins_alert_configs.alert_config_category_code = ?", where.AlertConfigCategoryCode)
	} // AlertConfigCategoryCode

	if where.WithOrmDeleted {
		query.Unscoped()
	}

	if where.InLastDefaultResetTime {
		query.Where("ins_alerts.created_at > NOW() - INTERVAL '?'", gorm.Expr("1 days"))
	} // InLast7Days

	if where.IsRead.Valid {
		query.Where("ins_alerts.is_read = ?", where.IsRead.Bool)
	} // IsRead

	if !where.CreatedAtFrom.IsZero() {
		query.Where("ins_alerts.created_at >= ?", where.CreatedAtFrom)
	}
}

func enrichAlertQueryWithPreload(query *gorm.DB, preload models.AlertPreload) {
	if preload.AlertConfig {
		query.Preload("AlertConfig")
	}

	if preload.AlertConfigCategory {
		query.Preload("AlertConfig.AlertCategory")
	}

	if preload.AlertConfigSubCategory {
		query.Preload("AlertConfig.AlertSubCategory")
	}

	if preload.StateCategory {
		query.Preload("StateCategory")
	}

	if preload.Trigger {
		query.Preload("Trigger")
	}

}

func (r *alertRepository) GetAlert(ctx context.Context, dB database.DBI, cond models.AlertCondition) (*models.Alert, error) {
	integrationAccountation := models.Alert{}
	query := dB.GetOrm().Model(&integrationAccountation)

	enrichAlertQueryWithWhere(query, cond.Where)
	enrichAlertQueryWithPreload(query, cond.Preload)

	if len(cond.Columns) > 0 {
		query.Select(cond.Columns)
	}

	err := query.First(&integrationAccountation).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errorhandler.ErrDataNotFound("integration account")
		}

		return nil, err
	}

	return &integrationAccountation, nil
}

func (r *alertRepository) GetAlertList(ctx context.Context, dB database.DBI, param models.GetAlertListParam) (int, []models.Alert, error) {
	var totalRecords int64
	integrationAccounts := []models.Alert{}
	query := dB.GetTx().Model(&integrationAccounts).
		Joins("JOIN ins_alert_configs ON ins_alert_configs.id = ins_alerts.alert_config_id")

	if param.SearchKeyword != "" {
		query.Where("LOWER(ins_alert_configs.name) ILIKE LOWER(?)", "%"+param.SearchKeyword+"%")
	}

	enrichAlertQueryWithWhere(query, param.Cond.Where)
	enrichAlertQueryWithPreload(query, param.Cond.Preload)

	err := query.Count(&totalRecords).Error
	if err != nil {
		return 0, nil, err
	}

	if totalRecords <= 0 {
		return int(totalRecords), nil, nil
	}

	query.Order("created_at DESC")
	offset := (param.PageNo - 1) * param.PageSize
	err = query.Offset(offset).Limit(param.PageSize).Find(&integrationAccounts).Error
	if err != nil {
		return 0, nil, err
	}

	return int(totalRecords), integrationAccounts, nil
}

func (r *alertRepository) UpdateAlertConfig(ctx context.Context, dB database.DBI, id string, alertConfig *models.AlertConfig) error {
	return dB.GetTx().
		Model(&models.AlertConfig{}).
		Where("id = ?", id).
		Updates(alertConfig).
		Error
}

func (r *alertRepository) UpdateAlertConfigTrigger(ctx context.Context, dB database.DBI, id string, alertConfigTrigger *models.AlertConfigTrigger) error {
	return dB.GetTx().
		Model(&models.AlertConfigTrigger{}).
		Where("id = ?", id).
		Updates(alertConfigTrigger).
		Error
}

func (r *alertRepository) UpdateAlertConfigTriggerState(ctx context.Context, dB database.DBI, id string, alertConfigTriggerState *models.AlertConfigTriggerState) error {
	return dB.GetTx().
		Model(&models.AlertConfigTriggerState{}).
		Where("id = ?", id).
		Updates(alertConfigTriggerState).
		Error
}

func (r *alertRepository) DeleteAlertConfigTriggers(ctx context.Context, dB database.DBI, ids []string) error {
	if len(ids) == 0 {
		return nil
	}

	return dB.GetTx().Delete(&models.AlertConfigTrigger{}, ids).Error
}

func (r *alertRepository) DeleteAlertConfigTriggerStates(ctx context.Context, dB database.DBI, ids []string) error {
	if len(ids) == 0 {
		return nil
	}

	return dB.GetTx().Delete(&models.AlertConfigTriggerState{}, ids).Error
}

func (r *alertRepository) DeleteAlertConfigTriggerStatesByConfigTriggerIDs(ctx context.Context, dB database.DBI, configTriggerIDs []string) error {
	if len(configTriggerIDs) == 0 {
		return nil
	}

	return dB.GetTx().Delete(&models.AlertConfigTriggerState{}, "alert_config_trigger_id IN ?", configTriggerIDs).Error
}

func (r *alertRepository) CreateAlertConfigTrigger(ctx context.Context, dB database.DBI, alertConfigTriggers []models.AlertConfigTrigger) error {
	if len(alertConfigTriggers) == 0 {
		return nil
	}

	return dB.GetTx().Create(&alertConfigTriggers).Error
}

func (r *alertRepository) CreateAlertConfigTriggerState(ctx context.Context, dB database.DBI, alertConfigTriggerStates []models.AlertConfigTriggerState) error {
	if len(alertConfigTriggerStates) == 0 {
		return nil
	}

	return dB.GetTx().Create(&alertConfigTriggerStates).Error
}

func (r *alertRepository) CreateAlert(ctx context.Context, dB database.DBI, alert *models.Alert) error {
	return dB.GetTx().Create(alert).Error
}

func (r *alertRepository) UpdateAlertTicketID(ctx context.Context, dB database.DBI, id string, ticketID string) error {
	return dB.GetTx().
		Model(&models.Alert{}).
		Where("id = ?", id).
		Update("ticket_id", ticketID).
		Error
}

func (r *alertRepository) DeleteAlertConfigByID(ctx context.Context, dB database.DBI, id string) error {
	return dB.GetTx().Delete(&models.AlertConfig{}, "id = ?", id).Error
}

func (r *alertRepository) DeleteAlertConfigTriggerByIDs(ctx context.Context, dB database.DBI, ids []string) error {
	if len(ids) == 0 {
		return nil
	}

	return dB.GetTx().Delete(&models.AlertConfigTrigger{}, "id IN ?", ids).Error
}

func (r *alertRepository) DeleteAlertConfigTriggerStateByIDs(ctx context.Context, dB database.DBI, ids []string) error {
	if len(ids) == 0 {
		return nil
	}

	return dB.GetTx().Delete(&models.AlertConfigTriggerState{}, "id IN ?", ids).Error
}

func (r *alertRepository) SetAlertsAsRead(ctx context.Context, dB database.DBI, IDs []string, clientID string) error {
	now := time.Now()
	return dB.GetTx().
		Model(&models.Alert{}).
		Where("id IN ?", IDs).
		Where("client_id = ?", clientID).
		Where("is_read IS FALSE").
		Updates(&models.Alert{
			ReadDatetime: &now,
			IsRead:       true,
		}).
		Error
}

func (r *alertRepository) MarkAllAlertsAsReadByAssetID(ctx context.Context, dB database.DBI, assetID string, clientID string) error {
	now := time.Now()
	return dB.GetTx().
		Model(&models.Alert{}).
		Where("asset_id = ? OR related_asset_id = ?", assetID, assetID).
		Where("client_id = ?", clientID).
		Where("is_read IS FALSE").
		Updates(&models.Alert{
			ReadDatetime: &now,
			IsRead:       true,
		}).
		Error
}

func (r *alertRepository) GetAlertCountByAssetIDs(ctx context.Context, dB database.DBI, assetIDs []string) ([]models.AlertCountByAsset, error) {
	query := dB.GetTx().
		Model(&models.Alert{}).
		Select("asset_id, count(id) as count").
		Where("is_read IS FALSE").
		Where("asset_id IN ?", assetIDs).
		Group("asset_id")

	results := []models.AlertCountByAsset{}
	err := query.Find(&results).Error
	if err != nil {
		return nil, err
	}

	return results, nil
}

func (r *alertRepository) GetAlertCountByRelatedAssetIDs(ctx context.Context, dB database.DBI, relatedAssetIDs []string) ([]models.AlertCountByAsset, error) {
	query := dB.GetTx().
		Model(&models.Alert{}).
		Select("related_asset_id AS asset_id, count(id) as count").
		Where("is_read IS FALSE").
		Where("related_asset_id IN ?", relatedAssetIDs).
		Group("related_asset_id")

	results := []models.AlertCountByAsset{}
	err := query.Find(&results).Error
	if err != nil {
		return nil, err
	}

	return results, nil
}

func (r *alertRepository) CountAlert(ctx context.Context, dB database.DBI, condition models.AlertCondition) (int, error) {
	query := dB.GetTx().Model(&models.Alert{})
	enrichAlertQueryWithWhere(query, condition.Where)

	if condition.Where.DistinctAssetID {
		query.Distinct("asset_id")
	}

	var total int64
	err := query.Count(&total).Error
	if err != nil {
		return 0, err
	}

	return int(total), nil
}

func enrichAlertParameterQueryWithWhere(query *gorm.DB, where models.AlertParameterWhere) {
	if where.Code != "" {
		query.Where("id = ?", where.Code)
	} // ID

	if where.WithOrmDeleted {
		query.Unscoped()
	}

	if len(where.SourceCodes) > 0 {
		query.Where("source_codes && ?", pq.Array(where.SourceCodes))
	}
}

func enrichAlertParameterQueryWithPreload(query *gorm.DB, preload models.AlertParameterPreload) {

}

func (r *alertRepository) GetAlertParameterList(ctx context.Context, dB database.DBI, param models.GetAlertParameterListParam) (int, []models.AlertParameter, error) {
	var totalRecords int64
	alertParameters := []models.AlertParameter{}
	query := dB.GetTx().Model(&alertParameters)

	if param.SearchKeyword != "" {
		query.Where("LOWER(label) ILIKE LOWER(?)", "%"+param.SearchKeyword+"%")
	}

	enrichAlertParameterQueryWithWhere(query, param.Cond.Where)
	enrichAlertParameterQueryWithPreload(query, param.Cond.Preload)

	err := query.Count(&totalRecords).Error
	if err != nil {
		return 0, nil, err
	}

	if totalRecords <= 0 {
		return int(totalRecords), nil, nil
	}

	offset := (param.PageNo - 1) * param.PageSize
	err = query.Offset(offset).Limit(param.PageSize).Find(&alertParameters).Error
	if err != nil {
		return 0, nil, err
	}

	return int(totalRecords), alertParameters, nil
}

func enrichAlertConditionOperatorQueryWithWhere(query *gorm.DB, where models.AlertConditionOperatorWhere) {
	if where.Code != "" {
		query.Where("id = ?", where.Code)
	} // ID

	if where.WithOrmDeleted {
		query.Unscoped()
	}

	if len(where.DataTypes) > 0 {
		query.Where("data_types && ?", pq.Array(where.DataTypes))
	}
}

func enrichAlertConditionOperatorQueryWithPreload(query *gorm.DB, preload models.AlertConditionOperatorPreload) {

}

func (r *alertRepository) GetAlertConditionOperatorList(ctx context.Context, dB database.DBI, param models.GetAlertConditionOperatorListParam) (int, []models.AlertConditionOperator, error) {
	var totalRecords int64
	alertConditionOperators := []models.AlertConditionOperator{}
	query := dB.GetTx().Model(&alertConditionOperators)

	if param.SearchKeyword != "" {
		query.Where("LOWER(label) ILIKE LOWER(?)", "%"+param.SearchKeyword+"%")
	}

	enrichAlertConditionOperatorQueryWithWhere(query, param.Cond.Where)
	enrichAlertConditionOperatorQueryWithPreload(query, param.Cond.Preload)

	err := query.Count(&totalRecords).Error
	if err != nil {
		return 0, nil, err
	}

	if totalRecords <= 0 {
		return int(totalRecords), nil, nil
	}

	offset := (param.PageNo - 1) * param.PageSize
	err = query.Offset(offset).Limit(param.PageSize).Find(&alertConditionOperators).Error
	if err != nil {
		return 0, nil, err
	}

	return int(totalRecords), alertConditionOperators, nil
}

func (r *alertRepository) CreateAlertConfigV2(ctx context.Context, dB database.DBI, alertConfig *models.AlertConfigV2) error {
	return dB.GetTx().Create(alertConfig).Error
}

func enrichAlertConfigV2QueryWithWhere(query *gorm.DB, where models.AlertConfigV2Where) {
	if where.ID != "" {
		query.Where("id = ?", where.ID)
	} // ID

	if where.ClientID != "" {
		query.Where("client_id = ?", where.ClientID)
	} // ClientID

	if len(where.AssetIDs) > 0 {
		query.Where("asset_ids && ?", pq.Array(where.AssetIDs))
	}

	if where.StatusCode != "" {
		query.Where("status_code = ?", where.StatusCode)
	}

	if where.NotHappenOnAssetID != "" {
		query.Where("? != ALL(happen_on_asset_ids) OR happen_on_asset_ids IS NULL", where.NotHappenOnAssetID)
	}

	if where.WithOrmDeleted {
		query.Unscoped()
	}
}

func enrichAlertConfigV2QueryWithPreload(query *gorm.DB, preload models.AlertConfigV2Preload) {
	if preload.Conditions {
		query.Preload("Conditions")
	}

	if preload.ConditionsParameter {
		query.Preload("Conditions.Parameter")
	}
	if preload.ConditionsOperator {
		query.Preload("Conditions.Operator")
	}
}

func (r *alertRepository) GetAlertConfigV2(ctx context.Context, dB database.DBI, cond models.AlertConfigV2Cond) (*models.AlertConfigV2, error) {
	integrationAccountation := models.AlertConfigV2{}
	query := dB.GetOrm().Model(&integrationAccountation)

	enrichAlertConfigV2QueryWithWhere(query, cond.Where)
	enrichAlertConfigV2QueryWithPreload(query, cond.Preload)

	if len(cond.Columns) > 0 {
		query.Select(cond.Columns)
	}

	err := query.First(&integrationAccountation).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errorhandler.ErrDataNotFound("alert config")
		}

		return nil, err
	}

	return &integrationAccountation, nil
}

func (r *alertRepository) GetAlertConfigV2s(ctx context.Context, dB database.DBI, cond models.AlertConfigV2Cond) ([]models.AlertConfigV2, error) {
	alertConfigs := []models.AlertConfigV2{}
	query := dB.GetOrm().Model(&alertConfigs)

	enrichAlertConfigV2QueryWithWhere(query, cond.Where)
	enrichAlertConfigV2QueryWithPreload(query, cond.Preload)

	if len(cond.Columns) > 0 {
		query.Select(cond.Columns)
	}

	err := query.Find(&alertConfigs).Error
	if err != nil {
		return nil, err
	}

	return alertConfigs, nil
}

func (r *alertRepository) GetAlertConfigV2List(ctx context.Context, dB database.DBI, param models.GetAlertConfigV2ListParam) (int, []models.AlertConfigV2, error) {
	var totalRecords int64
	integrationAccounts := []models.AlertConfigV2{}
	query := dB.GetTx().Model(&integrationAccounts)
	if param.SearchKeyword != "" {
		query.Where("LOWER(name) ILIKE LOWER(?)", "%"+param.SearchKeyword+"%")
	}

	enrichAlertConfigV2QueryWithWhere(query, param.Cond.Where)
	enrichAlertConfigV2QueryWithPreload(query, param.Cond.Preload)

	err := query.Count(&totalRecords).Error
	if err != nil {
		return 0, nil, err
	}

	if totalRecords <= 0 {
		return int(totalRecords), nil, nil
	}

	query.Order("updated_at DESC")
	offset := (param.PageNo - 1) * param.PageSize
	err = query.Offset(offset).Limit(param.PageSize).Find(&integrationAccounts).Error
	if err != nil {
		return 0, nil, err
	}

	return int(totalRecords), integrationAccounts, nil
}

func (r *alertRepository) DeleteAlertConfigV2(ctx context.Context, dB database.DBI, id string) error {
	return dB.GetTx().Delete(&models.AlertConfigV2{}, "id = ?", id).Error
}

func (r *alertRepository) DeleteAlertConfigV2Conditions(ctx context.Context, dB database.DBI, ids []string) error {
	if len(ids) == 0 {
		return nil
	}

	return dB.GetTx().Delete(&models.AlertConfigV2Condition{}, ids).Error
}

func (r *alertRepository) UpdateAlertConfigV2(ctx context.Context, dB database.DBI, id string, alertConfig *models.AlertConfigV2) error {
	return dB.GetTx().
		Where("id = ?", id).
		Updates(alertConfig).
		Error
}

func (r *alertRepository) ClearAlertConfigHappenOnAssetIDs(ctx context.Context, dB database.DBI, id string) error {
	return dB.GetTx().
		Model(&models.AlertConfigV2{}).
		Where("id = ?", id).
		Update("HappenOnAssetIDs", pq.Array([]string{})).Error
}

func (r *alertRepository) DiactivateAlertConfigByID(ctx context.Context, dB database.DBI, id string) error {
	return dB.GetTx().
		Model(&models.AlertConfigV2{}).
		Where("id = ?", id).
		Update("status_code", constants.ALERT_CONFIG_STATUS_CODE_INACTIVE).Error
}

func (r *alertRepository) AppendHappenOnAssetIDAlertConfigV2(ctx context.Context, dB database.DBI, id string, assetID string) error {
	return dB.GetTx().
		Model(&models.AlertConfigV2{}).
		Where("id = ?", id).
		Updates(map[string]interface{}{
			"happen_on_asset_ids": gorm.Expr("happen_on_asset_ids || ?", pq.Array([]string{assetID})),
		}).
		Error
}

func (r *alertRepository) UpdateAlertConfigV2Condition(ctx context.Context, dB database.DBI, id string, alertConfigCondition *models.AlertConfigV2Condition) error {
	return dB.GetTx().
		Where("id = ?", id).
		Updates(alertConfigCondition).
		Error
}

func (r *alertRepository) CreateAlertConfigV2Conditions(ctx context.Context, dB database.DBI, alertConfigV2Conditions []models.AlertConfigV2Condition) error {
	if len(alertConfigV2Conditions) == 0 {
		return nil
	}

	return dB.GetTx().Create(&alertConfigV2Conditions).Error
}

func (r *alertRepository) CreateAlertV2(ctx context.Context, dB database.DBI, alert *models.AlertV2) error {
	return dB.GetTx().Create(alert).Error
}

func enrichAlertV2QueryWithWhere(query *gorm.DB, where models.AlertV2Where) {
	if where.ID != "" {
		query.Where("ins_alerts_v2.id = ?", where.ID)
	} // ID

	if where.ClientID != "" {
		query.Where("ins_alerts_v2.client_id = ?", where.ClientID)
	} // ClientID

	if where.AssetID != "" {
		query.Where("ins_alerts_v2.asset_id = ?", where.AssetID)
	}

	if len(where.AssetIDs) > 0 {
		query.Where("ins_alerts_v2.asset_id IN ?", where.AssetIDs)
	}

	if where.AlertConfigID != "" {
		query.Where("ins_alerts_v2.alert_config_id = ?", where.AlertConfigID)
	}

	if where.InLastDefaultResetTime {
		query.Where("ins_alerts_v2.created_at > NOW() - INTERVAL '?'", gorm.Expr("1 days"))
	} // InLast7Days

	if where.IsRead.Valid {
		query.Where("ins_alerts_v2.is_read = ?", where.IsRead.Bool)
	} // IsRead

	if where.WithOrmDeleted {
		query.Unscoped()
	}

	if len(where.AssetCategories) > 0 {
		query.Where("ins_alerts_v2.asset_category_code IN ?", where.AssetCategories)
	}

	if len(where.ExcludedAssetCategories) > 0 {
		query.Where("ins_alerts_v2.asset_category_code NOT IN ?", where.ExcludedAssetCategories)
	}
}

func enrichAlertV2QueryWithPreload(query *gorm.DB, preload models.AlertV2Preload) {
	if preload.AlertConfig {
		query.Preload("AlertConfig")
	}
}

func (r *alertRepository) GetAlertV2(ctx context.Context, dB database.DBI, cond models.AlertV2Cond) (*models.AlertV2, error) {
	integrationAccountation := models.AlertV2{}
	query := dB.GetOrm().Model(&integrationAccountation)

	enrichAlertV2QueryWithWhere(query, cond.Where)
	enrichAlertV2QueryWithPreload(query, cond.Preload)

	if len(cond.Columns) > 0 {
		query.Select(cond.Columns)
	}

	err := query.First(&integrationAccountation).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errorhandler.ErrDataNotFound("alert config")
		}

		return nil, err
	}

	return &integrationAccountation, nil
}

func (r *alertRepository) GetAlertV2s(ctx context.Context, dB database.DBI, cond models.AlertV2Cond) ([]models.AlertV2, error) {
	alertConfigs := []models.AlertV2{}
	query := dB.GetOrm().Model(&alertConfigs)

	enrichAlertV2QueryWithWhere(query, cond.Where)
	enrichAlertV2QueryWithPreload(query, cond.Preload)

	if len(cond.Columns) > 0 {
		query.Select(cond.Columns)
	}

	err := query.Find(&alertConfigs).Error
	if err != nil {
		return nil, err
	}

	return alertConfigs, nil
}

func (r *alertRepository) GetAlertV2List(ctx context.Context, dB database.DBI, param models.GetAlertV2ListParam) (int, []models.AlertV2, error) {
	var totalRecords int64
	alerts := []models.AlertV2{}
	query := dB.GetTx().Model(&alerts).
		Joins("JOIN ins_alert_configs_v2 ON ins_alert_configs_v2.id = ins_alerts_v2.alert_config_id")

	if param.SearchKeyword != "" {
		query.Where("LOWER(ins_alert_configs_v2.name) ILIKE LOWER(?)", "%"+param.SearchKeyword+"%")
	}

	enrichAlertV2QueryWithWhere(query, param.Cond.Where)
	enrichAlertV2QueryWithPreload(query, param.Cond.Preload)

	err := query.Count(&totalRecords).Error
	if err != nil {
		return 0, nil, err
	}

	if totalRecords <= 0 {
		return int(totalRecords), nil, nil
	}

	query.Order("updated_at DESC")
	offset := (param.PageNo - 1) * param.PageSize
	err = query.Offset(offset).Limit(param.PageSize).Find(&alerts).Error
	if err != nil {
		return 0, nil, err
	}

	return int(totalRecords), alerts, nil
}

func (r *alertRepository) SetAlertV2sAsRead(ctx context.Context, dB database.DBI, IDs []string, clientID string) error {
	now := time.Now()
	return dB.GetTx().
		Where("id IN ?", IDs).
		Where("client_id = ?", clientID).
		Where("is_read IS FALSE").
		Updates(&models.AlertV2{
			ReadDatetime: null.TimeFrom(now),
			IsRead:       true,
		}).
		Error
}

func (r *alertRepository) MarkAllAlertV2sAsReadByAssetID(ctx context.Context, dB database.DBI, assetID string, clientID string) error {
	now := time.Now()
	return dB.GetTx().
		Where("asset_id = ?", assetID).
		Where("client_id = ?", clientID).
		Where("is_read IS FALSE").
		Updates(&models.AlertV2{
			ReadDatetime: null.TimeFrom(now),
			IsRead:       true,
		}).
		Error
}

func (r *alertRepository) UpdateAlertV2(ctx context.Context, dB database.DBI, id string, alert *models.AlertV2) error {
	return dB.GetTx().
		Where("id = ?", id).
		Updates(alert).
		Error
}

func (r *alertRepository) CountAssetAlertV2(ctx context.Context, dB database.DBI, condition models.AlertV2Cond) (int, error) {
	var totalRecords int64
	query := dB.GetTx().Model(&models.AlertV2{})

	enrichAlertV2QueryWithWhere(query, condition.Where)

	err := query.Count(&totalRecords).Error
	if err != nil {
		return 0, err
	}

	return int(totalRecords), nil
}
