package usecase

import (
	assetModel "assetfindr/internal/app/asset/models"
	assetRepo "assetfindr/internal/app/asset/repository"
	geoRepo "assetfindr/internal/app/geo/repository"
	"assetfindr/internal/app/integration/constants"
	"assetfindr/internal/app/integration/dtos"
	"assetfindr/internal/app/integration/models"
	notificationUsecase "assetfindr/internal/app/notification/usecase"

	"assetfindr/internal/app/integration/repository"
	taskConstants "assetfindr/internal/app/task/constants"
	taskModel "assetfindr/internal/app/task/models"
	taskRepo "assetfindr/internal/app/task/repository"
	userModel "assetfindr/internal/app/user-identity/models"
	userIdentityRepository "assetfindr/internal/app/user-identity/repository"
	"assetfindr/internal/infrastructure/database"
	"assetfindr/pkg/common/commonmodel"
	"assetfindr/pkg/common/helpers/authhelpers"
	"context"

	"gopkg.in/guregu/null.v4"
)

type AlertUseCase struct {
	DB                  database.DBUsecase
	DBTimeScale         database.DBUsecase
	alertRepo           repository.AlertRepository
	gpsidRepo           repository.GPSIDRepository
	assetVehicleRepo    assetRepo.AssetVehicleRepository
	trackingRepo        geoRepo.TrackingRepository
	assetAssignmentRepo assetRepo.AssetAssignmentRepository
	userRepo            userIdentityRepository.UserRepository
	assetRepo           assetRepo.AssetRepository
	accurateRepo        repository.AccurateRepository
	clientRepo          userIdentityRepository.ClientRepository
	ticketRepo          taskRepo.TicketRepository
	notifUseCase        notificationUsecase.NotificationUseCase
	assetLinkedRepo     assetRepo.AssetLinkedRepository
}

func NewAlertUsecase(
	DB database.DBUsecase,
	DBTimeScale database.DBUsecase,
	alertRepo repository.AlertRepository,
	gpsidRepo repository.GPSIDRepository,
	trackingRepo geoRepo.TrackingRepository,
	assetVehicleRepo assetRepo.AssetVehicleRepository,
	assetAssignmentRepo assetRepo.AssetAssignmentRepository,
	userRepo userIdentityRepository.UserRepository,
	assetRepo assetRepo.AssetRepository,
	accurateRepo repository.AccurateRepository,
	clientRepo userIdentityRepository.ClientRepository,
	ticketRepo taskRepo.TicketRepository,
	notifUseCase notificationUsecase.NotificationUseCase,
	assetLinkedRepo assetRepo.AssetLinkedRepository,
) AlertUseCase {
	return AlertUseCase{
		DB:                  DB,
		DBTimeScale:         DBTimeScale,
		alertRepo:           alertRepo,
		gpsidRepo:           gpsidRepo,
		trackingRepo:        trackingRepo,
		assetVehicleRepo:    assetVehicleRepo,
		assetAssignmentRepo: assetAssignmentRepo,
		userRepo:            userRepo,
		assetRepo:           assetRepo,
		accurateRepo:        accurateRepo,
		clientRepo:          clientRepo,
		ticketRepo:          ticketRepo,
		notifUseCase:        notifUseCase,
		assetLinkedRepo:     assetLinkedRepo,
	}
}

func (uc *AlertUseCase) GetAlertConfigStatuses(ctx context.Context) (*commonmodel.ListResponse, error) {
	result, err := uc.alertRepo.GetAlertConfigStatuses(ctx, uc.DB.DB())
	if err != nil {
		return nil, err
	}

	return &commonmodel.ListResponse{
		TotalRecords: len(result),
		PageSize:     len(result),
		PageNo:       1,
		Data:         result,
	}, nil
}

func (uc *AlertUseCase) GetAlertStateCategories(ctx context.Context) (*commonmodel.ListResponse, error) {
	result, err := uc.alertRepo.GetAlertStateCategories(ctx, uc.DB.DB())
	if err != nil {
		return nil, err
	}

	return &commonmodel.ListResponse{
		TotalRecords: len(result),
		PageSize:     len(result),
		PageNo:       1,
		Data:         result,
	}, nil
}

func (uc *AlertUseCase) GetAlertActionTypes(ctx context.Context) (*commonmodel.ListResponse, error) {
	result, err := uc.alertRepo.GetAlertActionTypes(ctx, uc.DB.DB())
	if err != nil {
		return nil, err
	}

	return &commonmodel.ListResponse{
		TotalRecords: len(result),
		PageSize:     len(result),
		PageNo:       1,
		Data:         result,
	}, nil
}

func (uc *AlertUseCase) GetAlertConfigCategories(ctx context.Context, req dtos.GetAlertConfigCategoriesReq) (*commonmodel.ListResponse, error) {
	result, err := uc.alertRepo.GetAlertConfigCategories(ctx, uc.DB.DB(), req.AssetCategoryCode)
	if err != nil {
		return nil, err
	}

	return &commonmodel.ListResponse{
		TotalRecords: len(result),
		PageSize:     len(result),
		PageNo:       1,
		Data:         result,
	}, nil
}

func (uc *AlertUseCase) GetAlertConfigSubCategories(ctx context.Context, req dtos.GetAlertConfigSubCategoriesReq) (*commonmodel.ListResponse, error) {
	result, err := uc.alertRepo.GetAlertConfigSubCategories(ctx, uc.DB.DB(), req.CategoryCode)
	if err != nil {
		return nil, err
	}

	return &commonmodel.ListResponse{
		TotalRecords: len(result),
		PageSize:     len(result),
		PageNo:       1,
		Data:         result,
	}, nil
}

func (uc *AlertUseCase) GetAlertTriggers(ctx context.Context, req dtos.GetAlertTriggersReq) (*commonmodel.ListResponse, error) {
	result, err := uc.alertRepo.GetAlertTriggers(ctx, uc.DB.DB(), req.SubCategoryCode)
	if err != nil {
		return nil, err
	}

	return &commonmodel.ListResponse{
		TotalRecords: len(result),
		PageSize:     len(result),
		PageNo:       1,
		Data:         result,
	}, nil
}

func (uc *AlertUseCase) CreateAlertConfig(ctx context.Context, req dtos.AlertConfigReq) (*commonmodel.CreateResponse, error) {
	alertConfig := &models.AlertConfig{
		AssetCategoryCode:    req.AssetCategoryCode,
		AssetSubCategoryCode: req.AssetSubCategoryCode,
		AlertCategoryCode:    req.AlertCategoryCode,
		AlertSubCategoryCode: req.AlertSubCategoryCode,
		Name:                 req.Name,
		Description:          req.Description,
		StatusCode:           req.StatusCode,
		AssetID:              req.AssetID,
		ConfigTriggers:       []models.AlertConfigTrigger{},
	}

	for i := range req.ConfigTriggers {
		configTriggerReq := req.ConfigTriggers[i]
		configTrigger := models.AlertConfigTrigger{
			TriggerCode:         configTriggerReq.TriggerCode,
			ConfigTriggerStates: []models.AlertConfigTriggerState{},
		}

		for j := range configTriggerReq.ConfigTriggerStates {
			configTrigger.ConfigTriggerStates = append(configTrigger.ConfigTriggerStates, models.AlertConfigTriggerState{
				StateCategoryCode:  configTriggerReq.ConfigTriggerStates[j].StateCategoryCode,
				Value:              configTriggerReq.ConfigTriggerStates[j].Value,
				ActionTypeCodes:    configTriggerReq.ConfigTriggerStates[j].ActionTypeCodes,
				AutoCreateTicket:   null.BoolFrom(configTriggerReq.ConfigTriggerStates[j].AutoCreateTicket),
				TicketPriorityCode: null.StringFrom(configTriggerReq.ConfigTriggerStates[j].TicketPriorityCode),
				AssignedUserID:     configTriggerReq.ConfigTriggerStates[j].AssignedUserID,
			})
		}

		alertConfig.ConfigTriggers = append(alertConfig.ConfigTriggers, configTrigger)
	}
	err := uc.alertRepo.CreateAlertConfig(ctx, uc.DB.WithCtx(ctx).DB(), alertConfig)
	if err != nil {
		return nil, err
	}

	return &commonmodel.CreateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: alertConfig.ID,
		Data:        nil,
	}, nil
}

func (uc *AlertUseCase) GetAlertConfigs(ctx context.Context, req dtos.AlertConfigListReq) (*commonmodel.ListResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	count, alertConfigs, err := uc.alertRepo.GetAlertConfigList(ctx, uc.DB.DB(), models.GetAlertConfigListParam{
		ListRequest: req.ListRequest,
		Cond: models.AlertConfigCondition{
			Where: models.AlertConfigWhere{
				ClientID:             claim.GetLoggedInClientID(),
				AssetCategoryCode:    req.AssetCategoryCode,
				AssetSubCategoryCode: req.AssetSubCategoryCode,
				AlertCategoryCode:    req.AlertCategoryCode,
				AlertSubCategoryCode: req.AlertSubCategoryCode,
				AssetID:              req.AssetID,
				StatusCode:           req.StatusCode,
			},
			Preload: models.AlertConfigPreload{
				AlertCategory:    true,
				AlertSubCategory: true,
				Status:           true,
			},
		},
	})
	if err != nil {
		return nil, err
	}

	respData := make([]dtos.AlertConfig, 0, len(alertConfigs))
	for _, alertConfig := range alertConfigs {
		respData = append(respData, dtos.AlertConfig{
			ID:                   alertConfig.ID,
			AssetCategoryCode:    alertConfig.AssetCategoryCode,
			AssetSubCategoryCode: alertConfig.AssetSubCategoryCode,
			AlertCategoryCode:    alertConfig.AlertCategoryCode,
			AlertSubCategoryCode: alertConfig.AlertSubCategoryCode,
			Name:                 alertConfig.Name,
			Description:          alertConfig.Description,
			StatusCode:           alertConfig.StatusCode,
			Status:               commonmodel.ConstantModel(alertConfig.Status),
			AlertCategory: dtos.AlertConfigCategory{
				Code:              alertConfig.AlertCategory.Code,
				Description:       alertConfig.AlertCategory.Description,
				Label:             alertConfig.AlertCategory.Label,
				AssetCategoryCode: alertConfig.AlertCategory.AssetCategoryCode,
			},
			AlertSubCategory: dtos.AlertConfigSubCategory{
				Code:         alertConfig.AlertSubCategory.Code,
				CategoryCode: alertConfig.AlertSubCategory.CategoryCode,
				UomCodes:     alertConfig.AlertSubCategory.UomCodes,
				Description:  alertConfig.AlertSubCategory.Description,
				Label:        alertConfig.AlertSubCategory.Label,
			},
		})
	}

	return &commonmodel.ListResponse{
		TotalRecords: count,
		PageSize:     req.PageSize,
		PageNo:       req.PageNo,
		Data:         respData,
	}, nil
}

func (uc *AlertUseCase) GetAlerts(ctx context.Context, req dtos.AlertListReq) (*commonmodel.ListResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	count, alerts, err := uc.alertRepo.GetAlertList(ctx, uc.DB.DB(), models.GetAlertListParam{
		ListRequest: req.ListRequest,
		Cond: models.AlertCondition{
			Where: models.AlertWhere{
				ClientID:                claim.GetLoggedInClientID(),
				StateCategoryCode:       req.StateCategoryCode,
				TriggerCode:             req.TriggerCode,
				AssetID:                 req.AssetID,
				AssetIDs:                req.AssetIDs,
				AlertConfigCategoryCode: req.AlertConfigCategoryCode,
			},
			Preload: models.AlertPreload{
				AlertConfig:            true,
				AlertConfigCategory:    true,
				AlertConfigSubCategory: true,
				StateCategory:          true,
				Trigger:                false,
			},
		},
	})
	if err != nil {
		return nil, err
	}

	assetIDs := []string{}
	ticketIDs := []string{}

	for i := range alerts {
		assetIDs = append(assetIDs, alerts[i].AssetID)
		if alerts[i].TicketID != "" {
			ticketIDs = append(ticketIDs, alerts[i].TicketID)
		}
	}

	assets, err := uc.assetRepo.GetAssets(ctx, uc.DB.DB(), assetModel.AssetCondition{
		Where: assetModel.AssetWhere{
			IDs: assetIDs,
		},
	})
	if err != nil {
		return nil, err
	}

	tickets, err := uc.ticketRepo.GetTicketsV2(ctx, uc.DB.DB(), taskModel.TicketCondition{
		Where: taskModel.TicketWhere{
			IDs: ticketIDs,
		},
		Preload: taskModel.TicketPreload{
			TicketStatus: true,
		},
	})
	if err != nil {
		return nil, err
	}

	mapAssets := map[string]assetModel.Asset{}
	for i := range assets {
		mapAssets[assets[i].ID] = assets[i]
	}

	mapTickets := map[string]taskModel.Ticket{}
	for i := range tickets {
		mapTickets[tickets[i].ID] = tickets[i]
	}

	respData := make([]dtos.Alert, 0, len(alerts))
	for _, alert := range alerts {
		asset := mapAssets[alert.AssetID]
		ticket := mapTickets[alert.TicketID]
		alert := dtos.Alert{
			ID:                        alert.ID,
			AlertConfigTriggerStateID: alert.AlertConfigTriggerStateID,
			AlertConfigTriggerID:      alert.AlertConfigTriggerID,
			AlertConfigID:             alert.AlertConfigID,
			StateCategoryCode:         alert.StateCategoryCode,
			TriggerCode:               alert.TriggerCode,
			Time:                      alert.Time,
			RecordedValue:             alert.RecordedValue,
			AssetID:                   alert.AssetID,
			TicketID:                  alert.TicketID,
			ReadDatetime:              alert.ReadDatetime,
			IsRead:                    alert.IsRead,
			AssetReferenceNumber:      asset.ReferenceNumber,
			AssetSerialNumber:         asset.SerialNumber,
			TicketStatus:              commonmodel.ConstantModel(ticket.TicketStatus),
			TicketStatusCode:          ticket.StatusCode,
			AlertConfig: dtos.AlertConfig{
				ID:                   alert.AlertConfig.ID,
				AssetCategoryCode:    alert.AlertConfig.AssetCategoryCode,
				AssetSubCategoryCode: alert.AlertConfig.AssetSubCategoryCode,
				AlertCategoryCode:    alert.AlertConfig.AlertCategoryCode,
				AlertSubCategoryCode: alert.AlertConfig.AlertSubCategoryCode,
				Name:                 alert.AlertConfig.Name,
				Description:          alert.AlertConfig.Description,
				StatusCode:           alert.AlertConfig.StatusCode,
				AlertCategory: dtos.AlertConfigCategory{
					Code:              alert.AlertConfig.AlertCategory.Code,
					Description:       alert.AlertConfig.AlertCategory.Description,
					Label:             alert.AlertConfig.AlertCategory.Label,
					AssetCategoryCode: alert.AlertConfig.AlertCategory.AssetCategoryCode,
				},
				AlertSubCategory: dtos.AlertConfigSubCategory{
					Code:         alert.AlertConfig.AlertSubCategory.Code,
					CategoryCode: alert.AlertConfig.AlertSubCategory.CategoryCode,
					UomCodes:     alert.AlertConfig.AlertSubCategory.UomCodes,
					Description:  alert.AlertConfig.AlertSubCategory.Description,
					Label:        alert.AlertConfig.AlertSubCategory.Label,
				},
			},
			StateCategory: commonmodel.ConstantModel(alert.StateCategory),
			CreatedAt:     alert.CreatedAt,
		}
		respData = append(respData, alert)
	}

	return &commonmodel.ListResponse{
		TotalRecords: count,
		PageSize:     req.PageSize,
		PageNo:       req.PageNo,
		Data:         respData,
	}, nil
}

func (uc *AlertUseCase) GetAlertConfig(ctx context.Context, id string) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	alertConfig, err := uc.alertRepo.GetAlertConfig(ctx, uc.DB.DB(), models.AlertConfigCondition{
		Where: models.AlertConfigWhere{
			ID:       id,
			ClientID: claim.GetLoggedInClientID(),
		},
		Preload: models.AlertConfigPreload{
			Status:                true,
			AlertCategory:         true,
			AlertSubCategory:      true,
			ConfigTriggers:        true,
			ConfigTriggerStates:   true,
			ConfigTriggersTrigger: true,
		},
		Columns:     []string{},
		IsForUpdate: false,
	})
	if err != nil {
		return nil, err
	}

	userIDs := []string{}
	for _, configTrigger := range alertConfig.ConfigTriggers {
		for _, configTriggerState := range configTrigger.ConfigTriggerStates {
			userIDs = append(userIDs, configTriggerState.AssignedUserID.String)
		}
	}

	users, err := uc.userRepo.GetUsersV2(ctx, uc.DB.DB(), userModel.UserCondition{
		Where: userModel.UserWhere{
			IDs: userIDs,
		},
		Columns: []string{},
		Preload: userModel.UserPreload{},
	})
	if err != nil {
		return nil, err
	}

	mapUserName := map[string]string{}
	for i := range users {
		mapUserName[users[i].ID] = users[i].GetName()
	}

	resp := dtos.AlertConfig{
		ID:                   id,
		AssetCategoryCode:    alertConfig.AssetCategoryCode,
		AssetSubCategoryCode: alertConfig.AssetSubCategoryCode,
		AlertCategoryCode:    alertConfig.AlertCategoryCode,
		AlertSubCategoryCode: alertConfig.AlertSubCategoryCode,
		Name:                 alertConfig.Name,
		Description:          alertConfig.Description,
		StatusCode:           alertConfig.StatusCode,
		Status:               commonmodel.ConstantModel(alertConfig.Status),
		AlertCategory: dtos.AlertConfigCategory{
			Code:              alertConfig.AlertCategory.Code,
			Description:       alertConfig.AlertCategory.Description,
			Label:             alertConfig.AlertCategory.Label,
			AssetCategoryCode: alertConfig.AlertCategory.AssetCategoryCode,
		},
		AlertSubCategory: dtos.AlertConfigSubCategory{
			Code:         alertConfig.AlertSubCategory.Code,
			CategoryCode: alertConfig.AlertSubCategory.CategoryCode,
			UomCodes:     alertConfig.AlertSubCategory.UomCodes,
			Description:  alertConfig.AlertSubCategory.Description,
			Label:        alertConfig.AlertSubCategory.Label,
		},
		ConfigTriggers: make([]dtos.AlertConfigTrigger, 0, len(alertConfig.ConfigTriggers)),
	}

	for _, configTrigger := range alertConfig.ConfigTriggers {
		configTriggerResp := dtos.AlertConfigTrigger{
			ID:            configTrigger.ID,
			AlertConfigID: configTrigger.AlertConfigID,
			TriggerCode:   configTrigger.TriggerCode,
			Trigger: dtos.AlertTrigger{
				Code:            configTrigger.Trigger.Code,
				Label:           configTrigger.Trigger.Label,
				Description:     configTrigger.Trigger.Description,
				SubCategoryCode: configTrigger.Trigger.SubCategoryCode,
			},
			ConfigTriggerStates: make([]dtos.AlertConfigTriggerState, 0, len(configTrigger.ConfigTriggerStates)),
		}

		for _, configTriggerState := range configTrigger.ConfigTriggerStates {
			configTriggerResp.ConfigTriggerStates = append(configTriggerResp.ConfigTriggerStates, dtos.AlertConfigTriggerState{
				ID:                   configTriggerState.ID,
				AlertConfigTriggerID: configTriggerState.AlertConfigTriggerID,
				StateCategoryCode:    configTriggerState.StateCategoryCode,
				Value:                configTriggerState.Value,
				ActionTypeCodes:      configTriggerState.ActionTypeCodes,
				AutoCreateTicket:     configTriggerState.AutoCreateTicket.Bool,
				TicketPriorityCode:   configTriggerState.TicketPriorityCode.String,
				AssignedUserID:       configTriggerState.AssignedUserID,
				AssignedUserName:     mapUserName[configTriggerState.AssignedUserID.String],
			})
		}

		resp.ConfigTriggers = append(resp.ConfigTriggers, configTriggerResp)
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: id,
		Data:        resp,
	}, nil
}

func (uc *AlertUseCase) UpdateAlertConfigStatus(ctx context.Context, id string, req dtos.UpdateAlertConfigStatusReq) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	alertConfig, err := uc.alertRepo.GetAlertConfig(ctx, uc.DB.DB(), models.AlertConfigCondition{
		Where: models.AlertConfigWhere{
			ID:       id,
			ClientID: claim.GetLoggedInClientID(),
		},
	})
	if err != nil {
		return nil, err
	}

	if alertConfig.StatusCode == req.StatusCode {
		return &commonmodel.DetailResponse{
			Success:     true,
			Message:     "Success",
			ReferenceID: id,
			Data:        nil,
		}, nil
	}

	err = uc.alertRepo.UpdateAlertConfig(ctx, uc.DB.WithCtx(ctx).DB(), id, &models.AlertConfig{
		StatusCode: req.StatusCode,
	})
	if err != nil {
		return nil, err
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: id,
		Data:        nil,
	}, nil
}

func (uc *AlertUseCase) DeleteAlertConfig(ctx context.Context, id string) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	alertConfig, err := uc.alertRepo.GetAlertConfig(ctx, uc.DB.DB(), models.AlertConfigCondition{
		Where: models.AlertConfigWhere{
			ID:       id,
			ClientID: claim.GetLoggedInClientID(),
		},
		Preload: models.AlertConfigPreload{
			ConfigTriggers:      true,
			ConfigTriggerStates: true,
		},
	})
	if err != nil {
		return nil, err
	}

	tx, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return nil, err
	}

	defer tx.Rollback()

	configTriggerIDs := []string{}
	configTriggerStateIDs := []string{}
	for _, configTrigger := range alertConfig.ConfigTriggers {
		configTriggerIDs = append(configTriggerIDs, configTrigger.ID)
		for _, configTriggerState := range configTrigger.ConfigTriggerStates {
			configTriggerStateIDs = append(configTriggerStateIDs, configTriggerState.ID)
		}
	}

	err = uc.alertRepo.DeleteAlertConfigTriggerByIDs(ctx, tx.DB(), configTriggerIDs)
	if err != nil {
		return nil, err
	}

	err = uc.alertRepo.DeleteAlertConfigTriggerStateByIDs(ctx, tx.DB(), configTriggerStateIDs)
	if err != nil {
		return nil, err
	}

	err = uc.alertRepo.DeleteAlertConfigByID(ctx, tx.DB(), id)
	if err != nil {
		return nil, err
	}

	err = tx.Commit()
	if err != nil {
		return nil, err
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: id,
		Data:        nil,
	}, nil
}

func (uc *AlertUseCase) UpdateAlertConfig(ctx context.Context, id string, req dtos.AlertConfigReq) (*commonmodel.UpdateResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	alertConfig, err := uc.alertRepo.GetAlertConfig(ctx, uc.DB.DB(), models.AlertConfigCondition{
		Where: models.AlertConfigWhere{
			ID:       id,
			ClientID: claim.GetLoggedInClientID(),
		},
		Preload:     models.AlertConfigPreload{},
		Columns:     []string{},
		IsForUpdate: false,
	})
	if err != nil {
		return nil, err
	}

	tx, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return nil, err
	}

	defer tx.Rollback()

	err = uc.alertRepo.UpdateAlertConfig(ctx, tx.DB(), id, &models.AlertConfig{
		Name:        req.Name,
		Description: req.Description,
		StatusCode:  req.StatusCode,
	})
	if err != nil {
		return nil, err
	}

	newConfigTriggers := []models.AlertConfigTrigger{}
	newConfigTriggerStates := []models.AlertConfigTriggerState{}
	deleteConfigTriggerIDs := []string{}
	deleteConfigTriggerStateIDs := []string{}
	for _, configTrigger := range req.ConfigTriggers {
		// New config trigger condition
		if configTrigger.ID == "" {
			newTriggerStateOnNewConfigTrigger := make(
				[]models.AlertConfigTriggerState,
				0,
				len(configTrigger.ConfigTriggerStates),
			)

			for _, configTriggerState := range configTrigger.ConfigTriggerStates {
				newTriggerStateOnNewConfigTrigger = append(newTriggerStateOnNewConfigTrigger,
					models.AlertConfigTriggerState{
						AlertConfigTriggerID: "",
						StateCategoryCode:    configTriggerState.StateCategoryCode,
						Value:                configTriggerState.Value,
						ActionTypeCodes:      configTriggerState.ActionTypeCodes,
						AutoCreateTicket:     null.BoolFrom(configTriggerState.AutoCreateTicket),
						TicketPriorityCode:   null.StringFrom(configTriggerState.TicketPriorityCode),
						AssignedUserID:       configTriggerState.AssignedUserID,
					})
			}

			newConfigTriggers = append(newConfigTriggers, models.AlertConfigTrigger{
				AlertConfigID:       id,
				TriggerCode:         configTrigger.TriggerCode,
				ConfigTriggerStates: newTriggerStateOnNewConfigTrigger,
			})
			continue
		}

		// Deleted config trigger condition
		if configTrigger.IsDeleted {
			deleteConfigTriggerIDs = append(deleteConfigTriggerIDs, configTrigger.ID)
			continue
		}

		// Update config trigger condition
		err = uc.alertRepo.UpdateAlertConfigTrigger(ctx, tx.DB(),
			configTrigger.ID, &models.AlertConfigTrigger{
				TriggerCode: configTrigger.TriggerCode,
			})
		if err != nil {
			return nil, err
		}
		for _, configTriggerState := range configTrigger.ConfigTriggerStates {
			// New config trigger state condition
			if configTriggerState.ID == "" {
				newConfigTriggerStates = append(
					newConfigTriggerStates,
					models.AlertConfigTriggerState{
						AlertConfigTriggerID: configTrigger.ID,
						StateCategoryCode:    configTriggerState.StateCategoryCode,
						Value:                configTriggerState.Value,
						ActionTypeCodes:      configTriggerState.ActionTypeCodes,
						AutoCreateTicket:     null.BoolFrom(configTriggerState.AutoCreateTicket),
						TicketPriorityCode:   null.StringFrom(configTriggerState.TicketPriorityCode),
						AssignedUserID:       configTriggerState.AssignedUserID,
					})

				continue
			}

			// Delete config trigger state condition
			if configTriggerState.IsDeleted {
				deleteConfigTriggerStateIDs = append(deleteConfigTriggerStateIDs, configTriggerState.ID)
				continue
			}

			// Update config trigger state condition
			err = uc.alertRepo.UpdateAlertConfigTriggerState(ctx, tx.DB(),
				configTriggerState.ID, &models.AlertConfigTriggerState{
					StateCategoryCode:  configTriggerState.StateCategoryCode,
					Value:              configTriggerState.Value,
					ActionTypeCodes:    configTriggerState.ActionTypeCodes,
					AutoCreateTicket:   null.BoolFrom(configTriggerState.AutoCreateTicket),
					TicketPriorityCode: null.StringFrom(configTriggerState.TicketPriorityCode),
					AssignedUserID:     configTriggerState.AssignedUserID,
				})
			if err != nil {
				return nil, err
			}
		}
	}

	err = uc.alertRepo.DeleteAlertConfigTriggerStates(ctx, tx.DB(), deleteConfigTriggerStateIDs)
	if err != nil {
		return nil, err
	}

	err = uc.alertRepo.DeleteAlertConfigTriggerStatesByConfigTriggerIDs(ctx, tx.DB(), deleteConfigTriggerIDs)
	if err != nil {
		return nil, err
	}

	err = uc.alertRepo.DeleteAlertConfigTriggers(ctx, tx.DB(), deleteConfigTriggerIDs)
	if err != nil {
		return nil, err
	}

	err = uc.alertRepo.CreateAlertConfigTrigger(ctx, tx.DB(), newConfigTriggers)
	if err != nil {
		return nil, err
	}

	err = uc.alertRepo.CreateAlertConfigTriggerState(ctx, tx.DB(), newConfigTriggerStates)
	if err != nil {
		return nil, err
	}

	err = tx.Commit()
	if err != nil {
		return nil, err
	}

	return &commonmodel.UpdateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: alertConfig.ID,
		Data:        nil,
	}, nil
}

func (u *AlertUseCase) SetAlertsAsRead(ctx context.Context, ids []string) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	err = u.alertRepo.SetAlertsAsRead(ctx, u.DB.WithCtx(ctx).DB(), ids, claim.GetLoggedInClientID())
	if err != nil {
		return nil, err
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        nil,
	}, nil
}

func (u *AlertUseCase) MarkAllAlertsAsReadByAssetID(ctx context.Context, assetID string) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	err = u.alertRepo.MarkAllAlertsAsReadByAssetID(ctx, u.DB.WithCtx(ctx).DB(), assetID, claim.GetLoggedInClientID())
	if err != nil {
		return nil, err
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        nil,
	}, nil
}

func (uc *AlertUseCase) GetAlertParamaterList(ctx context.Context, req dtos.AlertParameterListReq) (*commonmodel.ListResponse, error) {
	count, alerts, err := uc.alertRepo.GetAlertParameterList(ctx, uc.DB.DB(), models.GetAlertParameterListParam{
		ListRequest: req.ListRequest,
		Cond: models.AlertParameterCondition{
			Where: models.AlertParameterWhere{
				SourceCodes: req.SourceCodes,
			},
			IsForUpdate: false,
		},
	})
	if err != nil {
		return nil, err
	}

	return &commonmodel.ListResponse{
		TotalRecords: count,
		PageSize:     req.PageSize,
		PageNo:       req.PageNo,
		Data:         alerts,
	}, nil
}

func (uc *AlertUseCase) GetAlertConditionOperatorList(ctx context.Context, req dtos.AlertConditionOperatorListReq) (*commonmodel.ListResponse, error) {
	count, alerts, err := uc.alertRepo.GetAlertConditionOperatorList(ctx, uc.DB.DB(), models.GetAlertConditionOperatorListParam{
		ListRequest: req.ListRequest,
		Cond: models.AlertConditionOperatorCondition{
			Where: models.AlertConditionOperatorWhere{
				DataTypes: req.DataTypes,
			},
			IsForUpdate: false,
		},
	})
	if err != nil {
		return nil, err
	}

	return &commonmodel.ListResponse{
		TotalRecords: count,
		PageSize:     req.PageSize,
		PageNo:       req.PageNo,
		Data:         alerts,
	}, nil
}

func (uc *AlertUseCase) CreateAlertConfigV2(ctx context.Context, req dtos.AlertConfigV2Req) (*commonmodel.CreateResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	if req.TicketCategoryCode.String != "" {
		_, err = uc.ticketRepo.GetTicketCategory(ctx, uc.DB.DB(), taskModel.TicketCategoryCondition{
			Where: taskModel.TicketCategoryWhere{
				Code:              req.TicketCategoryCode.String,
				StatusCodes:       []string{taskConstants.TICKET_CATEGORY_STATUS_ACTIVE},
				ClientIDOrGeneral: claim.GetLoggedInClientID(),
			},
		})
		if err != nil {
			return nil, err
		}
	}

	alertConfig := &models.AlertConfigV2{
		Name:                      req.Name,
		Description:               req.Description,
		StatusCode:                req.StatusCode,
		ConditionTypeCodes:        req.ConditionTypeCodes,
		AssetIDs:                  req.AssetIDs,
		UseActions:                req.UseActions,
		ActionTypeCodes:           req.ActionTypeCodes,
		ActionUserRecipients:      req.ActionUserRecipients,
		ActionNotifyAssetAssignee: req.ActionNotifyAssetAssignee,
		TicketAutoCreate:          req.TicketAutoCreate,
		TicketPriorityCode:        req.TicketPriorityCode,
		TicketCategoryCode:        req.TicketCategoryCode,
		TicketSubject:             req.TicketSubject,
		TicketType:                req.TicketType,
		TicketDesc:                req.TicketDesc,
		TicketAssignedUserID:      req.TicketAssignedUserID,
		DeactivateAfterReach:      req.DeactivateAfterReach,
		Conditions:                []models.AlertConfigV2Condition{},
	}

	for i := range req.Conditions {
		alertConfig.Conditions = append(alertConfig.Conditions, models.AlertConfigV2Condition{
			ParameterCode: req.Conditions[i].ParameterCode,
			OperatorCode:  req.Conditions[i].OperatorCode,
			Val:           req.Conditions[i].Val,
		})
	}
	err = uc.alertRepo.CreateAlertConfigV2(ctx, uc.DB.WithCtx(ctx).DB(), alertConfig)
	if err != nil {
		return nil, err
	}

	return &commonmodel.CreateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: alertConfig.ID,
		Data:        nil,
	}, nil
}

func (uc *AlertUseCase) UpdateAlertConfigV2(ctx context.Context, id string, req dtos.AlertConfigV2Req) (*commonmodel.UpdateResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	existingAlertConfig, err := uc.alertRepo.GetAlertConfigV2(ctx, uc.DB.DB(), models.AlertConfigV2Cond{
		Where: models.AlertConfigV2Where{
			ID:       id,
			ClientID: claim.GetLoggedInClientID(),
		},
		Preload: models.AlertConfigV2Preload{},
	})
	if err != nil {
		return nil, err
	}

	if req.TicketCategoryCode.String != "" && req.TicketCategoryCode != existingAlertConfig.TicketCategoryCode {
		_, err := uc.ticketRepo.GetTicketCategory(ctx, uc.DB.DB(), taskModel.TicketCategoryCondition{
			Where: taskModel.TicketCategoryWhere{
				Code:              req.TicketCategoryCode.String,
				StatusCodes:       []string{taskConstants.TICKET_CATEGORY_STATUS_ACTIVE},
				ClientIDOrGeneral: claim.GetLoggedInClientID(),
			},
		})
		if err != nil {
			return nil, err
		}
	}

	alertConfig := &models.AlertConfigV2{
		Name:                      req.Name,
		Description:               req.Description,
		StatusCode:                req.StatusCode,
		ConditionTypeCodes:        req.ConditionTypeCodes,
		AssetIDs:                  req.AssetIDs,
		UseActions:                req.UseActions,
		ActionTypeCodes:           req.ActionTypeCodes,
		ActionUserRecipients:      req.ActionUserRecipients,
		ActionNotifyAssetAssignee: req.ActionNotifyAssetAssignee,
		TicketAutoCreate:          req.TicketAutoCreate,
		TicketPriorityCode:        req.TicketPriorityCode,
		TicketCategoryCode:        req.TicketCategoryCode,
		TicketSubject:             req.TicketSubject,
		TicketDesc:                req.TicketDesc,
		TicketAssignedUserID:      req.TicketAssignedUserID,
		DeactivateAfterReach:      req.DeactivateAfterReach,
	}

	tx, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return nil, err
	}

	defer tx.Rollback()

	deletedConditionIDs := []string{}
	newConditions := []models.AlertConfigV2Condition{}
	for i := range req.Conditions {
		if req.Conditions[i].ID == "" {
			newConditions = append(newConditions, models.AlertConfigV2Condition{
				AlertConfigID: id,
				ParameterCode: req.Conditions[i].ParameterCode,
				OperatorCode:  req.Conditions[i].OperatorCode,
				Val:           req.Conditions[i].Val,
			})
		} else if req.Conditions[i].IsDelete {
			if req.Conditions[i].ID != "" {
				deletedConditionIDs = append(deletedConditionIDs, req.Conditions[i].ID)
			}
		} else {
			err := uc.alertRepo.UpdateAlertConfigV2Condition(ctx, tx.DB(), req.Conditions[i].ID, &models.AlertConfigV2Condition{
				ParameterCode: req.Conditions[i].ParameterCode,
				OperatorCode:  req.Conditions[i].OperatorCode,
				Val:           req.Conditions[i].Val,
			})
			if err != nil {
				return nil, err
			}
		}
	}

	err = uc.alertRepo.UpdateAlertConfigV2(ctx, uc.DB.WithCtx(ctx).DB(), id, alertConfig)
	if err != nil {
		return nil, err
	}

	if req.DeactivateAfterReach.Valid && !req.DeactivateAfterReach.Bool &&
		req.DeactivateAfterReach != existingAlertConfig.DeactivateAfterReach {
		err = uc.alertRepo.ClearAlertConfigHappenOnAssetIDs(ctx, uc.DB.WithCtx(ctx).DB(), id)
		if err != nil {
			return nil, err
		}
	}

	err = uc.alertRepo.CreateAlertConfigV2Conditions(ctx, tx.DB(), newConditions)
	if err != nil {
		return nil, err
	}

	err = uc.alertRepo.DeleteAlertConfigV2Conditions(ctx, uc.DB.DB(), deletedConditionIDs)
	if err != nil {
		return nil, err
	}

	err = tx.Commit()
	if err != nil {
		return nil, err
	}

	return &commonmodel.UpdateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: id,
		Data:        nil,
	}, nil
}

func (uc *AlertUseCase) GetAlertConfigV2s(ctx context.Context, req dtos.AlertConfigV2ListReq) (*commonmodel.ListResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	count, alertConfigs, err := uc.alertRepo.GetAlertConfigV2List(ctx, uc.DB.DB(), models.GetAlertConfigV2ListParam{
		ListRequest: req.ListRequest,
		Cond: models.AlertConfigV2Cond{
			Where: models.AlertConfigV2Where{
				ClientID:   claim.GetLoggedInClientID(),
				AssetIDs:   req.AssetIDs,
				StatusCode: req.StatusCode,
			},
			Preload: models.AlertConfigV2Preload{
				Conditions:          true,
				ConditionsParameter: true,
				ConditionsOperator:  true,
			},
		},
	})
	if err != nil {
		return nil, err
	}

	respData := make([]dtos.AlertConfigV2, 0, len(alertConfigs))
	for _, alertConfig := range alertConfigs {
		respData = append(respData, dtos.BuildAlertConfigV2Resp(alertConfig))
	}

	return &commonmodel.ListResponse{
		TotalRecords: count,
		PageSize:     req.PageSize,
		PageNo:       req.PageNo,
		Data:         respData,
	}, nil
}

func (uc *AlertUseCase) GetAlertConfigV2(ctx context.Context, id string) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	alertConfigs, err := uc.alertRepo.GetAlertConfigV2(ctx, uc.DB.DB(), models.AlertConfigV2Cond{
		Where: models.AlertConfigV2Where{
			ID:       id,
			ClientID: claim.GetLoggedInClientID(),
		},
		Preload: models.AlertConfigV2Preload{
			Conditions:          true,
			ConditionsParameter: true,
			ConditionsOperator:  true,
		},
	})
	if err != nil {
		return nil, err
	}

	var users []userModel.User
	if len(alertConfigs.ActionUserRecipients) > 0 {
		users, err = uc.userRepo.GetUsersV2(ctx, uc.DB.DB(), userModel.UserCondition{
			Where: userModel.UserWhere{
				IDs: alertConfigs.ActionUserRecipients,
			},
		})
		if err != nil {
			return nil, err
		}
	}

	var assets []assetModel.Asset
	if len(alertConfigs.AssetIDs) > 0 {
		assets, err = uc.assetRepo.GetAssets(ctx, uc.DB.DB(), assetModel.AssetCondition{
			Where: assetModel.AssetWhere{
				IDs: alertConfigs.AssetIDs,
			},
		})
		if err != nil {
			return nil, err
		}
	}
	resp := dtos.BuildAlertConfigV2Resp(*alertConfigs)
	resp.RecipientsUsers = dtos.BuildUsersResp(users)
	resp.Assets = dtos.BuildAssetsResp(assets)

	if alertConfigs.TicketAssignedUserID.String != "" {
		user, err := uc.userRepo.GetUser(ctx, uc.DB.DB(), userModel.UserCondition{
			Where: userModel.UserWhere{
				ID: alertConfigs.TicketAssignedUserID.String,
			},
		})
		if err != nil {
			return nil, err
		}

		resp.TicketAssignedUserName = null.StringFrom(user.GetName())
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: id,
		Data:        resp,
	}, nil
}

func (uc *AlertUseCase) UpdateAlertConfigV2Status(ctx context.Context, id string, newStatusCode string) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	alertConfig, err := uc.alertRepo.GetAlertConfigV2(ctx, uc.DB.DB(), models.AlertConfigV2Cond{
		Where: models.AlertConfigV2Where{
			ID:         id,
			ClientID:   claim.GetLoggedInClientID(),
			StatusCode: "",
		},
		Preload: models.AlertConfigV2Preload{
			Conditions: true,
		},
	})
	if err != nil {
		return nil, err
	}

	tx, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return nil, err
	}

	defer tx.Rollback()

	if newStatusCode == alertConfig.StatusCode {
		return &commonmodel.DetailResponse{
			Success:     true,
			Message:     "Success",
			ReferenceID: id,
			Data:        nil,
		}, nil
	}

	err = uc.alertRepo.UpdateAlertConfigV2(ctx, tx.DB(), id, &models.AlertConfigV2{
		StatusCode: newStatusCode,
	})
	if err != nil {
		return nil, err
	}

	if newStatusCode == constants.ALERT_CONFIG_STATUS_CODE_ACTIVE {
		err = uc.alertRepo.ClearAlertConfigHappenOnAssetIDs(ctx, tx.DB(), id)
		if err != nil {
			return nil, err
		}
	}

	err = tx.Commit()
	if err != nil {
		return nil, err
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: id,
		Data:        nil,
	}, nil
}

func (uc *AlertUseCase) DeleteAlertConfigV2(ctx context.Context, id string) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	alertConfig, err := uc.alertRepo.GetAlertConfigV2(ctx, uc.DB.DB(), models.AlertConfigV2Cond{
		Where: models.AlertConfigV2Where{
			ID:         id,
			ClientID:   claim.GetLoggedInClientID(),
			StatusCode: "",
		},
		Preload: models.AlertConfigV2Preload{
			Conditions: true,
		},
	})
	if err != nil {
		return nil, err
	}

	tx, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return nil, err
	}

	defer tx.Rollback()

	configConditionIDs := []string{}
	for _, configCondition := range alertConfig.Conditions {
		configConditionIDs = append(configConditionIDs, configCondition.ID)
	}

	err = uc.alertRepo.DeleteAlertConfigV2Conditions(ctx, tx.DB(), configConditionIDs)
	if err != nil {
		return nil, err
	}

	err = uc.alertRepo.DeleteAlertConfigV2(ctx, tx.DB(), id)
	if err != nil {
		return nil, err
	}

	err = tx.Commit()
	if err != nil {
		return nil, err
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: id,
		Data:        nil,
	}, nil
}

func (uc *AlertUseCase) GetAlertV2s(ctx context.Context, req dtos.AlertV2ListReq) (*commonmodel.ListResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	count, alerts, err := uc.alertRepo.GetAlertV2List(ctx, uc.DB.DB(), models.GetAlertV2ListParam{
		ListRequest: req.ListRequest,
		Cond: models.AlertV2Cond{
			Where: models.AlertV2Where{
				AssetIDs: req.AssetIDs,
				ClientID: claim.GetLoggedInClientID(),
			},
			Preload: models.AlertV2Preload{
				AlertConfig: true,
			},
			Columns: []string{},
		},
	})
	if err != nil {
		return nil, err
	}

	respData := make([]dtos.AlertV2, 0, len(alerts))
	for _, alert := range alerts {
		respData = append(respData, dtos.BuildAlertV2Resp(alert))
	}

	return &commonmodel.ListResponse{
		TotalRecords: count,
		PageSize:     req.PageSize,
		PageNo:       req.PageNo,
		Data:         respData,
	}, nil
}

func (u *AlertUseCase) SetAlertV2sAsRead(ctx context.Context, ids []string) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	err = u.alertRepo.SetAlertV2sAsRead(ctx, u.DB.WithCtx(ctx).DB(), ids, claim.GetLoggedInClientID())
	if err != nil {
		return nil, err
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        nil,
	}, nil
}

func (u *AlertUseCase) MarkAllAlertV2sAsReadByAssetID(ctx context.Context, assetID string) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	err = u.alertRepo.MarkAllAlertV2sAsReadByAssetID(ctx, u.DB.WithCtx(ctx).DB(), assetID, claim.GetLoggedInClientID())
	if err != nil {
		return nil, err
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        nil,
	}, nil
}

func (u *AlertUseCase) ChartAlertV2sByAssetCategories(ctx context.Context, chartName string, chartCode string, assetCategories []string, excludedAssetCategories []string) ([]commonmodel.Chart, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	count, err := u.alertRepo.CountAssetAlertV2(ctx, u.DB.DB(), models.AlertV2Cond{
		Where: models.AlertV2Where{
			ClientID:                claim.GetLoggedInClientID(),
			AssetCategories:         assetCategories,
			ExcludedAssetCategories: excludedAssetCategories,
			IsRead:                  null.BoolFrom(false),
		},
	})
	if err != nil {
		return nil, err
	}

	var charts []commonmodel.Chart
	expiredComponentChart := commonmodel.Chart{
		Name: chartName,
		Code: null.StringFrom(chartCode),
		Y:    float64(count),
	}
	charts = append(charts, expiredComponentChart)

	return charts, nil
}
