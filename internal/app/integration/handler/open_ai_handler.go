package handler

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"os"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	openai "github.com/openai/openai-go/v2"
	"github.com/openai/openai-go/v2/option"
)

const openAIModel = "gpt-4o-mini"

type TicketMapping struct {
	WEIGHT_BRIDGE_OUTBOUND *string `json:"WEIGHT_BRIDGE_OUTBOUND,omitempty"`
	TICKET_ID              *string `json:"TICKET_ID,omitempty"`
	VEHICLE                *string `json:"VEHICLE,omitempty"`
	ACCOUNT                *string `json:"ACCOUNT,omitempty"`
	HAULER                 *string `json:"HAULER,omitempty"`
	TARE                   *string `json:"TARE,omitempty"`
	INBOUND_DATE           *string `json:"INBOUND_DATE,omitempty"`
	INBOUND_TIME           *string `json:"INBOUND_TIME,omitempty"`
	INBOUND_GROSS          *string `json:"INBOUND_GROSS,omitempty"`
	OUTBOUND_DATE          *string `json:"OUTBOUND_DATE,omitempty"`
	OUTBOUND_TIME          *string `json:"OUTBOUND_TIME,omitempty"`
	OUTBOUND_TARE          *string `json:"OUTBOUND_TARE,omitempty"`
	MATERIAL               *string `json:"MATERIAL,omitempty"`
	SOURCE_LOCATION        *string `json:"SOURCE_LOCATION,omitempty"`
	TRUCK_TYPE             *string `json:"TRUCK_TYPE,omitempty"`
	NET_WEIGHT             *string `json:"NET_WEIGHT,omitempty"`
	TIME_OUT               *string `json:"TIME_OUT,omitempty"`
	END_TIPPING_LOCATION   *string `json:"END_TIPPING_LOCATION,omitempty"`
}

func newOpenAIClient() (openai.Client, error) {
	apiKey := "********************************************************************************************************************************************************************"
	if apiKey == "" {
		return openai.Client{}, errors.New("OPENAI_API_KEY is not set")
	}
	opts := []option.RequestOption{option.WithAPIKey(apiKey)}
	if base := os.Getenv("OPENAI_BASE_URL"); base != "" {
		opts = append(opts, option.WithBaseURL(strings.TrimRight(base, "/")))
	}
	return openai.NewClient(opts...), nil
}

func callOpenAIForMapping(ctx context.Context, model, ocr string, ocr2ndTry string) (TicketMapping, error) {
	var out TicketMapping

	client, err := newOpenAIClient()
	if err != nil {
		return out, err
	}

	const promptTemplate = `
	You are given raw OCR text extracted from a coal mining weight bridge ticket.

	Your task: extract and map the values into a structured JSON object with the following keys. For each key, follow the location rule described (based on regex patterns) to determine where to find the value. The regex patterns are only guidance — the OCR result may contain typos, extra spaces, wrong line breaks, or partially broken words. You must interpret the text like a human, correcting obvious OCR mistakes while still matching the intended field.

	JSON KEYS & EXTRACTION RULES:
	1. WEIGHT_BRIDGE_OUTBOUND: After "Weight Bridge" (Regex: (?i)weight\s+bridge\s+0?(\d+))
	2. TICKET_ID: Starts with "IP" (Regex: (?i)\bIP[\w-]*[- ]\d+\b)
	3. VEHICLE: After "Vehicle" (Regex: (?i)\bVehicle\b\s*[-–:]?\s*\n?\s*(\d+))
	4. ACCOUNT: After "Account:" until line break.
	5. HAULER: After "Hauler" until line break.
	6. TARE: Weight in kg after "Tare". **Expected format: number only, no unit.**
	7. INBOUND_DATE: Date before "Inbound" (Regex: (\d{2}[/:.\s]?\d{2}[/:.\s]?\d{4})\s*[\d\s.:]{4,10}\s*(?i)Inbound). **Expected format: DD/MM/YYYY.**
	8. INBOUND_TIME: Time before "Inbound" (Regex: \d{2}[/:.\s]?\d{2}[/:.\s]?\d{4}\s*([\d\s.:]{4,10})\s*(?i)Inbound). **Expected format: HH:mm:ss (24-hour).**
	9. INBOUND_GROSS: Weight before "Inbound" (Regex: (?i)(\d+)\s*kg\s*\n.*\n.*inbound). **Expected format: number only, no unit.**
	10. OUTBOUND_DATE: Date before "Outbound" (Regex: (\d{2}[/:.\s]?\d{2}[/:.\s]?\d{4})\s*[\d\s.:]{4,10}\s*(?i)Outbound). **Expected format: DD/MM/YYYY.**
	11. OUTBOUND_TIME: Time before "Outbound" (Regex: \d{2}[/:.\s]?\d{2}[/:.\s]?\d{4}\s*([\d\s.:]{4,10})\s*(?i)Outbound). **Expected format: HH:mm:ss (24-hour).**
	12. OUTBOUND_TARE: Weight before "Outbound" (Regex: (?i)(\d+)\s*kg\s*\n.*\n.*outbound). **Expected format: number only, no unit.**
	13. MATERIAL: After "Material" until line break.
	14. SOURCE_LOCATION: After "Source:" or "COALPAD" (Regex: (?i)COALPAD\s+(.*))
	15. TRUCK_TYPE: After "Type Truck:" until line break.
	16. NET_WEIGHT: After "Net Weight:" or "Net Weght:" until line break.
	17. TIME_OUT: After "Time Out:" until line break. **Expected format: HH:mm:ss (24-hour).**
	18. END_TIPPING_LOCATION: Matches "SD-..." or "ROM..." (Regex: \b(SD-\S*|ROM\S*)\b)

	ADDITIONAL GUIDELINES:
	- From the OCR text, extract the values for each key based on the rules above.
	- Some times the OCR text may have switch between "Inbound" and "Outbound" sections, so be careful to match the correct date/time/weight.
	- - OUTBOUND_DATE + OUTBOUND_TIME and OUTBOUND_TARE always larger than INBOUND_DATE + INBOUND_TIME and INBOUND_GROSS.
	- - OUTBOUND_DATE + OUTBOUND_TIME and OUTBOUND_TARE can't be the same as INBOUND_DATE + INBOUND_TIME and INBOUND_GROSS. if you find it the same must be something wrong and please recheck and get larger value for outbound and lesser value for inbound.
	- Here are potential options for WEIGHT_BRIDGE_OUTBOUND, SOURCE_LOCATION and END_TIPPING_LOCATION
	- - Coalpad BT 05 AMM,SD-3,WB2,SD-4,SD-5,WB3,Coalpad FSP 01,Coalpad 01 TJ,Coalpad 02 BT,Coalpad BT 02 EXTEN,Coalpad 05 BT,Coalpad BT 05 BSS,Coalpad 05 TJ IP,Coalpad 05 TJ AMM,Coalpad 05 TJ IP,Coalpad BT 05 BSS,Coalpad BT 05 AMM,SD-3,WB2,SD-4,SD-5,WB3,Coalpad FSP 01,Coalpad 01 TJ,Coalpad 02 BT,Coalpad BT 02 EXTEN,Coalpad 05 BT,Coalpad 05 TJ AMM,COALPAD 06 TA AMM
	- Here are potential options for MATERIAL
	- - Raw Coal

	OUTPUT RULES:
	- Use regex rules as hints, not strict requirements.
	- If a field is missing, set it to null.
	- Correct obvious OCR errors and normalize spacing.
	- Follow the exact expected formats above for applicable fields.
	- Output only valid JSON, no additional commentary.

	First try OCR TEXT:
	"""
	%s
	"""

	Second try OCR TEXT:
	"""
	%s
	"""
	`

	userMsg := fmt.Sprintf(promptTemplate, strings.TrimSpace(ocr), strings.TrimSpace(ocr2ndTry))

	// Simple retry/backoff for transient 429/5xx
	backoffs := []time.Duration{0, 800 * time.Millisecond, 1600 * time.Millisecond}
	var lastErr error

	for _, wait := range backoffs {
		if wait > 0 {
			select {
			case <-ctx.Done():
				return out, ctx.Err()
			case <-time.After(wait):
			}
		}

		resp, err := client.Chat.Completions.New(ctx, openai.ChatCompletionNewParams{
			Model: model,
			Messages: []openai.ChatCompletionMessageParamUnion{
				openai.UserMessage(userMsg),
			},
			Temperature: openai.Float(0.1),
			TopP:        openai.Float(0.9),
			MaxTokens:   openai.Int(4096),
		})

		if err != nil {
			lastErr = fmt.Errorf("openai error: %w", err)
			continue
		}

		if resp == nil || len(resp.Choices) == 0 {
			lastErr = errors.New("empty model response (no choices)")
			continue
		}

		rawContent := resp.Choices[0].Message.Content
		text := strings.TrimSpace(rawContent)

		if text == "" {
			lastErr = errors.New("empty model response content")
			continue
		}

		if err := json.Unmarshal([]byte(text), &out); err != nil {
			if trimmed := trimToJSON(text); trimmed != "" {
				if err2 := json.Unmarshal([]byte(trimmed), &out); err2 == nil {
					return out, nil
				}
			}
			return out, fmt.Errorf("json unmarshal failed: %w; raw=%s", err, text)
		}
		return out, nil
	}
	return out, lastErr
}

func trimToJSON(s string) string {
	start := strings.Index(s, "{")
	end := strings.LastIndex(s, "}")
	if start >= 0 && end > start {
		return s[start : end+1]
	}
	return ""
}

type ocrToJsonReq struct {
	OCRText    string `json:"ocr_text" binding:"required"`
	OCRText2nd string `json:"ocr_text_2nd" binding:"required"`
	OCRText3rd string `json:"ocr_text_3rd" binding:"required"`
}

func (h *IntegrationHandler) MapOCRWithOpenAI(c *gin.Context) {
	var req ocrToJsonReq
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	ctx, cancel := context.WithTimeout(c.Request.Context(), 60*time.Second)
	defer cancel()

	mapping, err := callOpenAIForMapping(ctx, openAIModel, req.OCRText, req.OCRText)
	if err != nil {
		c.JSON(http.StatusBadGateway, gin.H{"error": err.Error()})
		return
	}
	c.JSON(http.StatusOK, gin.H{"data": mapping})
}
