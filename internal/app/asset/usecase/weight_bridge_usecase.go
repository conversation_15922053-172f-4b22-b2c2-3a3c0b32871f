package usecase

import (
	"assetfindr/internal/app/asset/dtos"
	"assetfindr/internal/app/asset/models"
	"assetfindr/internal/app/asset/repository"
	storageConstants "assetfindr/internal/app/storage/constants"
	storageDtos "assetfindr/internal/app/storage/dtos"
	storageUsecase "assetfindr/internal/app/storage/usecase"
	userIdentityModel "assetfindr/internal/app/user-identity/models"
	userIdentityRepository "assetfindr/internal/app/user-identity/repository"
	"assetfindr/internal/infrastructure/database"
	"assetfindr/pkg/common/commonlogger"
	"assetfindr/pkg/common/commonmodel"
	"assetfindr/pkg/common/helpers/authhelpers"
	"assetfindr/pkg/common/helpers/timehelpers"
	"context"
)

type WeightBridgeUseCase struct {
	DB                 database.DBUsecase
	weightBridgeRepo   repository.WeightBridgeRepository
	locationRepository repository.LocationRepository
	UserRepository     userIdentityRepository.UserRepository
	storageUsecase     *storageUsecase.AttachmentUseCase
}

func NewWeightBridgeUseCase(
	DB database.DBUsecase,
	weightBridgeRepo repository.WeightBridgeRepository,
	userRepository userIdentityRepository.UserRepository,
	storageUsecase *storageUsecase.AttachmentUseCase,
	locationRepository repository.LocationRepository,
) *WeightBridgeUseCase {
	return &WeightBridgeUseCase{
		DB:                 DB,
		weightBridgeRepo:   weightBridgeRepo,
		UserRepository:     userRepository,
		storageUsecase:     storageUsecase,
		locationRepository: locationRepository,
	}
}

func (uc *WeightBridgeUseCase) GetWeightBridgeTickets(ctx context.Context, req dtos.WeightBridgeTicketListReq) (*commonmodel.ListResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	createdAtStart, _ := timehelpers.ParseDateFilter(req.CreatedStartDate, false)
	createdAtEnd, _ := timehelpers.ParseDateFilter(req.CreatedEndDate, true)

	count, tickets, err := uc.weightBridgeRepo.GetWeightBridgeTicketList(ctx, uc.DB.DB(), models.GetWeightBridgeTicketListParam{
		ListRequest: req.ListRequest,
		Cond: models.WeightBridgeTicketCondition{
			Where: models.WeightBridgeTicketWhere{
				ClientID:        claim.GetLoggedInClientID(),
				OutboundAtStart: createdAtStart,
				OutboundAtEnd:   createdAtEnd,
			},
			Preload: models.WeightBridgeTicketPreload{
				AssetVehicle:                 true,
				InboundWeightBridgeLocation:  true,
				InboundLocation:              true,
				OutboundWeightBridgeLocation: true,
				OutboundLocation:             true,
				LocationRoute:                true,
			},
		},
	})
	if err != nil {
		return nil, err
	}

	var userIds []string

	for _, ticket := range tickets {
		if ticket.CreatedBy != "" {
			userIds = append(userIds, ticket.CreatedBy)
		}
	}

	usersMapById := map[string]userIdentityModel.User{}

	err = uc.UserRepository.GetUsersInMapByIds(ctx, uc.DB.DB(), &usersMapById, userIds)
	if err != nil {
		commonlogger.Errorf("Error in getting users by user ids from identity service", err)
		return nil, err
	}

	respData := make([]dtos.WeightBridgeTicket, 0, len(tickets))

	for _, ticket := range tickets {
		weightBridgeTicket := dtos.WeightBridgeTicket{}
		weightBridgeTicket.Set(ticket, usersMapById)
		respData = append(respData, weightBridgeTicket)
	}

	return &commonmodel.ListResponse{
		TotalRecords: count,
		PageSize:     req.PageSize,
		PageNo:       req.PageNo,
		Data:         respData,
	}, nil
}

func (uc *WeightBridgeUseCase) GetWeightBridgeTicket(ctx context.Context, id string) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	ticket, err := uc.weightBridgeRepo.GetWeightBridgeTicket(ctx, uc.DB.DB(), models.WeightBridgeTicketCondition{
		Where: models.WeightBridgeTicketWhere{
			ID:       id,
			ClientID: claim.GetLoggedInClientID(),
		},
		Preload: models.WeightBridgeTicketPreload{
			AssetVehicle:                 true,
			InboundWeightBridgeLocation:  true,
			InboundLocation:              true,
			OutboundWeightBridgeLocation: true,
			OutboundLocation:             true,
			LocationRoute:                true,
		},
	})
	if err != nil {
		return nil, err
	}

	var userIds []string

	if ticket.CreatedBy != "" {
		userIds = append(userIds, ticket.CreatedBy)
	}

	usersMapById := map[string]userIdentityModel.User{}

	err = uc.UserRepository.GetUsersInMapByIds(ctx, uc.DB.DB(), &usersMapById, userIds)
	if err != nil {
		commonlogger.Errorf("Error in getting users by user ids from identity service", err)
		return nil, err
	}

	respData := dtos.WeightBridgeTicket{}
	respData.Set(*ticket, usersMapById)

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: id,
		Data:        respData,
	}, nil

}

func (uc *WeightBridgeUseCase) CreateWeightBridgeTicket(ctx context.Context, req dtos.CreateWeightBridgeTicket) (*commonmodel.CreateResponse, error) {
	tX, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return nil, err
	}

	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	defer tX.Rollback()

	weightBridgeTicket := &models.WeightBridgeTicket{
		ReferenceID:                    req.ReferenceID,
		VehicleAssetID:                 req.VehicleAssetID,
		LoadType:                       req.LoadType,
		InboundAt:                      req.InboundAt,
		InboundWeightKg:                req.InboundWeightKg,
		InboundWeightBridgeLocationID:  req.InboundWeightBridgeLocationID,
		InboundOperatorUserID:          req.InboundOperatorUserID,
		InboundOperatorUserFirstName:   req.InboundOperatorUserFirstName,
		InboundOperatorUserLastName:    req.InboundOperatorUserLastName,
		InboundLocationID:              req.InboundLocationID,
		OutboundAt:                     req.OutboundAt,
		OutboundWeightKg:               req.OutboundWeightKg,
		OutboundWeightBridgeLocationID: req.OutboundWeightBridgeLocationID,
		OutboundOperatorUserID:         req.OutboundOperatorUserID,
		OutboundOperatorUserFirstName:  req.OutboundOperatorUserFirstName,
		OutboundOperatorUserLastName:   req.OutboundOperatorUserLastName,
		OutboundLocationID:             req.OutboundLocationID,
		NetWeight:                      req.NetWeight,
		LocationRouteID:                req.LocationRouteID,
		Remark:                         req.Remark,
	}

	if weightBridgeTicket.LocationRouteID == "" {
		_, locationRoutes, err := uc.locationRepository.GetLocationRouteList(ctx, uc.DB.DB(), models.GetLocationRouteListParam{
			Cond: models.LocationRouteCondition{
				Where: models.LocationRouteWhere{
					StartLocationID:    req.InboundLocationID,
					EndLocationChildID: req.OutboundLocationID,
				},
			},
		})
		if err != nil {
			return nil, err
		}

		if len(locationRoutes) > 0 {
			weightBridgeTicket.LocationRouteID = locationRoutes[0].ID
		}
	}

	if req.OutboundAt.Valid {
		shiftCode := timehelpers.GetShiftCode(req.OutboundAt.Time)
		weightBridgeTicket.WorkShift = shiftCode
	}

	err = uc.weightBridgeRepo.CreateWeightBridgeTicket(ctx, tX.DB(), weightBridgeTicket)
	if err != nil {
		return nil, err
	}

	_, err = uc.storageUsecase.CreateAttachmentsPhotosV2(ctx, storageDtos.UpsertAttachmentReq{
		ReferenceCode:     storageConstants.ATTACHMENT_REFERENCE_CODE_WEIGHT_BRIDGE_TICKETS,
		SourceReferenceID: weightBridgeTicket.ID,
		ClientID:          claim.GetLoggedInClientID(),
		Photos:            req.Photos,
	})

	if err != nil {
		return nil, err
	}

	err = tX.Commit()
	if err != nil {
		return nil, err
	}

	return &commonmodel.CreateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: weightBridgeTicket.ID,
		Data:        nil,
	}, nil
}

func (uc *WeightBridgeUseCase) UpdateWeightBridgeTicket(ctx context.Context, req dtos.CreateWeightBridgeTicket, id string) (*commonmodel.CreateResponse, error) {
	tX, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return nil, err
	}

	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	defer tX.Rollback()

	weightBridgeTicket, err := uc.weightBridgeRepo.GetWeightBridgeTicket(ctx, tX.DB(), models.WeightBridgeTicketCondition{
		Where: models.WeightBridgeTicketWhere{
			ID:       id,
			ClientID: claim.GetLoggedInClientID(),
		},
	})

	if err != nil {
		return nil, err
	}

	weightBridgeTicket.ReferenceID = req.ReferenceID
	weightBridgeTicket.VehicleAssetID = req.VehicleAssetID
	weightBridgeTicket.LoadType = req.LoadType
	weightBridgeTicket.InboundAt = req.InboundAt
	weightBridgeTicket.InboundWeightKg = req.InboundWeightKg
	weightBridgeTicket.InboundWeightBridgeLocationID = req.InboundWeightBridgeLocationID
	weightBridgeTicket.InboundOperatorUserID = req.InboundOperatorUserID
	weightBridgeTicket.InboundOperatorUserFirstName = req.InboundOperatorUserFirstName
	weightBridgeTicket.InboundOperatorUserLastName = req.InboundOperatorUserLastName
	weightBridgeTicket.InboundLocationID = req.InboundLocationID
	weightBridgeTicket.OutboundAt = req.OutboundAt
	weightBridgeTicket.OutboundWeightKg = req.OutboundWeightKg
	weightBridgeTicket.OutboundWeightBridgeLocationID = req.OutboundWeightBridgeLocationID
	weightBridgeTicket.OutboundOperatorUserID = req.OutboundOperatorUserID
	weightBridgeTicket.OutboundOperatorUserFirstName = req.OutboundOperatorUserFirstName
	weightBridgeTicket.OutboundOperatorUserLastName = req.OutboundOperatorUserLastName
	weightBridgeTicket.OutboundLocationID = req.OutboundLocationID
	weightBridgeTicket.NetWeight = req.NetWeight
	weightBridgeTicket.LocationRouteID = req.LocationRouteID
	weightBridgeTicket.Remark = req.Remark
	if req.OutboundAt.Valid {
		shiftCode := timehelpers.GetShiftCode(req.OutboundAt.Time)
		weightBridgeTicket.WorkShift = shiftCode
	}

	err = uc.weightBridgeRepo.UpdateWeightBridgeTicket(ctx, tX.DB(), weightBridgeTicket)
	if err != nil {
		return nil, err
	}

	_, err = uc.storageUsecase.UpdateAttachmentPhotos(ctx, storageDtos.UpsertAttachmentReq{
		ReferenceCode:     storageConstants.ATTACHMENT_REFERENCE_CODE_WEIGHT_BRIDGE_TICKETS,
		SourceReferenceID: weightBridgeTicket.ID,
		ClientID:          claim.GetLoggedInClientID(),
		Photos:            req.Photos,
	})

	if err != nil {
		return nil, err
	}

	err = tX.Commit()
	if err != nil {
		return nil, err
	}

	return &commonmodel.CreateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: weightBridgeTicket.ID,
		Data:        nil,
	}, nil
}

func (uc *WeightBridgeUseCase) DeleteWeightBridgeTicket(ctx context.Context, id string) (*commonmodel.CreateResponse, error) {

	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	weightBridgeTicket, err := uc.weightBridgeRepo.GetWeightBridgeTicket(ctx, uc.DB.DB(), models.WeightBridgeTicketCondition{
		Where: models.WeightBridgeTicketWhere{
			ID:       id,
			ClientID: claim.GetLoggedInClientID(),
		},
	})
	println(id)
	println("---------1")
	if err != nil {
		return nil, err
	}

	err = uc.weightBridgeRepo.DeleteWeightBridgeTicket(ctx, uc.DB.DB(), id)
	if err != nil {
		return nil, err
	}

	return &commonmodel.CreateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: weightBridgeTicket.ID,
		Data:        nil,
	}, nil
}
