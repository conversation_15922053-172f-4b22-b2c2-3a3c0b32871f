package dtos

import (
	"assetfindr/pkg/common/commonmodel"
	"strings"
	"time"

	"gopkg.in/guregu/null.v4"
)

type Location struct {
	ID                  string       `json:"id"`
	Name                string       `json:"name"`
	IsInventoryLocation bool         `json:"is_inventory_location"`
	Address             string       `json:"address"`
	Floor               string       `json:"floor"`
	Unit                string       `json:"unit"`
	StatusCode          string       `json:"status_code"`
	PICUserID           string       `json:"pic_user_id"`
	PICUser             LocationUser `json:"pic_user"`
	Description         string       `json:"description"`
	MapLat              float64      `json:"map_lat"`
	MapLong             float64      `json:"map_long"`
	CreatedAt           time.Time    `json:"created_at"`
	LocationNumber      string       `json:"location_number"`
	LocationTypeCode    string       `json:"location_type_code"`
	ParentLocationID    string       `json:"parent_location_id"`
	LocationAlias       string       `json:"location_alias"`
}

type LocationRoute struct {
	ID                     string    `json:"id"`
	StartLocationID        string    `json:"start_location_id"`
	StartLocationName      string    `json:"start_location_name"`
	EndLocationID          string    `json:"end_location_id"`
	EndLocationName        string    `json:"end_location_name"`
	OperationStartDate     null.Time `json:"operation_start_date"`
	OperationEndDate       null.Time `json:"operation_end_date"`
	DistanceKm             float64   `json:"distance_km"`
	IsEstimationDistanceKm bool      `json:"is_estimation_distance_km"`
}

type LocationUser struct {
	ID          string `json:"id"`
	Name        string `json:"name"`
	PhoneNumber string `json:"phone_number,omitempty"`
}

type CreateLocation struct {
	Name                string                 `json:"name" binding:"required"`
	IsInventoryLocation bool                   `json:"is_inventory_location"`
	Address             string                 `json:"address" binding:"required"`
	Floor               string                 `json:"floor"`
	Unit                string                 `json:"unit"`
	StatusCode          string                 `json:"status_code" binding:"required"`
	Description         string                 `json:"description"`
	PICUserID           string                 `json:"pic_user_id" binding:"required"`
	MapLat              float64                `json:"map_lat"`
	MapLong             float64                `json:"map_long"`
	Photos              []commonmodel.PhotoReq `json:"photos"`
}

type UpdateLocation struct {
	Name                string                 `json:"name"`
	IsInventoryLocation bool                   `json:"is_inventory_location"`
	Address             string                 `json:"address"`
	Floor               string                 `json:"floor"`
	Unit                string                 `json:"unit"`
	StatusCode          string                 `json:"status_code"`
	Description         string                 `json:"description"`
	PICUserID           string                 `json:"pic_user_id"`
	MapLat              float64                `json:"map_lat"`
	MapLong             float64                `json:"map_long"`
	Photos              []commonmodel.PhotoReq `json:"photos"`
}

type LocationListReq struct {
	commonmodel.ListRequest
	ShowDeleted       bool     `form:"show_deleted"`
	StatusCodes       []string `form:"status_codes"`
	LocationTypeCodes []string `form:"location_type_codes"`
}

type LocationRouteListReq struct {
	commonmodel.ListRequest
	StartLocationID    string `form:"start_location_id"`
	EndLocationID      string `form:"end_location_id"`
	EndLocationChildID string `form:"end_location_child_id"`
}

func (l *LocationListReq) Normalize() {
	l.ListRequest.Normalize()

	for _, val := range l.StatusCodes {
		if strings.ToLower(val) == "deleted" {
			l.ShowDeleted = true
		}
	}
}

func (l *LocationRouteListReq) Normalize() {
	l.ListRequest.Normalize()
}
