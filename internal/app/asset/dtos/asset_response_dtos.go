package dtos

import (
	"assetfindr/internal/app/asset/models"
	"assetfindr/pkg/common/commonmodel"
	"assetfindr/pkg/common/helpers"
	"assetfindr/pkg/common/helpers/calculationhelpers"
	"database/sql"
	"time"

	"github.com/jackc/pgtype"
	"gopkg.in/guregu/null.v4"
)

type AssetListVehicleItemResponse struct {
	ID           string    `json:"id"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at" `
	VehicleName  string    `json:"vehicle_name"`
	BrandID      string    `json:"brand_id"`
	BrandName    string    `json:"brand_name"`
	ModelNumber  string    `json:"model_number"`
	SerialNumber string    `json:"serial_number"`
	Rfid         string    `json:"rfid"`

	LastInspectedAt null.Time `json:"last_inspected_at"`

	// Category
	CategoryCode    string `json:"category_code"`
	SubCategoryCode string `json:"sub_catecategory_code"`
	CategoryName    string `json:"category_name"`
	SubCategoryName string `json:"sub_catecategory_name"`

	// Custom Category
	CustomCategoryID      string       `json:"custom_category_id"`
	CustomCategoryName    string       `json:"custom_category_name"`
	CustomSubCategoryID   *null.String `json:"custom_sub_category_id"`
	CustomSubCategoryName string       `json:"custom_sub_category_name"`

	ProductionYear          int                     `json:"production_year"`
	StatusCode              string                  `json:"status_code"`
	Status                  string                  `json:"status"`
	ReferenceNumber         string                  `json:"reference_number"`
	RegistrationNumber      string                  `json:"registration_number"`
	VehicleBodyType         string                  `json:"vehicle_body_type"`
	NumberOfTyres           null.Int                `json:"no_of_tyres"`
	AssingedToUserID        string                  `json:"assigned_to_user_id"`
	AssingedToUser          AssetAssignmentResponse `json:"assigned_to_user"`
	AttachedToParentAsset   string                  `json:"attached_to_parent_asset"`
	VehicleKM               float64                 `json:"vehicle_km"`
	VehicleHm               float64                 `json:"vehicle_hm"`
	NumberOfSpareTyres      int                     `json:"no_of_spare_tyres"`
	StatusInactiveTotalTime int                     `json:"status_inactive_total_time"`
	StatusInactiveStartTime *sql.NullTime           `json:"status_inactive_start_time"`
	UseKilometer            null.Bool               `json:"use_kilometer"`
	UseHourmeter            null.Bool               `json:"use_hourmeter"`
	UseKilometerHourmeter   null.Bool               `json:"use_kilometer_hourmeter"`
	AxleConfiguration       pgtype.JSONB            `json:"axle_configuration"`
	MaxRtdDiffTolerance     *null.Int               `json:"max_rtd_diff_tolerance"`
	Vehicle                 VehicleDetailResp       `json:"vehicle"`
	Address                 string                  `json:"address"`
	Photo                   string                  `json:"photo"`

	UseTyreOptimax  null.Bool `json:"use_tyre_optimax"`
	UseFleetOptimax null.Bool `json:"use_fleet_optimax"`

	PartnerOwnerID   string `json:"partner_owner_id"`
	PartnerOwnerNo   string `json:"partner_owner_no"`
	PartnerOwnerName string `json:"partner_owner_name"`

	HasLinkedAssetTyre bool    `json:"has_linked_asset_tyre"`
	TargetRTD          float64 `json:"target_rtd"`

	Models models.AssetModel `json:"models"`
}

type AssetVehicleListReceiver struct {
	AssetID                         string    `json:"asset_id"`
	RegistrationNumber              string    `json:"registration_number"`
	AssetVehicleBodyTypeCode        string    `json:"vehicle_body_type_code"`
	NumberOfTyres                   null.Int  `json:"no_of_tyres"`
	EngineModel                     string    `json:"engine_model"`
	TransmissionModel               string    `json:"transmission_model"`
	VrdNumber                       string    `json:"vrd_number_stnk"`
	VrdExpiryDate                   string    `json:"vrd_expiry_date"`
	VrdNumberAssignTo               string    `json:"vrd_number_assign_to"`
	EngineNumber                    string    `json:"engine_number"`
	ChassisNumber                   string    `json:"chassis_number"`
	GpsDeviceImei                   string    `json:"gps_device_imei" `
	RegistrationCertificateNumber   string    `json:"registration_certificate_number"`
	RegistrationCertificateAssignTo string    `json:"registration_certificate_assign_to"`
	InspectionBookNumber            string    `json:"inspection_book_number"`
	InspectionBookExpiryDate        string    `json:"inspection_book_expiry_date"`
	InspectionBookNumberAssignTo    string    `json:"inspection_book_number_assign_to"`
	ClientID                        string    `json:"client_id"`
	ID                              string    `json:"id"`
	CreatedAt                       time.Time `json:"created_at"`
	UpdatedAt                       time.Time `json:"updated_at" `
	VehicleName                     string    `json:"vehicle_name"`
	BrandID                         string    `json:"brand_id"`
	BrandName                       string    `json:"brand_name"`
	ModelNumber                     string    `json:"model_number"`
	SerialNumber                    string    `json:"serial_number"`
	CategoryCode                    string    `json:"category_code"`
	ProductionYear                  int       `json:"production_year"`
	StatusCode                      string    `json:"status_code"`
	Status                          string    `json:"status"`
	VehicleBodyType                 string    `json:"vehicle_body_type"`
	AssingedToUserID                string    `json:"assigned_to_user_id"`
	AttachedToParentAsset           string    `json:"attached_to_parent_asset"`
	Cost                            int       `json:"cost"`
	VehicleKM                       float64   `json:"vehicle_km" binding:"min=0" default:"0"`
	VehicleHm                       float64   `json:"vehicle_hm"`
	NumberOfSpareTyres              int       `json:"no_of_spare_tyres" binding:"min=0" default:"0"`
	UseKilometer                    null.Bool `json:"use_kilometer"`
	UseHourmeter                    null.Bool `json:"use_hourmeter"`
	UseKilometerHourmeter           null.Bool `json:"use_kilometer_hourmeter"`

	Photos []commonmodel.PhotoReq `json:"photos"`
}

func BuildAssetVehicleListResponse(assets []models.AssetVehicle, assignmentsMapByAssetId map[string]AssetAssignmentResponse) []AssetListVehicleItemResponse {
	response := []AssetListVehicleItemResponse{}

	for _, asset := range assets {
		photo := ""
		if asset.Asset.Photo.String != "" {
			photoUrl, _ := helpers.GenerateCloudStorageSignedURL(asset.Asset.Photo.String, time.Duration(24))
			photo = photoUrl
		}

		assingedToUserID := ""
		assetAssignment, assetAssignmentExists := assignmentsMapByAssetId[asset.AssetID]
		if assetAssignmentExists {
			assingedToUserID = assetAssignment.UserID
		}

		if asset.Vehicle.AxleConfiguration.Status == pgtype.Undefined {
			asset.Vehicle.AxleConfiguration.Status = pgtype.Null
		}

		responseItem := AssetListVehicleItemResponse{
			Models:          asset.Asset.AssetModel,
			ID:              asset.AssetID,
			CreatedAt:       asset.CreatedAt,
			UpdatedAt:       asset.UpdatedAt,
			VehicleName:     asset.Asset.Name,
			BrandID:         asset.Asset.BrandID,
			BrandName:       asset.Asset.Brand.BrandName,
			ModelNumber:     asset.Asset.ModelNumber,
			SerialNumber:    asset.Asset.SerialNumber,
			Rfid:            asset.Asset.Rfid,
			UseTyreOptimax:  asset.Asset.UseTyreOptimax,
			UseFleetOptimax: asset.Asset.UseFleetOptimax,
			Address:         asset.Asset.Address,
			Photo:           photo,
			LastInspectedAt: asset.LastInspectedAt,

			PartnerOwnerID:   asset.Asset.PartnerOwnerID,
			PartnerOwnerNo:   asset.Asset.PartnerOwnerNo,
			PartnerOwnerName: asset.Asset.PartnerOwnerName,

			// Category
			CategoryCode:    asset.Asset.AssetCategoryCode,
			CategoryName:    asset.Asset.AssetCategory.Label,
			SubCategoryCode: asset.Asset.SubCategoryCode,
			SubCategoryName: asset.Asset.SubCategory.Label,

			// Custom Category
			CustomCategoryID:      asset.Asset.CustomAssetCategoryID,
			CustomCategoryName:    asset.Asset.CustomAssetCategory.Name,
			CustomSubCategoryID:   asset.Asset.CustomAssetSubCategoryID,
			CustomSubCategoryName: asset.Asset.CustomAssetSubCategory.Name,

			ProductionYear:          asset.Asset.ProductionDate.Year(),
			StatusCode:              asset.Asset.AssetStatusCode,
			Status:                  asset.Asset.AssetStatus.Label,
			ReferenceNumber:         asset.Asset.ReferenceNumber,
			RegistrationNumber:      asset.RegistrationNumber,
			VehicleBodyType:         asset.AssetVehicleBodyType.Label,
			NumberOfTyres:           asset.NumberOfTyres,
			AssingedToUserID:        assingedToUserID,
			AssingedToUser:          assetAssignment,
			AttachedToParentAsset:   "",
			VehicleKM:               asset.VehicleKM,
			VehicleHm:               calculationhelpers.Div100(asset.VehicleHm),
			UseKilometer:            asset.UseKilometer,
			UseHourmeter:            asset.UseHourmeter,
			UseKilometerHourmeter:   asset.UseKilometerHourmeter,
			NumberOfSpareTyres:      int(asset.NumberOfSpareTyres.Int64),
			StatusInactiveTotalTime: asset.Asset.StatusInactiveTotalTime,
			StatusInactiveStartTime: asset.Asset.StatusInactiveStartTime,
			AxleConfiguration:       asset.AxleConfiguration,
			MaxRtdDiffTolerance:     asset.MaxRtdDiffTolerance,
			HasLinkedAssetTyre:      asset.CountLinkedTyreByTrigger > 0,
			Vehicle: VehicleDetailResp{
				ID:                asset.VehicleID,
				BrandID:           asset.Vehicle.BrandID,
				BrandName:         asset.Vehicle.Brand.BrandName,
				VehicleTypeCode:   asset.Vehicle.VehicleTypeCode,
				Model:             asset.Vehicle.VehicleModel,
				EngineModel:       asset.Vehicle.EngineModel,
				TransmissionModel: asset.Vehicle.TransmissionModel,
				IsGeneral:         asset.Vehicle.IsGeneral(),
				VehicleNumber:     asset.Vehicle.VehicleNumber,
				AxleConfiguration: asset.Vehicle.AxleConfiguration,
			},
		}

		if asset.TargetRemoval != nil {
			responseItem.TargetRTD = asset.TargetRemoval.TargetRTD
		}

		response = append(response, responseItem)
	}

	return response
}

type AssetListItemResponse struct {
	ID                    string    `json:"id"`
	CreatedAt             time.Time `json:"created_at"`
	UpdatedAt             time.Time `json:"updated_at" `
	Name                  string    `json:"name"`
	BrandID               string    `json:"brand_id"`
	BrandName             string    `json:"brand_name"`
	ModelNumber           string    `json:"model_number"`
	SerialNumber          string    `json:"serial_number"`
	ReferenceNumber       string    `json:"reference_number"`
	CategoryCode          string    `json:"category_code"`
	ProductionYear        int       `json:"production_year"`
	StatusCode            string    `json:"status_code"`
	Status                string    `json:"status"`
	AssingedToUserID      string    `json:"assigned_to_user_id"`
	AttachedToParentAsset string    `json:"attached_to_parent_asset"`
	UseTyreOptimax        null.Bool `json:"use_tyre_optimax"`
	UseFleetOptimax       null.Bool `json:"use_fleet_optimax"`
}

func BuildAssetListResponse(assets []models.Asset) []AssetListItemResponse {
	response := []AssetListItemResponse{}

	for _, asset := range assets {
		response = append(response, AssetListItemResponse{
			ID:                    asset.ID,
			CreatedAt:             asset.CreatedAt,
			UpdatedAt:             asset.UpdatedAt,
			Name:                  asset.Name,
			BrandID:               asset.BrandID,
			BrandName:             asset.Brand.BrandName,
			ModelNumber:           asset.ModelNumber,
			SerialNumber:          asset.SerialNumber,
			ReferenceNumber:       asset.ReferenceNumber,
			CategoryCode:          asset.AssetCategoryCode,
			ProductionYear:        asset.ProductionDate.Year(),
			StatusCode:            asset.AssetStatusCode,
			Status:                asset.AssetStatus.Label,
			AssingedToUserID:      "",
			AttachedToParentAsset: "",
			UseTyreOptimax:        asset.UseTyreOptimax,
			UseFleetOptimax:       asset.UseFleetOptimax,
		})
	}

	return response
}

type AssetSelectionItem struct {
	ID               string                     `json:"id"`
	CreatedAt        time.Time                  `json:"created_at"`
	UpdatedAt        time.Time                  `json:"updated_at" `
	Name             string                     `json:"name"`
	SerialNumber     string                     `json:"serial_number"`
	ReferenceNumber  string                     `json:"reference_number"`
	BrandID          string                     `json:"brand_id"`
	BrandName        string                     `json:"brand_name"`
	ModelName        string                     `json:"model_name"`
	AssetVehicle     *AssetVehicleSelectionItem `json:"asset_vehicle"`
	AssetTyre        *AssetTyreSelectionItem    `json:"asset_tyre"`
	PartnerOwnerId   string                     `json:"partner_owner_id"`
	PartnerOwnerName string                     `json:"partner_owner_name"`
	CategoryName     string                     `json:"category_name"`
	SubCategoryName  string                     `json:"sub_catecategory_name"`
	UseTyreOptimax   null.Bool                  `json:"use_tyre_optimax"`
	UseFleetOptimax  null.Bool                  `json:"use_fleet_optimax"`
	StatusCode       string                     `json:"status_code"`
}

type AssetVehicleSelectionItem struct {
	ReferenceNumber      string `json:"reference_number"`
	SerialNumber         string `json:"serial_number"`
	RegistrationNumber   string `json:"registration_number"`
	AssetVehicleBodyType string `json:"vehicle_body_type"`
}

type AssetTyreSelectionItem struct {
	PatternType  string `json:"pattern_type"`
	TyrePosition int    `json:"tyre_position"`
	SectionWidth string `json:"section_width"`
	Construction string `json:"construction"`
	RimDiameter  string `json:"rim_diameter"`

	ParentAssetReferenceNumber string `json:"parent_asset_reference_number"`
	ParentAssetSerialNumber    string `json:"parent_asset_serial_number"`
}

func BuildAssetSelectionListResponse(assets []models.Asset) []AssetSelectionItem {
	response := make([]AssetSelectionItem, 0, len(assets))
	for _, asset := range assets {
		item := AssetSelectionItem{
			ID:               asset.ID,
			CreatedAt:        asset.CreatedAt,
			UpdatedAt:        asset.UpdatedAt,
			Name:             asset.Name,
			SerialNumber:     asset.SerialNumber,
			ReferenceNumber:  asset.ReferenceNumber,
			BrandID:          asset.BrandID,
			BrandName:        asset.Brand.BrandName,
			ModelName:        asset.AssetModel.AssetModelName,
			PartnerOwnerId:   asset.PartnerOwnerID,
			PartnerOwnerName: asset.PartnerOwnerName,
			CategoryName:     asset.AssetCategory.Label,
			SubCategoryName:  asset.SubCategory.Label,
			UseTyreOptimax:   asset.UseTyreOptimax,
			UseFleetOptimax:  asset.UseFleetOptimax,
			StatusCode:       asset.AssetStatusCode,
		}

		if asset.AssetVehicle != nil {
			item.AssetVehicle = &AssetVehicleSelectionItem{
				ReferenceNumber:      asset.ReferenceNumber,
				SerialNumber:         asset.SerialNumber,
				RegistrationNumber:   asset.AssetVehicle.RegistrationNumber,
				AssetVehicleBodyType: asset.AssetVehicle.AssetVehicleBodyType.Label,
			}
		}

		if asset.AssetTyre != nil {
			item.AssetTyre = &AssetTyreSelectionItem{
				PatternType:  asset.AssetTyre.Tyre.PatternType,
				SectionWidth: asset.AssetTyre.Tyre.SectionWidth,
				Construction: asset.AssetTyre.Tyre.Construction,
				RimDiameter:  asset.AssetTyre.Tyre.RimDiameter,
			}

			if asset.ChildAssetLinked != nil {
				if asset.ChildAssetLinked.AssetLinkedAssetVehicleTyre != nil {
					item.AssetTyre.TyrePosition = asset.ChildAssetLinked.
						AssetLinkedAssetVehicleTyre.TyrePosition
				}

				if asset.ChildAssetLinked.ParentAsset.AssetID != "" {
					item.AssetTyre.ParentAssetSerialNumber = asset.ChildAssetLinked.ParentAsset.Asset.SerialNumber
					item.AssetTyre.ParentAssetReferenceNumber = asset.ChildAssetLinked.ParentAsset.Asset.ReferenceNumber
				}
			}
		}

		response = append(response, item)
	}

	return response
}

type AssetCountResp struct {
	AlertInLast72Hours int `json:"alert_in_last_72_hours"`
	Maintenance        int `json:"maintenance"`
	Active             int `json:"active"`
	Inactive           int `json:"inactive"`
}
