package dtos

import (
	"assetfindr/internal/app/asset/models"
	userIdentityModel "assetfindr/internal/app/user-identity/models"
	"assetfindr/pkg/common/commonmodel"
	"time"

	"gopkg.in/guregu/null.v4"
)

type WeightBridgeTicket struct {
	ID                                string        `json:"id"`
	ReferenceID                       string        `json:"reference_id"`
	VehicleAssetID                    string        `json:"vehicle_asset_id"`
	VehicleReferenceNumber            string        `json:"vehicle_reference_number"`
	VehicleSerialNumber               string        `json:"vehicle_serial_number"`
	VehicleName                       string        `json:"vehicle_name"`
	LoadType                          string        `json:"load_type"`
	InboundAt                         null.Time     `json:"inbound_at"`
	InboundWeightKg                   int           `json:"inbound_weight_kg"`
	InboundWeightBridgeLocationID     string        `json:"inbound_weight_bridge_location_id"`
	InboundWeightBridgeLocationLabel  string        `json:"inbound_weight_bridge_location_label"`
	InboundWeightBridgeLocationAlias  string        `json:"inbound_weight_bridge_location_alias"`
	InboundOperatorUserID             string        `json:"inbound_operator_user_id"`
	InboundOperatorUserFirstName      string        `json:"inbound_operator_user_first_name"`
	InboundOperatorUserLastName       string        `json:"inbound_operator_user_last_name"`
	InboundOperatorUserFullName       string        `json:"inbound_operator_user_full_name"`
	InboundOperatorUserPhoneNumber    string        `json:"inbound_operator_user_phone_number"`
	InboundLocationID                 string        `json:"inbound_location_id"`
	InboundLocationLabel              string        `json:"inbound_location_label"`
	InboundLocationAlias              string        `json:"inbound_location_alias"`
	OutboundAt                        null.Time     `json:"outbound_at"`
	OutboundWeightKg                  int           `json:"outbound_weight_kg"`
	OutboundWeightBridgeLocationID    string        `json:"outbound_weight_bridge_location_id"`
	OutboundWeightBridgeLocationLabel string        `json:"outbound_weight_bridge_location_label"`
	OutboundWeightBridgeLocationAlias string        `json:"outbound_weight_bridge_location_alias"`
	OutboundOperatorUserID            string        `json:"outbound_operator_user_id"`
	OutboundOperatorUserFirstName     string        `json:"outbound_operator_user_first_name"`
	OutboundOperatorUserLastName      string        `json:"outbound_operator_user_last_name"`
	OutboundOperatorUserFullName      string        `json:"outbound_operator_user_full_name"`
	OutboundOperatorUserPhoneNumber   string        `json:"outbound_operator_user_phone_number"`
	OutboundLocationID                string        `json:"outbound_location_id"`
	OutboundLocationLabel             string        `json:"outbound_location_label"`
	OutboundLocationAlias             string        `json:"outbound_location_alias"`
	NetWeight                         float64       `json:"net_weight"`
	WorkShift                         string        `json:"work_shift"`
	LocationRouteID                   string        `json:"location_route_id"`
	LocationRoute                     LocationRoute `json:"location_route"`
	Remark                            string        `json:"remark"`
	CreatedAt                         time.Time     `json:"created_at"`
	CreatedBy                         string        `json:"created_by"`
	CreatedByFullName                 string        `json:"created_by_full_name"`
	CreatedByPhoneNumber              string        `json:"created_by_phone_number"`
	UpdatedAt                         time.Time     `json:"updated_at"`
}

type CreateWeightBridgeTicket struct {
	ReferenceID                    string                 `json:"reference_id"`
	VehicleAssetID                 string                 `json:"vehicle_asset_id"`
	LoadType                       string                 `json:"load_type"`
	InboundAt                      null.Time              `json:"inbound_at"`
	InboundWeightKg                int                    `json:"inbound_weight_kg"`
	InboundWeightBridgeLocationID  string                 `json:"inbound_weight_bridge_location_id"`
	InboundOperatorUserID          string                 `json:"inbound_operator_user_id"`
	InboundOperatorUserFirstName   string                 `json:"inbound_operator_user_first_name"`
	InboundOperatorUserLastName    string                 `json:"inbound_operator_user_last_name"`
	InboundLocationID              string                 `json:"inbound_location_id"`
	OutboundAt                     null.Time              `json:"outbound_at"`
	OutboundWeightKg               int                    `json:"outbound_weight_kg"`
	OutboundWeightBridgeLocationID string                 `json:"outbound_weight_bridge_location_id"`
	OutboundOperatorUserID         string                 `json:"outbound_operator_user_id"`
	OutboundOperatorUserFirstName  string                 `json:"outbound_operator_user_first_name"`
	OutboundOperatorUserLastName   string                 `json:"outbound_operator_user_last_name"`
	OutboundLocationID             string                 `json:"outbound_location_id"`
	NetWeight                      float64                `json:"net_weight"`
	LocationRouteID                string                 `json:"location_route_id"`
	Remark                         string                 `json:"remark"`
	Photos                         []commonmodel.PhotoReq `json:"photos"`
}

type WeightBridgeTicketListReq struct {
	commonmodel.ListRequest
	CreatedStartDate string `form:"created_start_date"` //TODO: will need to change this
	CreatedEndDate   string `form:"created_end_date"`   //TODO: will need to change this
}

func (wbt *WeightBridgeTicketListReq) Normalize() {
	wbt.ListRequest.Normalize()
}

func (wbt *WeightBridgeTicket) Set(weightBridgeTicket models.WeightBridgeTicket, usersMapById map[string]userIdentityModel.User) {
	wbt.ID = weightBridgeTicket.ID
	wbt.ReferenceID = weightBridgeTicket.ReferenceID
	wbt.LoadType = weightBridgeTicket.LoadType

	wbt.VehicleAssetID = weightBridgeTicket.VehicleAssetID
	wbt.VehicleReferenceNumber = weightBridgeTicket.AssetVehicle.Asset.ReferenceNumber
	wbt.VehicleSerialNumber = weightBridgeTicket.AssetVehicle.Asset.SerialNumber
	wbt.VehicleName = weightBridgeTicket.AssetVehicle.Asset.Name

	wbt.InboundAt = weightBridgeTicket.InboundAt
	wbt.InboundWeightKg = weightBridgeTicket.InboundWeightKg
	wbt.InboundWeightBridgeLocationID = weightBridgeTicket.InboundWeightBridgeLocationID
	wbt.InboundWeightBridgeLocationLabel = weightBridgeTicket.InboundWeightBridgeLocation.Name
	wbt.InboundWeightBridgeLocationAlias = weightBridgeTicket.InboundWeightBridgeLocation.LocationAlias
	wbt.InboundOperatorUserID = weightBridgeTicket.InboundOperatorUserID
	wbt.InboundOperatorUserFullName = weightBridgeTicket.InboundOperatorUserFirstName + " " + weightBridgeTicket.InboundOperatorUserLastName
	wbt.InboundOperatorUserFirstName = weightBridgeTicket.InboundOperatorUserFirstName
	wbt.InboundOperatorUserLastName = weightBridgeTicket.InboundOperatorUserLastName
	wbt.InboundLocationID = weightBridgeTicket.InboundLocationID
	wbt.InboundLocationLabel = weightBridgeTicket.InboundLocation.Name
	wbt.InboundLocationAlias = weightBridgeTicket.InboundLocation.LocationAlias

	wbt.OutboundAt = weightBridgeTicket.OutboundAt
	wbt.OutboundWeightKg = weightBridgeTicket.OutboundWeightKg
	wbt.OutboundWeightBridgeLocationID = weightBridgeTicket.OutboundWeightBridgeLocationID
	wbt.OutboundWeightBridgeLocationLabel = weightBridgeTicket.OutboundWeightBridgeLocation.Name
	wbt.OutboundWeightBridgeLocationAlias = weightBridgeTicket.OutboundWeightBridgeLocation.LocationAlias
	wbt.OutboundOperatorUserID = weightBridgeTicket.OutboundOperatorUserID
	wbt.OutboundOperatorUserFullName = weightBridgeTicket.OutboundOperatorUserFirstName + " " + weightBridgeTicket.OutboundOperatorUserLastName
	wbt.OutboundOperatorUserFirstName = weightBridgeTicket.OutboundOperatorUserFirstName
	wbt.OutboundOperatorUserLastName = weightBridgeTicket.OutboundOperatorUserLastName
	wbt.OutboundLocationID = weightBridgeTicket.OutboundLocationID
	wbt.OutboundLocationLabel = weightBridgeTicket.OutboundLocation.Name
	wbt.OutboundLocationAlias = weightBridgeTicket.OutboundLocation.LocationAlias

	wbt.NetWeight = weightBridgeTicket.NetWeight
	wbt.LocationRouteID = weightBridgeTicket.LocationRouteID
	wbt.LocationRoute = LocationRoute{
		ID:                     weightBridgeTicket.LocationRoute.ID,
		StartLocationID:        weightBridgeTicket.LocationRoute.StartLocationID,
		StartLocationName:      weightBridgeTicket.LocationRoute.StartLocation.Name,
		EndLocationID:          weightBridgeTicket.LocationRoute.EndLocationID,
		EndLocationName:        weightBridgeTicket.LocationRoute.EndLocation.Name,
		OperationStartDate:     weightBridgeTicket.LocationRoute.OperationStartDate,
		OperationEndDate:       weightBridgeTicket.LocationRoute.OperationEndDate,
		DistanceKm:             weightBridgeTicket.LocationRoute.DistanceKm,
		IsEstimationDistanceKm: weightBridgeTicket.LocationRoute.IsEstimationDistanceKm,
	}
	wbt.Remark = weightBridgeTicket.Remark
	wbt.CreatedAt = weightBridgeTicket.CreatedAt
	wbt.CreatedBy = weightBridgeTicket.CreatedBy
	wbt.WorkShift = weightBridgeTicket.WorkShift

	createdByUser := usersMapById[weightBridgeTicket.CreatedBy]
	wbt.CreatedByFullName = createdByUser.FirstName + " " + createdByUser.LastName
	wbt.CreatedByPhoneNumber = createdByUser.PhoneNumber
	wbt.UpdatedAt = weightBridgeTicket.UpdatedAt
}
