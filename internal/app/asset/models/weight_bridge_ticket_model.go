package models

import (
	"assetfindr/pkg/common/commonmodel"
	"time"

	"gopkg.in/guregu/null.v4"
	"gorm.io/gorm"
)

type WeightBridgeTicket struct {
	commonmodel.ModelV2
	ReferenceID                    string        `json:"reference_id"`
	VehicleAssetID                 string        `json:"vehicle_asset_id"`
	AssetVehicle                   AssetVehicle  `json:"asset_vehicle" gorm:"foreignKey:VehicleAssetID;references:AssetID"`
	LoadType                       string        `json:"load_type"`
	InboundAt                      null.Time     `json:"inbound_at"`
	InboundWeightKg                int           `json:"inbound_weight_kg"`
	InboundWeightBridgeLocationID  string        `json:"inbound_weight_bridge_location_id"`
	InboundWeightBridgeLocation    Location      `json:"inbound_weight_bridge_location" gorm:"foreignKey:InboundWeightBridgeLocationID;references:ID"`
	InboundOperatorUserID          string        `json:"inbound_operator_user_id"`
	InboundOperatorUserFirstName   string        `json:"inbound_operator_user_first_name"`
	InboundOperatorUserLastName    string        `json:"inbound_operator_user_last_name"`
	InboundLocationID              string        `json:"inbound_location_id"`
	InboundLocation                Location      `json:"inbound_location" gorm:"foreignKey:InboundLocationID;references:ID"`
	OutboundAt                     null.Time     `json:"outbound_at"`
	OutboundWeightKg               int           `json:"outbound_weight_kg"`
	OutboundWeightBridgeLocationID string        `json:"outbound_weight_bridge_location_id"`
	OutboundWeightBridgeLocation   Location      `json:"outbound_weight_bridge_location" gorm:"foreignKey:OutboundWeightBridgeLocationID;references:ID"`
	OutboundOperatorUserID         string        `json:"outbound_operator_user_id"`
	OutboundOperatorUserFirstName  string        `json:"outbound_operator_user_first_name"`
	OutboundOperatorUserLastName   string        `json:"outbound_operator_user_last_name"`
	OutboundLocationID             string        `json:"outbound_location_id"`
	OutboundLocation               Location      `json:"outbound_location" gorm:"foreignKey:OutboundLocationID;references:ID"`
	NetWeight                      float64       `json:"net_weight"`
	WorkShift                      string        `json:"work_shift"`
	LocationRouteID                string        `json:"location_route_id"`
	LocationRoute                  LocationRoute `json:"location_route" gorm:"foreignKey:LocationRouteID;references:ID"`
	Remark                         string        `json:"remark"`
}

func (wbt WeightBridgeTicket) TableName() string {
	return "ams_weight_bridge_tickets"
}

func (wbt *WeightBridgeTicket) BeforeCreate(db *gorm.DB) error {
	wbt.SetUUID("wbt")
	wbt.ModelV2.BeforeCreate(db)
	return nil
}

func (wbt *WeightBridgeTicket) BeforeUpdate(db *gorm.DB) error {
	wbt.ModelV2.BeforeUpdate(db)
	return nil
}

type WeightBridgeTicketWhere struct {
	ID              string
	ClientID        string
	OutboundAtStart time.Time
	OutboundAtEnd   time.Time
}

type WeightBridgeTicketPreload struct {
	AssetVehicle                 bool
	InboundWeightBridgeLocation  bool
	InboundLocation              bool
	OutboundWeightBridgeLocation bool
	OutboundLocation             bool
	LocationRoute                bool
}

type WeightBridgeTicketCondition struct {
	Where   WeightBridgeTicketWhere
	Preload WeightBridgeTicketPreload
	Columns []string
}

type GetWeightBridgeTicketListParam struct {
	commonmodel.ListRequest
	Cond WeightBridgeTicketCondition
}
