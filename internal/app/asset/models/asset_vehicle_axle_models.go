package models

import (
	"assetfindr/internal/app/asset/constants"

	"github.com/jackc/pgtype"
)

func AxleConfigurationJSONBToStruct(newAxle pgtype.JSONB) ([]AxleConfiguration, error) {
	rAxleConfiguration := []AxleConfiguration{}
	err := newAxle.AssignTo(&rAxleConfiguration)

	return rAxleConfiguration, err
}

func MapPositionToPressureFromAxle(axle []AxleConfiguration) map[int]AxleConfiguration {
	data := map[int]AxleConfiguration{}
	number := 1
	for i := range axle {
		switch axle[i].Axle {
		case constants.AXLE_CONF_TWO_TYRES_WITH_STEERING_WHEEL, constants.AXLE_CONF_TWO_TYRES,
			constants.AXLE_CONF_TWO_TYRES_WITH_SPINDLE:
			for j := 0; j < 2; j++ {
				data[number] = axle[i]
				number++
			}
		case constants.AXLE_CONF_FOUR_TYRES, constants.AXLE_CONF_FOUR_TYRES_WITH_SPINDLE:
			for j := 0; j < 4; j++ {
				data[number] = axle[i]
				number++
			}
		case constants.AXLE_CONF_ONE_SPARE_TYRE:
			// no need for spare tyre
		}
	}

	return data
}

func MapTyrePairPositionByAxle(axle []AxleConfiguration) map[int]int {
	data := map[int]int{}
	number := 1
	for i := range axle {
		switch axle[i].Axle {
		case constants.AXLE_CONF_TWO_TYRES_WITH_STEERING_WHEEL, constants.AXLE_CONF_TWO_TYRES,
			constants.AXLE_CONF_TWO_TYRES_WITH_SPINDLE:
			number += 2
		case constants.AXLE_CONF_FOUR_TYRES, constants.AXLE_CONF_FOUR_TYRES_WITH_SPINDLE:
			for i := 0; i < 2; i++ {
				data[number] = number + 1
				number += 2
			}
		case constants.AXLE_CONF_ONE_SPARE_TYRE:
			// no need for spare tyre
		}
	}

	return data
}
