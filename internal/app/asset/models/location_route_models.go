package models

import (
	"assetfindr/pkg/common/commonmodel"

	"gopkg.in/guregu/null.v4"
	"gorm.io/gorm"
)

type LocationRoute struct {
	commonmodel.ModelV2
	StartLocationID        string
	StartLocation          Location `gorm:"foreignkey:StartLocationID;references:ID"`
	EndLocationID          string
	EndLocation            Location  `gorm:"foreignkey:EndLocationID;references:ID"`
	OperationStartDate     null.Time `json:"operation_start_date" gorm:"default:null"`
	OperationEndDate       null.Time `json:"operation_end_date" gorm:"default:null"`
	DistanceKm             float64
	IsEstimationDistanceKm bool `gorm:"default:false" json:"is_estimation_distance_km"`
}

func (lr LocationRoute) TableName() string {
	return "ams_location_routes"
}

func (lr *LocationRoute) BeforeCreate(db *gorm.DB) error {
	lr.SetUUID("lro")
	lr.ModelV2.BeforeCreate(db)
	return nil
}

func (lr *LocationRoute) BeforeUpdate(db *gorm.DB) error {
	lr.ModelV2.BeforeUpdate(db)
	return nil
}

type LocationRouteWhere struct {
	ID                 string
	ClientID           string
	StartLocationID    string
	EndLocationID      string
	EndLocationChildID string
}

type LocationRoutePreload struct {
	EndLocation   bool
	StartLocation bool
}

type LocationRouteCondition struct {
	Where   LocationRouteWhere
	Preload LocationRoutePreload
	Columns []string
}

type GetLocationRouteListParam struct {
	commonmodel.ListRequest
	Cond LocationRouteCondition
}
