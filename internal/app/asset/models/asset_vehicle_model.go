package models

import (
	"assetfindr/internal/app/asset/constants"
	"assetfindr/pkg/common/commonmodel"
	"database/sql"
	"time"

	"github.com/jackc/pgtype"
	"gopkg.in/guregu/null.v4"
	"gorm.io/gorm"
)

type AssetVehicle struct {
	commonmodel.ModelV2NoIDField
	AssetID                         string                    `json:"asset_id" gorm:"type:varchar(40);primaryKey" pgjsonb:"asset_id"`
	Asset                           Asset                     `json:"asset"`
	RegistrationNumber              string                    `json:"registration_number" gorm:"type:varchar(100)" pgjsonb:"registration_number"`
	AssetVehicleBodyTypeID          string                    `json:"vehicle_body_type_code" gorm:"type:varchar(20);default:null" pgjsonb:"asset_vehicle_body_type_id"`
	AssetVehicleBodyType            AssetVehicleBodyType      `json:"asset_vehicle_body_type"`
	NumberOfTyres                   null.Int                  `json:"no_of_tyres" pgjsonb:"number_of_tyres"`
	EngineModel                     string                    `json:"engine_model" gorm:"type:varchar(100)" pgjsonb:"engine_model"`
	TransmissionModel               string                    `json:"transmission_model" gorm:"type:varchar(100)" pgjsonb:"transmission_model"`
	VrdNumber                       string                    `json:"vrd_number_stnk" gorm:"column:vrd_number;type:varchar(100)" pgjsonb:"vrd_number"`
	VrdExpiryDate                   time.Time                 `json:"vrd_expiry_date"  pgjsonb:"vrd_expiry_date"`
	VrdNumberAssignTo               string                    `json:"vrd_number_assign_to" gorm:"type:varchar(40)" pgjsonb:"vrd_number_assign_to"`
	EngineNumber                    string                    `json:"engine_number" gorm:"type:varchar(100)" pgjsonb:"engine_number"`
	ChassisNumber                   string                    `json:"chassis_number" gorm:"type:varchar(100)" pgjsonb:"chassis_number"`
	GpsDeviceImei                   sql.NullString            `json:"gps_device_imei" gorm:"default:null;type:varchar(100)" pgjsonb:"gps_device_imei"`
	RegistrationCertificateNumber   string                    `json:"registration_certificate_number" gorm:"column:registration_certificate_number;type:varchar(100)" pgjsonb:"registration_certificate_number"`
	RegistrationCertificateAssignTo string                    `json:"registration_certificate_assign_to" gorm:"type:varchar(40)" pgjsonb:"registration_certificate_assign_to"`
	InspectionBookNumber            string                    `json:"inspection_book_number" gorm:"column:inspection_book_number;type:varchar(100)" pgjsonb:"inspection_book_number"`
	InspectionBookNumberAssignTo    string                    `json:"inspection_book_number_assign_to" gorm:"type:varchar(40)" pgjsonb:"inspection_book_number_assign_to"`
	InspectionBookExpiryDate        time.Time                 `json:"inspection_book_expiry_date" pgjsonb:"inspection_book_expiry_date"`
	VehicleKM                       float64                   `json:"vehicle_km" pgjsonb:"vehicle_km"`
	VehicleHm                       int                       `json:"vehicle_hm" pgjsonb:"vehicle_hm"`
	NumberOfSpareTyres              null.Int                  `json:"no_of_spare_tyres" pgjsonb:"number_of_spare_tyres"`
	UseKilometer                    null.Bool                 `json:"use_kilometer" gorm:"default:false" pgjsonb:"use_kilometer"`
	UseHourmeter                    null.Bool                 `json:"use_hourmeter" gorm:"default:false" pgjsonb:"use_hourmeter"`
	UseKilometerHourmeter           null.Bool                 `json:"use_kilometer_hourmeter" gorm:"default:false" pgjsonb:"use_kilometer_hourmeter"`
	VehicleID                       string                    `json:"vehicle_id" gorm:"type:varchar(40)" pgjsonb:"vehicle_id"`
	AxleConfiguration               pgtype.JSONB              `gorm:"default:null" json:"axle_configuration" pgjsonb:"axle_configuration"`
	MaxRtdDiffTolerance             *null.Int                 `gorm:"default:null" json:"max_rtd_diff_tolerance" pgjsonb:"max_rtd_diff_tolerance"`
	CountLinkedTyreByTrigger        int                       `json:"count_linked_tyre_by_trigger" pgjsonb:"count_linked_tyre_by_trigger"`
	LastInspectedAt                 null.Time                 `json:"last_inspected_at" pgjsonb:"last_inspected_at"`
	TargetRemovalID                 string                    `json:"target_removal_id" gorm:"type:varchar(40)" pgjsonb:"target_removal_id"`
	Vehicle                         Vehicle                   `json:"vehicle" gorm:"foreignKey:VehicleID;references:ID"`
	TargetRemoval                   *VehicleTargetTyreRemoval `json:"target_removal" gorm:"foreignKey:TargetRemovalID;references:ID"`
}

// TableName specifies the database table name for the AmsAssetVehicle model
func (a *AssetVehicle) TableName() string {
	return "ams_asset_vehicles"
}

func (a *AssetVehicle) BeforeCreate(db *gorm.DB) error {
	a.ModelV2NoIDField.BeforeCreate(db)
	return nil
}

func (a *AssetVehicle) BeforeUpdate(db *gorm.DB) error {
	a.ModelV2NoIDField.BeforeUpdate(db)
	return nil
}

type AssetVehicleCondition struct {
	Where       AssetVehicleWhere
	Preload     AssetVehiclePreload
	Columns     []string
	IsForUpdate bool
}

type AssetVehicleWhere struct {
	AssetID               string
	AssetIDs              []string
	ClientID              string
	ExpiryInMoreThan30Day bool
	EngineNumbers         []string
	RegistrationNumbers   []string
	StatusCode            []string
	VehicleID             string
	VehicleIDs            []string
	Categories            []string
	SubCategories         []string
	Brands                []string
	Models                []string
	CustomCategories      []string
	CustomSubCategories   []string
	Locations             []string
	CreateOn              string
	UpdatedStartDate      string
	UpdatedEndDate        string
	PartnerOwnerID        string
	UseTyreOptimax        bool
	UseFleetOptimax       null.Bool
	HasAxleConfiguration  bool
	HasLinkedAssetTyre    bool
	AssignUserID          string
}

type AssetVehiclePreload struct {
	AssetVehicleBodyType bool
	AssetBrand           bool
	AssetAssetStatus     bool
	Asset                bool
	Vehicle              bool
	Category             bool
	CustomAssetCategory  bool
	AssetModels          bool
	TargetRemoval        bool
}

type GetAssetVehicleListParam struct {
	commonmodel.ListRequest
	Cond AssetVehicleCondition
}

type AxleConfiguration struct {
	Axle              string     `json:"axle"`
	PressureMin       null.Float `json:"pressure_min"`
	PressureMax       null.Float `json:"pressure_max"`
	PressureTolerance null.Float `json:"pressure_tolerance"`
}

func AxleConfigurationsTyrePositionToRowNumber(axle []AxleConfiguration) map[int]int {
	data := map[int]int{}
	tyrePosition := 1
	rowPositon := 1
	for i := range axle {
		n := 0

		switch axle[i].Axle {
		case constants.AXLE_CONF_TWO_TYRES_WITH_STEERING_WHEEL,
			constants.AXLE_CONF_TWO_TYRES, constants.AXLE_CONF_TWO_TYRES_WITH_SPINDLE:
			n = 2
		case constants.AXLE_CONF_FOUR_TYRES, constants.AXLE_CONF_FOUR_TYRES_WITH_SPINDLE:
			n = 4
		case constants.AXLE_CONF_ONE_SPARE_TYRE:
			n = 1
		}

		for j := 0; j < n; j++ {
			data[tyrePosition] = rowPositon
			tyrePosition++
		}
		rowPositon++
	}

	return data
}

// Temporary
func AxleConfigurationOldToNew(oldAxle []string) []AxleConfiguration {
	axleConfiguration := []AxleConfiguration{}
	for i := 0; i < len(oldAxle); i++ {
		axleConfiguration = append(axleConfiguration, AxleConfiguration{
			Axle: oldAxle[i],
		})
	}
	return axleConfiguration
}

func AxleConfigurationNewToOld(newAxle pgtype.JSONB) []string {
	rAxleConfiguration := []AxleConfiguration{}
	_ = newAxle.AssignTo(&rAxleConfiguration)

	axleConfiguration := []string{}
	for i := 0; i < len(rAxleConfiguration); i++ {
		axleConfiguration = append(axleConfiguration, rAxleConfiguration[i].Axle)
	}
	return axleConfiguration
}
