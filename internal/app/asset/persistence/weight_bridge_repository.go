package persistence

import (
	"assetfindr/internal/app/asset/models"
	"assetfindr/internal/app/asset/repository"
	"assetfindr/internal/errorhandler"
	"assetfindr/internal/infrastructure/database"
	"context"

	"gorm.io/gorm"
)

type weightBridgeRepository struct{}

func NewWeightBridgeRepository() repository.WeightBridgeRepository {
	return &weightBridgeRepository{}
}

func enrichWeightBridgeTicketQueryWithWhere(query *gorm.DB, where models.WeightBridgeTicketWhere) {
	if where.ID != "" {
		query.Where("ams_weight_bridge_tickets.id = ?", where.ID)
	} // ID

	if where.ClientID != "" {
		query.Where("ams_weight_bridge_tickets.client_id = ?", where.ClientID)
	} // ClientID

	if !where.OutboundAtStart.IsZero() {
		query.Where("ams_weight_bridge_tickets.outbound_at >= ?", where.OutboundAtStart)
	} // InboundAtStart

	if !where.OutboundAtEnd.IsZero() {
		query.Where("ams_weight_bridge_tickets.outbound_at <= ?", where.OutboundAtEnd)
	} // InboundAtEnd
}

func enrichWeightBridgeTicketQueryWithPreload(query *gorm.DB, preload models.WeightBridgeTicketPreload) {

	if preload.AssetVehicle {
		query.Preload("AssetVehicle")
		query.Preload("AssetVehicle.Asset")
	}

	if preload.InboundWeightBridgeLocation {
		query.Preload("InboundWeightBridgeLocation")
	}

	if preload.InboundLocation {
		query.Preload("InboundLocation")
	}

	if preload.OutboundWeightBridgeLocation {
		query.Preload("OutboundWeightBridgeLocation")
	}

	if preload.OutboundLocation {
		query.Preload("OutboundLocation")
	}

	if preload.LocationRoute {
		query.Preload("LocationRoute")
		query.Preload("LocationRoute.StartLocation")
		query.Preload("LocationRoute.EndLocation")
	}

}

func (r *weightBridgeRepository) GetWeightBridgeTicket(ctx context.Context, dB database.DBI, cond models.WeightBridgeTicketCondition) (*models.WeightBridgeTicket, error) {
	ticket := models.WeightBridgeTicket{}
	query := dB.GetOrm().Model(&ticket)

	enrichWeightBridgeTicketQueryWithWhere(query, cond.Where)

	enrichWeightBridgeTicketQueryWithPreload(query, cond.Preload)

	if len(cond.Columns) > 0 {
		query.Select(cond.Columns)
	}

	err := query.First(&ticket).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errorhandler.ErrDataNotFound("weight bridge ticket")
		}

		return nil, err
	}

	return &ticket, nil
}

func (r *weightBridgeRepository) GetWeightBridgeTicketList(ctx context.Context, dB database.DBI, param models.GetWeightBridgeTicketListParam) (int, []models.WeightBridgeTicket, error) {
	var totalRecords int64
	tickets := []models.WeightBridgeTicket{}
	query := dB.GetTx().Model(&tickets).
		Joins("JOIN ams_assets ON ams_assets.id = ams_weight_bridge_tickets.vehicle_asset_id")

	if param.SearchKeyword != "" {
		query.Where(
			query.Session(&gorm.Session{NewDB: true}).
				Unscoped().
				Where("LOWER(ams_assets.name) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(ams_assets.reference_number) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(ams_assets.serial_number) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(ams_weight_bridge_tickets.reference_id) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(ams_weight_bridge_tickets.vehicle_asset_id) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(ams_weight_bridge_tickets.inbound_operator_user_first_name) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(ams_weight_bridge_tickets.inbound_operator_user_last_name) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(ams_weight_bridge_tickets.outbound_operator_user_first_name) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
				Or("LOWER(ams_weight_bridge_tickets.outbound_operator_user_last_name) LIKE LOWER(?)", "%"+param.SearchKeyword+"%"),
		)
	}

	enrichWeightBridgeTicketQueryWithWhere(query, param.Cond.Where)
	enrichWeightBridgeTicketQueryWithPreload(query, param.Cond.Preload)

	err := query.Count(&totalRecords).Error
	if err != nil {
		return 0, nil, err
	}

	if totalRecords <= 0 {
		return int(totalRecords), nil, nil
	}

	query.Order("updated_at DESC")
	offset := (param.PageNo - 1) * param.PageSize
	err = query.Offset(offset).Limit(param.PageSize).Find(&tickets).Error
	if err != nil {
		return 0, nil, err
	}

	return int(totalRecords), tickets, nil

}

func (r *weightBridgeRepository) CreateWeightBridgeTicket(ctx context.Context, dB database.DBI, weightBridgeTicket *models.WeightBridgeTicket) error {
	return dB.GetTx().Create(weightBridgeTicket).Error
}
func (r *weightBridgeRepository) UpdateWeightBridgeTicket(ctx context.Context, dB database.DBI, weightBridgeTicket *models.WeightBridgeTicket) error {
	return dB.GetTx().Save(weightBridgeTicket).Error
}

func (r *weightBridgeRepository) DeleteWeightBridgeTicket(ctx context.Context, dB database.DBI, ticketId string) error {
	query := dB.GetTx()
	println("---------2")

	return query.Debug().Delete(&models.WeightBridgeTicket{}, "id = ?", ticketId).Error

}
