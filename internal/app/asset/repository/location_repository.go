package repository

import (
	"assetfindr/internal/app/asset/models"
	"assetfindr/internal/infrastructure/database"
	"context"
)

type LocationRepository interface {
	CreateLocation(ctx context.Context, dB database.DBI, loc *models.Location) error
	GetLocationList(ctx context.Context, dB database.DBI, param models.GetLocationListParam) (int, []models.Location, error)
	GetLocation(ctx context.Context, dB database.DBI, cond models.LocationCondition) (*models.Location, error)
	UpdateLocation(ctx context.Context, dB database.DBI, id string, loc *models.Location) error
	GetLocationByIds(ctx context.Context, dB database.DBI, locations *[]models.Location, locationIds []string) error

	GetLocationRouteList(ctx context.Context, dB database.DBI, param models.GetLocationRouteListParam) (int, []models.LocationRoute, error)
}
