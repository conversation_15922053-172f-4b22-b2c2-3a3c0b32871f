package repository

import (
	"assetfindr/internal/app/asset/models"
	"assetfindr/internal/infrastructure/database"
	"context"
)

type WeightBridgeRepository interface {
	GetWeightBridgeTicketList(ctx context.Context, dB database.DBI, param models.GetWeightBridgeTicketListParam) (int, []models.WeightBridgeTicket, error)
	GetWeightBridgeTicket(ctx context.Context, dB database.DBI, cond models.WeightBridgeTicketCondition) (*models.WeightBridgeTicket, error)
	CreateWeightBridgeTicket(ctx context.Context, dB database.DBI, weightBridgeTicket *models.WeightBridgeTicket) error
	UpdateWeightBridgeTicket(ctx context.Context, dB database.DBI, weightBridgeTicket *models.WeightBridgeTicket) error
	DeleteWeightBridgeTicket(ctx context.Context, dB database.DBI, ticketId string) error
}
