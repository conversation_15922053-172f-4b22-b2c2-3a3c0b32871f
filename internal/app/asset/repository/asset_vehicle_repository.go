package repository

import (
	"assetfindr/internal/app/asset/models"
	"assetfindr/internal/infrastructure/database"
	"assetfindr/pkg/common/commonmodel"
	"context"
	"time"
)

type AssetVehicleRepository interface {
	GetAssetVehicles(ctx context.Context, dB database.DBI, assets *[]models.AssetVehicle, pageSize int, pageNo int, searchKeyword string) (int, error)
	GetAssetVehicleList(ctx context.Context, dB database.DBI, param models.GetAssetVehicleListParam) (int, []models.AssetVehicle, error)
	GetAssetVehicleByID(ctx context.Context, dB database.DBI, id string) (*models.AssetVehicle, error)
	CreateAssetVehicle(ctx context.Context, dB database.DBI, assetVehicle *models.AssetVehicle) error
	UpsertAssetVehicle(ctx context.Context, dB database.DBI, assetVehicle *models.AssetVehicle) error
	GetAssetVehicle(ctx context.Context, dB database.DBI, condition models.AssetVehicleCondition) (*models.AssetVehicle, error)
	GetAssetVehiclesV2(ctx context.Context, dB database.DBI, condition models.AssetVehicleCondition) ([]models.AssetVehicle, error)
	UpdateAssetVehicle(ctx context.Context, dB database.DBI, asset *models.AssetVehicle) error

	UpdateAssetVehicleKM(ctx context.Context, dB database.DBI, asset *models.AssetVehicle, vehicleKM float64) error
	UpdateAssetVehicleKMV2(ctx context.Context, dB database.DBI, assetID string, vehicleKM float64) error
	UpdateAssetVehicleHm(ctx context.Context, dB database.DBI, assetID string, vehicleHm int) error
	UpdateAssetVehicleKmHmToZero(ctx context.Context, dB database.DBI, assetID string) error
	UpdateAssetVehicleStateRelated(ctx context.Context, dB database.DBI, assetID string, prevState *models.AssetVehicleMeterState) error
	IsRegistrationNumberExist(ctx context.Context, dB database.DBI, registrationNumber string, clientID string) (bool, error)
	IsEngineNumberExist(ctx context.Context, dB database.DBI, engineNumber string, clientID string) (bool, error)
	GetAssetVehiclesWithExpiryVrdIn30Day(ctx context.Context, dB database.DBI) ([]models.AssetVehicle, error)
	GetAssetVehiclesWithExpiryInspectionBookIn30Day(ctx context.Context, dB database.DBI) ([]models.AssetVehicle, error)

	GetVehicles(ctx context.Context, dB database.DBI, req models.GetVehicleListParam) (int, []models.Vehicle, error)
	CreateVehicle(ctx context.Context, dB database.DBI, vehicle *models.Vehicle) error
	GetVehicle(ctx context.Context, dB database.DBI, condition models.VehicleCondition) (*models.Vehicle, error)
	UpdateVehicle(ctx context.Context, dB database.DBI, vehicle *models.Vehicle) error
	DeleteVehicleByID(ctx context.Context, dB database.DBI, id string) error
	GetVehiclesCSV(ctx context.Context, dB database.DBI, cond models.VehicleCondition) ([]models.Vehicle, error)

	PopulatePeriodicAssetVehicleStatsHistory(ctx context.Context, dB database.DBI) error
	GetAssetVehicleStatsHistory(ctx context.Context, dB database.DBI, datetime time.Time, assetID, clientID string) (*models.AssetVehicleStatsHistory, error)

	ChartVehiclesByInspectionCount(ctx context.Context, dB database.DBI, req models.InspectionChartReq) ([]commonmodel.Chart, error)
}
