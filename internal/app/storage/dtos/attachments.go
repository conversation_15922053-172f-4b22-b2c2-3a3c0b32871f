package dtos

import (
	"assetfindr/pkg/common/commonmodel"
	"fmt"
	"time"
)

type CreateAttachmentReq struct {
	Label             string `json:"label"`
	ReferenceCode     string `json:"reference_code"`
	SourceReferenceID string `json:"source_reference_id"`
	TargetReferenceID string `json:"target_reference_id"`
	Path              string `json:"path"`
	FileType          string `json:"file_type"`
	ClientID          string `json:"client_id"`
}

type UpdatePhotosAssetParam struct {
	AssetID  string
	ClientID string
}

type UpsertAttachmentReq struct {
	ReferenceCode     string
	SourceReferenceID string
	TargetReferenceID string
	ClientID          string
	AttachmentSources []AttachmentSource
	Photos            []commonmodel.PhotoReq
}

type AttachmentSource struct {
	ReferenceCode     string
	SourceReferenceID string
	TargetReferenceID string
}

type GetAttachmentsReq struct {
	ReferenceCode      string   `form:"reference_code"`
	SourceReferenceID  string   `form:"source_reference_id"`
	SourceReferenceIDs []string `form:"source_reference_ids"`
	TargetReferenceID  string   `form:"target_reference_id"`
}

func (r *GetAttachmentsReq) Validate() error {
	if r.ReferenceCode == "" {
		return fmt.Errorf("reference_code must be provided in query url")
	}

	if len(r.SourceReferenceIDs) == 0 && r.SourceReferenceID == "" && r.TargetReferenceID == "" {
		return fmt.Errorf("source_reference_id or target_reference_id must be provided in query url")
	}

	return nil
}

type GetAttachmentsResp struct {
	Id                string    `json:"id"`
	Number            string    `json:"number"`
	CreatedAt         time.Time `json:"created_at"`
	Label             string    `json:"label"`
	ReferenceCode     string    `json:"reference_code"`
	SourceReferenceID string    `json:"source_reference_id"`
	TargetReferenceID string    `json:"target_reference_id"`
	Path              string    `json:"path"`
	FileType          string    `json:"file_type"`
}

type UpdateAttachmentReq struct {
	Label string `json:"label"`
}

type GetPublicPhotoReq struct {
	Path string `form:"path"`
}
