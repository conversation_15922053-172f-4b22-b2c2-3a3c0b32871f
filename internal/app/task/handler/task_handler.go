package handler

import (
	"assetfindr/internal/app/task/dtos"
	"assetfindr/internal/errorhandler"
	"net/http"

	"github.com/gin-gonic/gin"
)

func (h *TicketHandler) GetTasks(c *gin.Context) {
	ctx := c.Request.Context()
	req := dtos.TaskListReq{}
	err := c.Bind<PERSON>y(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	req.Normalize()
	resp, err := h.TicketUseCase.GetTasks(ctx, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *TicketHandler) GetTask(c *gin.Context) {
	ctx := c.Request.Context()
	id := c.Param("id")
	resp, err := h.TicketUseCase.GetTask(ctx, id)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)

}

func (h *TicketHandler) CreateTask(c *gin.Context) {
	ctx := c.Request.Context()
	var req dtos.CreateUpdateTask
	err := c.ShouldBindJSON(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.TicketUseCase.CreateTask(ctx, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *TicketHandler) UpdateTask(c *gin.Context) {
	ctx := c.Request.Context()
	id := c.Param("id")
	var req dtos.CreateUpdateTask
	err := c.ShouldBindJSON(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.TicketUseCase.UpdateTask(ctx, id, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *TicketHandler) DeleteTask(c *gin.Context) {
	ctx := c.Request.Context()
	id := c.Param("id")

	resp, err := h.TicketUseCase.DeleteteTask(ctx, id)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *TicketHandler) ScheduleTask(c *gin.Context) {
	ctx := c.Request.Context()
	id := c.Param("id")
	var req dtos.ScheduleTask
	err := c.ShouldBindJSON(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.TicketUseCase.ScheduleTask(ctx, id, req)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *TicketHandler) CompleteTask(c *gin.Context) {
	ctx := c.Request.Context()
	id := c.Param("id")

	resp, err := h.TicketUseCase.CompleteTask(ctx, id)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *TicketHandler) JobRemindTaskSchedule(c *gin.Context) {
	ctx := c.Request.Context()
	var req struct {
		Type string `json:"type"`
	}
	err := c.BindJSON(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	resp, err := h.TicketUseCase.JobRemindTaskSchedule(ctx, req.Type)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}
