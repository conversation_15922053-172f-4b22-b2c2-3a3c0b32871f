package models

import (
	"assetfindr/pkg/common/commonmodel"
	"time"

	"gopkg.in/guregu/null.v4"
	"gorm.io/gorm"
)

type Task struct {
	commonmodel.ModelV2
	TaskNumber       string      `json:"task_number" gorm:"type:varchar(31);default:null"`
	Subject          string      `json:"subject" gorm:"type:varchar(255)"`
	Description      null.String `json:"description" gorm:"type:varchar(255)"`
	StatusCode       string      `json:"status_code" gorm:"type:varchar(40)"`
	ScheduleDatetime null.Time   `json:"schedule_datetime" gorm:"type:timestamptz"`
	AssetID          string      `json:"asset_id" gorm:"type:varchar(40)"`
	TaskStatus       TaskStatus  `json:"task_status" gorm:"foreignKey:StatusCode"`
}

func (t *Task) TableName() string {
	return "tks_tasks"
}

func (l *Task) BeforeCreate(db *gorm.DB) error {
	if l.ID == "" {
		l.SetUUID("tsk")
	}
	l.ModelV2.BeforeCreate(db)
	return nil
}

func (l *Task) BeforeUpdate(db *gorm.DB) error {
	l.ModelV2.BeforeUpdate(db)
	return nil
}

type TaskStatus struct {
	Code        string `gorm:"type:varchar(20);primaryKey"`
	Label       string `gorm:"type:varchar(20);not null"`
	Description string `gorm:"type:varchar(255)"`
}

func (t *TaskStatus) TableName() string {
	return "tks_TASKS_STATUSES"
}

type TaskWhere struct {
	ID            string
	IDs           []string
	ClientID      string
	CreatedBy     string
	AssetID       string
	AssetIDs      []string
	StatusCode    string
	StatusCodes   []string
	StartDatetime time.Time
	EndDatetime   time.Time
	DateScheduled time.Time
}

type TaskCondition struct {
	Where   TaskWhere
	Columns []string
	Preload TaskPreload
}

type TaskPreload struct {
	TaskStatus bool
}

type GetTaskListParam struct {
	commonmodel.ListRequest
	Cond TaskCondition
}
