package persistence

import (
	"assetfindr/internal/app/task/models"
	"assetfindr/internal/errorhandler"
	"assetfindr/internal/infrastructure/database"
	"context"

	"gorm.io/gorm"
)

func (r *TicketRepository) GetTask(ctx context.Context, dB database.DBI, condition models.TaskCondition) (*models.Task, error) {
	task := models.Task{}
	query := dB.GetTx().Model(&task)
	enrichTaskQueryWithWhere(query, condition.Where)

	err := query.First(&task).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errorhandler.ErrDataNotFound("task")
		}
		return nil, err
	}

	return &task, nil
}

func (r *TicketRepository) GetTasks(ctx context.Context, dB database.DBI, condition models.TaskCondition) ([]models.Task, error) {
	tasks := []models.Task{}
	query := dB.GetTx().Model(&tasks)
	enrichTaskQueryWithWhere(query, condition.Where)

	err := query.Find(&tasks).Error
	if err != nil {
		return nil, err
	}

	return tasks, nil
}

func (r *TicketRepository) GetTaskList(ctx context.Context, dB database.DBI, req models.GetTaskListParam) (int, []models.Task, error) {
	var totalRecords int64
	tasks := []models.Task{}
	query := dB.GetOrm().Model(&tasks)

	enrichTaskQueryWithWhere(query, req.Cond.Where)

	if req.SearchKeyword != "" {
		query.Where(
			query.Session(&gorm.Session{NewDB: true}).
				Unscoped().
				Where("LOWER(subject) LIKE LOWER(?)", "%"+req.SearchKeyword+"%").
				Or("LOWER(description) LIKE LOWER(?)", "%"+req.SearchKeyword+"%").
				Or("LOWER(task_number) LIKE LOWER(?)", "%"+req.SearchKeyword+"%"),
		)
	}

	err := query.Count(&totalRecords).Error
	if err != nil {
		return 0, nil, err
	}

	if totalRecords <= 0 {
		return int(totalRecords), tasks, nil
	}

	offset := (req.PageNo - 1) * req.PageSize
	query.Order("updated_at DESC")
	err = query.Offset(offset).Limit(req.PageSize).Find(&tasks).Error
	if err != nil {
		return 0, nil, err
	}

	return int(totalRecords), tasks, nil
}

func (r *TicketRepository) CreateTask(ctx context.Context, dB database.DBI, task *models.Task) error {
	return dB.GetTx().Create(task).Error
}

func (r *TicketRepository) UpdateTask(ctx context.Context, dB database.DBI, id string, task *models.Task) error {
	return dB.GetTx().Where("id = ?", id).Updates(task).Error
}

func (r *TicketRepository) DeleteTask(ctx context.Context, dB database.DBI, id string) error {
	return dB.GetTx().Delete(&models.Task{}, "id = ?", id).Error
}

func enrichTaskQueryWithWhere(query *gorm.DB, where models.TaskWhere) {
	if where.ID != "" {
		query.Where("id = ?", where.ID)
	} // ID

	if where.ClientID != "" {
		query.Where("client_id = ?", where.ClientID)
	} // ClientID

	if where.CreatedBy != "" {
		query.Where("created_by = ?", where.CreatedBy)
	} // CreatedBy

	if where.AssetID != "" {
		query.Where("asset_id = ?", where.AssetID)
	} // AssetID

	if len(where.AssetIDs) > 0 {
		query.Where("asset_id IN ?", where.AssetIDs)
	} // AssetIDs

	if where.StatusCode != "" {
		query.Where("status_code = ?", where.StatusCode)
	} // StatusCode

	if len(where.StatusCodes) > 0 {
		query.Where("status_code IN ?", where.StatusCodes)
	} // StatusCodes

	if !where.StartDatetime.IsZero() {
		query.Where("schedule_datetime >= ?", where.StartDatetime)
	} // StartDatetime

	if !where.EndDatetime.IsZero() {
		query.Where("schedule_datetime < ?", where.EndDatetime)
	} // EndDatetime

	if !where.DateScheduled.IsZero() {
		query.Where("timezone('-7', schedule_datetime)::date = timezone('-7', ?)::date", where.DateScheduled)
	} // DateScheduled
}
