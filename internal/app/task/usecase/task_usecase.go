package usecase

import (
	assetConstants "assetfindr/internal/app/asset/constants"
	assetModels "assetfindr/internal/app/asset/models"
	notifConstants "assetfindr/internal/app/notification/constants"
	notificationDtos "assetfindr/internal/app/notification/dtos"
	"assetfindr/internal/app/task/constants"
	"assetfindr/internal/app/task/dtos"
	"assetfindr/internal/app/task/models"
	userIdentityModel "assetfindr/internal/app/user-identity/models"
	"assetfindr/pkg/common/commonmodel"
	"assetfindr/pkg/common/helpers"
	"assetfindr/pkg/common/helpers/authhelpers"
	"assetfindr/pkg/common/helpers/timehelpers"
	"assetfindr/pkg/common/helpers/tmplhelpers"
	"context"
	"html/template"
	"time"

	"gopkg.in/guregu/null.v4"
)

func (uc *TicketUseCase) GetTasks(ctx context.Context, req dtos.TaskListReq) (*commonmodel.ListResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	count, tasks, err := uc.TicketRepository.GetTaskList(ctx, uc.DB.DB(), models.GetTaskListParam{
		ListRequest: req.ListRequest,
		Cond: models.TaskCondition{
			Where: models.TaskWhere{
				ClientID:      claim.GetLoggedInClientID(),
				CreatedBy:     claim.UserID,
				StatusCodes:   req.StatusCodes,
				AssetIDs:      req.AssetIDs,
				StartDatetime: req.StartDate,
				EndDatetime:   req.EndDate,
			},
		},
	})
	if err != nil {
		return nil, err
	}

	assetIDs := make([]string, 0, len(tasks))
	for _, task := range tasks {
		assetIDs = append(assetIDs, task.AssetID)
	}
	assetMap := make(map[string]assetModels.Asset)
	if len(assetIDs) > 0 {
		assets, err := uc.AssetRepository.GetAssets(ctx, uc.DB.DB(), assetModels.AssetCondition{
			Where: assetModels.AssetWhere{
				IDs: assetIDs,
			},
		})
		if err != nil {
			return nil, err
		}
		for _, asset := range assets {
			assetMap[asset.ID] = asset
		}
	}

	respData := make([]dtos.TaskListItem, 0, len(tasks))
	for _, task := range tasks {
		asset, ok := assetMap[task.AssetID]
		if ok {
			respData = append(respData, dtos.BuildTaskListRespItem(task, &asset))
		} else {
			respData = append(respData, dtos.BuildTaskListRespItem(task, nil))
		}
	}

	return &commonmodel.ListResponse{
		TotalRecords: count,
		PageSize:     req.PageSize,
		PageNo:       req.PageNo,
		Data:         respData,
	}, nil

}

func (uc *TicketUseCase) GetTask(ctx context.Context, id string) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	task, err := uc.TicketRepository.GetTask(ctx, uc.DB.DB(), models.TaskCondition{
		Where: models.TaskWhere{
			ID:        id,
			ClientID:  claim.GetLoggedInClientID(),
			CreatedBy: claim.UserID,
		},
	})
	if err != nil {
		return nil, err
	}

	var asset *assetModels.Asset
	if task.AssetID != "" {
		asset, err = uc.AssetRepository.GetAsset(ctx, uc.DB.DB(), assetModels.AssetCondition{
			Where: assetModels.AssetWhere{
				ID:       task.AssetID,
				ClientID: claim.GetLoggedInClientID(),
			},
			Preload: assetModels.AssetPreload{
				Brand:                     true,
				AssetTyreWithLinkedParent: true,
				CustomAssetCategory:       true,
				CustomAssetSubCategory:    true,
				AssetModel:                true,
			},
		})
		if err != nil {
			return nil, err
		}

		if asset.Photo.String != "" {
			photoUrl, _ := helpers.GenerateCloudStorageSignedURL(asset.Photo.String, time.Duration(24))
			asset.Photo = null.StringFrom(photoUrl)
		}
	}

	respData := dtos.BuildTaskDetailResp(*task, asset)

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: id,
		Data:        respData,
	}, nil

}

func (uc *TicketUseCase) CreateTask(ctx context.Context, req dtos.CreateUpdateTask) (*commonmodel.CreateResponse, error) {
	task := &models.Task{
		Subject:          req.Subject,
		Description:      req.Description,
		StatusCode:       constants.TASK_STATUS_OPEN,
		ScheduleDatetime: req.ScheduleDatetime,
		AssetID:          req.AssetID,
	}
	err := uc.TicketRepository.CreateTask(ctx, uc.DB.WithCtx(ctx).DB(), task)
	if err != nil {
		return nil, err
	}

	return &commonmodel.CreateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: task.ID,
		Data:        nil,
	}, nil
}

func (uc *TicketUseCase) DeleteteTask(ctx context.Context, id string) (*commonmodel.DeleteResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	_, err = uc.TicketRepository.GetTask(ctx, uc.DB.DB(), models.TaskCondition{
		Where: models.TaskWhere{
			ID:        id,
			ClientID:  claim.GetLoggedInClientID(),
			CreatedBy: claim.UserID,
		},
	})
	if err != nil {
		return nil, err
	}

	err = uc.TicketRepository.DeleteTask(ctx, uc.DB.DB(), id)
	if err != nil {
		return nil, err
	}

	return &commonmodel.DeleteResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: id,
	}, nil
}

func (uc *TicketUseCase) UpdateTask(ctx context.Context, id string, req dtos.CreateUpdateTask) (*commonmodel.UpdateResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	tx, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return nil, err
	}

	defer tx.Rollback()

	_, err = uc.TicketRepository.GetTask(ctx, tx.DB(), models.TaskCondition{
		Where: models.TaskWhere{
			ID:        id,
			ClientID:  claim.GetLoggedInClientID(),
			CreatedBy: claim.UserID,
		},
	})
	if err != nil {
		return nil, err
	}

	updateTask := models.Task{
		Subject:          req.Subject,
		Description:      req.Description,
		StatusCode:       req.StatusCode,
		ScheduleDatetime: req.ScheduleDatetime,
		AssetID:          req.AssetID,
	}

	err = uc.TicketRepository.UpdateTask(ctx, tx.DB(), id, &updateTask)
	if err != nil {
		return nil, err
	}

	err = tx.Commit()
	if err != nil {
		return nil, err
	}

	return &commonmodel.UpdateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: id,
		Data:        nil,
	}, nil
}

func (uc *TicketUseCase) ScheduleTask(ctx context.Context, id string, req dtos.ScheduleTask) (*commonmodel.UpdateResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	tx, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return nil, err
	}

	defer tx.Rollback()

	_, err = uc.TicketRepository.GetTask(ctx, tx.DB(), models.TaskCondition{
		Where: models.TaskWhere{
			ID:        id,
			ClientID:  claim.GetLoggedInClientID(),
			CreatedBy: claim.UserID,
		},
	})
	if err != nil {
		return nil, err
	}

	err = uc.TicketRepository.UpdateTask(ctx, tx.DB(), id, &models.Task{
		ScheduleDatetime: req.ScheduleDatetime,
	})
	if err != nil {
		return nil, err
	}

	err = tx.Commit()
	if err != nil {
		return nil, err
	}

	return &commonmodel.UpdateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: id,
		Data:        nil,
	}, nil
}

func (uc *TicketUseCase) CompleteTask(ctx context.Context, id string) (*commonmodel.UpdateResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	tx, err := uc.DB.WithCtx(ctx).BeginTx()
	if err != nil {
		return nil, err
	}

	defer tx.Rollback()

	_, err = uc.TicketRepository.GetTask(ctx, tx.DB(), models.TaskCondition{
		Where: models.TaskWhere{
			ID:        id,
			ClientID:  claim.GetLoggedInClientID(),
			CreatedBy: claim.UserID,
		},
	})
	if err != nil {
		return nil, err
	}

	err = uc.TicketRepository.UpdateTask(ctx, tx.DB(), id, &models.Task{
		StatusCode: constants.TASK_STATUS_COMPLETED,
	})
	if err != nil {
		return nil, err
	}

	err = tx.Commit()
	if err != nil {
		return nil, err
	}

	return &commonmodel.UpdateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: id,
		Data:        nil,
	}, nil
}

func (uc *TicketUseCase) JobRemindTaskSchedule(ctx context.Context, typeCode string) (*commonmodel.CreateResponse, error) {
	// default today
	dateScheduled := time.Now()

	if typeCode == "" {
		typeCode = tmplhelpers.TASK_REMINDER_TODAY
	}

	switch typeCode {
	case tmplhelpers.TASK_REMINDER_TODAY:
		dateScheduled = time.Now()
	case tmplhelpers.TASK_REMINDER_YESTERDAY:
		dateScheduled = time.Now().Add(-1 * 24 * time.Hour)
	case tmplhelpers.TASK_REMINDER_TOMORROW:
		dateScheduled = time.Now().Add(1 * 24 * time.Hour)
	}

	// Get tasks scheduled for the specified date
	tasks, err := uc.TicketRepository.GetTasks(ctx, uc.DB.DB(), models.TaskCondition{
		Where: models.TaskWhere{
			DateScheduled: dateScheduled,
			StatusCode:    constants.TASK_STATUS_OPEN,
		},
	})
	if err != nil {
		return nil, err
	}

	resp := &commonmodel.CreateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        nil,
	}

	if len(tasks) == 0 {
		return resp, nil
	}

	assetIDs := make([]string, 0, len(tasks))
	clientIDs := make([]string, 0, len(tasks))
	for _, task := range tasks {
		if task.AssetID != "" {
			assetIDs = append(assetIDs, task.AssetID)
		}
		clientIDs = append(clientIDs, task.ClientID)
	}

	mapAssets := make(map[string]assetModels.Asset)
	if len(assetIDs) > 0 {
		assets, err := uc.AssetRepository.GetAssets(ctx, uc.DB.DB(), assetModels.AssetCondition{
			Where: assetModels.AssetWhere{
				IDs: assetIDs,
			},
			Preload: assetModels.AssetPreload{
				AssetTyreTyre: true,
				Brand:         true,
			},
		})
		if err != nil {
			return nil, err
		}

		for _, asset := range assets {
			mapAssets[asset.ID] = asset
		}
	}

	mapClientAlias := make(map[string]string)
	if len(clientIDs) > 0 {
		clients, err := uc.UserRepository.GetClients(ctx, uc.DB.DB(), userIdentityModel.ClientCondition{
			Where: userIdentityModel.ClientWhere{
				IDs: clientIDs,
			},
		})
		if err != nil {
			return nil, err
		}

		for _, client := range clients {
			mapClientAlias[client.ID] = client.ClientAlias
		}
	}

	notifItems := make([]notificationDtos.CreateNotificationItem, 0, len(tasks))
	for _, task := range tasks {
		targetURL := uc.notifUseCase.GenerateTargetURL(ctx, mapClientAlias[task.ClientID], notifConstants.DESTINATION_TYPE_TASK, task.ID)
		templ := tmplhelpers.TaskReminder{
			TaskSubject:       task.Subject,
			TaskDescription:   task.Description.String,
			ScheduledDateTime: task.ScheduleDatetime.Time.In(time.Local).Format(timehelpers.RFC1123Notif),
			RelatedAsset:      "",
			RedirectLink:      template.URL(targetURL),
		}

		if task.AssetID != "" {
			asset := mapAssets[task.AssetID]

			if asset.AssetCategoryCode == assetConstants.ASSET_CATEGORY_VEHICLE_CODE {
				if asset.ReferenceNumber != "" {
					templ.RelatedAsset = asset.ReferenceNumber
				}

				if asset.SerialNumber == "" {
					templ.RelatedAsset += " / " + asset.SerialNumber
				}

				if asset.PartnerOwnerName != "" {
					templ.RelatedAsset += " / " + asset.PartnerOwnerName
				}
			} else if asset.AssetCategoryCode == assetConstants.ASSET_CATEGORY_TYRE_CODE {
				templ.RelatedAsset = asset.SerialNumber

				if asset.AssetTyre != nil {
					templ.RelatedAsset += " / " + asset.Brand.BrandName
					templ.RelatedAsset += " & " + asset.AssetTyre.Tyre.GetTyreSize()
				}
			}
		}

		templ.Normalize()

		notifItems = append(notifItems, notificationDtos.CreateNotificationItem{
			UserID:            task.CreatedBy,
			SourceCode:        notifConstants.NOTIFICATION_SOURCE_CODE_TASK,
			SourceReferenceID: task.ID,
			TargetReferenceID: task.AssetID,
			TargetURL:         targetURL,
			MessageHeader:     templ.GenerateEmailSubject(typeCode),
			MessageBody:       templ.GenerateEmailBody(typeCode),
			ClientID:          task.ClientID,
			TypeCode:          notifConstants.NOTIFICATION_TYPE_REMINDER_CODE,
			MessageFirebase: notificationDtos.MessageFirebase{
				Title: templ.GeneratePushNotifSubject(typeCode),
				Body:  templ.GeneratePushNotifBody(typeCode),
			},
			ReferenceCode:  notifConstants.NOTIF_REF_TASK,
			ReferenceValue: task.ID,
		})
	}

	go func(notifItems []notificationDtos.CreateNotificationItem) {
		_ = uc.notifUseCase.CreateNotification(context.Background(),
			notificationDtos.CreateNotificationReq{
				Items:           notifItems,
				SendToEmail:     true,
				SendToPushNotif: true,
			})
	}(notifItems)

	return resp, nil
}
