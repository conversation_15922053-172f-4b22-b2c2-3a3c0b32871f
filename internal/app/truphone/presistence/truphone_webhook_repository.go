package presistence

import (
	"assetfindr/internal/app/truphone/constants"
	"assetfindr/internal/app/truphone/repository"

	"assetfindr/internal/app/truphone/dtos"
	"assetfindr/pkg/common/commonmodel"
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
)

type truphoneWebhookRepository struct{}

func NewTruphoneWebhookRepository() repository.TruphoneWebhookRepository {
	return &truphoneWebhookRepository{}
}

func (r *truphoneWebhookRepository) NotificationDataSession(ctx context.Context, payload dtos.NewDataSessionPayload) error {
	message := commonmodel.SlackMessagePayload{
		Blocks: []commonmodel.Block{
			{
				Type: "section",
				Text: &commonmodel.TextElement{
					Type: "mrkdwn",
					Text: fmt.Sprintf("%s", payload.Type),
				},
			},
			{
				Type: "rich_text",
				Elements: []interface{}{
					commonmodel.RichTextList{
						Type:   "rich_text_list",
						Style:  "bullet",
						Indent: 0,
						Elements: []commonmodel.RichTextSection{
							{
								Type: "rich_text_section",
								Elements: []commonmodel.TextElement{
									{Type: "text", Text: "ICCID: "},
									{Type: "text", Text: payload.Iccid},
								},
							},
							{
								Type: "rich_text_section",
								Elements: []commonmodel.TextElement{
									{Type: "text", Text: "Device ID: "},
									{Type: "text", Text: "Device ID"},
								},
							},
							{
								Type: "rich_text_section",
								Elements: []commonmodel.TextElement{
									{Type: "text", Text: "Asset Name: "},
									{Type: "text", Text: "Asset Name"},
								},
							},
						},
					},
				},
			},
		},
	}

	reqBody, err := json.Marshal(&message)
	if err != nil {
		return err
	}

	err = r.TruphoneSlackWebhookUrl(ctx, constants.TRUPHONE_SLACK_WEBHOOK_URL, reqBody)
	if err != nil {
		return err
	}

	return nil
}

func (r *truphoneWebhookRepository) TruphoneSlackWebhookUrl(ctx context.Context, endpoint string, requestBody []byte) error {
	targetUrl := endpoint
	parsedTargetUrl, err := url.Parse(targetUrl)
	if err != nil {
		return err
	}

	req, err := http.NewRequest(http.MethodPost, parsedTargetUrl.String(), bytes.NewBuffer(requestBody))
	if err != nil {
		return err
	}
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{}
	resp, err := client.Do(req)

	if err != nil {
		return err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("request to slack webhook failed with status code: %d, resp: %s", resp.StatusCode, string(body))
	}

	return nil
}
