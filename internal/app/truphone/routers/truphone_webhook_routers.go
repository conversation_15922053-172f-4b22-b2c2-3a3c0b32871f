package routers

import (
	"assetfindr/internal/app/truphone/handler"

	"github.com/gin-gonic/gin"
)

func RegisterTruphoneWebhookRoutes(route *gin.Engine, truphoneWebhookHandler *handler.TruphoneWebhookHandler) *gin.Engine {

	truphoneWebhookRoute := route.Group("/v1/truphone-webhook")
	{
		truphoneWebhookRoute.POST("/data-session", truphoneWebhookHandler.NotificationDataSession)
	}

	return route
}
