package handler

import (
	"assetfindr/internal/app/truphone/dtos"
	"assetfindr/internal/app/truphone/usecase"
	"assetfindr/internal/errorhandler"
	"net/http"

	"github.com/gin-gonic/gin"
)

type TruphoneWebhookHandler struct {
	truphoneWebhookUseCase   *usecase.TruphoneWebhookUseCase
}

func NewTruphoneWebhookHandler(truphoneWebhookUseCase *usecase.TruphoneWebhookUseCase) *TruphoneWebhookHandler {
	return &TruphoneWebhookHandler{
		truphoneWebhookUseCase: truphoneWebhookUseCase,
	}
}

func (h *TruphoneWebhookHandler) NotificationDataSession(c *gin.Context) {
	ctx := c.Request.Context()
	var req dtos.NewDataSessionPayload
	err := c.ShouldBindJSON(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.truphoneWebhookUseCase.NotificationDataSession(ctx, req)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusOK, resp)
}