package usecase

import (
	"assetfindr/internal/app/truphone/constants"
	"assetfindr/internal/app/truphone/dtos"
	"assetfindr/internal/app/truphone/repository"
	"assetfindr/pkg/common/commonmodel"
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/url"

	"context"
)

type TruphoneWebhookUseCase struct {
	TruphoneWebhookRepository		repository.TruphoneWebhookRepository
}

func NewTruphoneWebhookUseCase(
	truphoneWebhookRepository repository.TruphoneWebhookRepository,
) *TruphoneWebhookUseCase {
	return &TruphoneWebhookUseCase{
		TruphoneWebhookRepository:		truphoneWebhookRepository,
	}
}

func (uc *TruphoneWebhookUseCase) TruphoneSlackWebhookUrl(ctx context.Context, endpoint string, requestBody commonmodel.SlackMessagePayload) (error) {
	targetUrl := endpoint
	parsedTargetUrl, err := url.Parse(targetUrl)
	if err != nil {
		fmt.Println("Error parsing slack webhook URL: ", err)
		return err
	}

	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		fmt.Println("Error marshalling JSON of slack message request body: ", err)
		return err
	}

	req, err := http.NewRequest("POST", parsedTargetUrl.String(), bytes.NewBuffer(jsonData))
	if err != nil {
		fmt.Println("Error creating slack webhook request: ", err)
		return err
	}
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		fmt.Println("Error fetching slack webhook: ", err)
		return err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Println("Error reading truphone api response: ", err)
		return err
	}

	if resp.StatusCode != http.StatusOK {
		errMsg := fmt.Sprintf("Request to slack webhook failed with status code: %d, resp: %s\n", resp.StatusCode, string(body))
		return errors.New(errMsg)
	}

	return nil
}

func (uc *TruphoneWebhookUseCase) NotificationDataSession(ctx context.Context, payload dtos.NewDataSessionPayload) (*commonmodel.CreateResponse, error) {

	message := commonmodel.SlackMessagePayload{
		Blocks: []commonmodel.Block{
			{
				Type: "section",
				Text: &commonmodel.TextElement{
					Type: "mrkdwn",
					Text: fmt.Sprintf("%s", payload.Type),
				},
			},
			{
				Type: "rich_text",
				Elements: []interface{}{
					commonmodel. RichTextList{
						Type:   "rich_text_list",
						Style:  "bullet",
						Indent: 0,
						Elements: []commonmodel.RichTextSection{
							{
								Type: "rich_text_section",
								Elements: []commonmodel.TextElement{
									{Type: "text", Text: "ICCID: "},
									{Type: "text", Text: payload.Iccid},
								},
							},
							{
								Type: "rich_text_section",
								Elements: []commonmodel.TextElement{
									{Type: "text", Text: "Device ID: "},
									{Type: "text", Text: "Device ID"},
								},
							},
							{
								Type: "rich_text_section",
								Elements: []commonmodel.TextElement{
									{Type: "text", Text: "Asset Name: "},
									{Type: "text", Text: "Asset Name"},
								},
							},
						},
					},
				},
			},
		},
	}

	err := uc.TruphoneSlackWebhookUrl(ctx, constants.TRUPHONE_SLACK_WEBHOOK_URL, message)
	if err != nil {
		fmt.Println("Failed while sending notification data session to slack")
		return nil, err
	}

	return &commonmodel.CreateResponse{
		Success:     true,
		Message:     "Notification Successfuly Sent to Slack",
		ReferenceID: payload.Iccid,
		Data:        nil,
	}, nil

}