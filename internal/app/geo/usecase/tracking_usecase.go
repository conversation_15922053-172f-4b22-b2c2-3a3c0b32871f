package usecase

import (
	"assetfindr/internal/app/geo/dtos"
	"assetfindr/internal/app/geo/models"
	"assetfindr/internal/app/geo/repository"
	"assetfindr/internal/errorhandler"
	"assetfindr/internal/infrastructure/bq"
	"assetfindr/internal/infrastructure/database"
	"assetfindr/pkg/common/commonlogger"
	"assetfindr/pkg/common/commonmodel"
	"assetfindr/pkg/common/helpers/authhelpers"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"time"

	assetModel "assetfindr/internal/app/asset/models"
	assetRepo "assetfindr/internal/app/asset/repository"
	integrationConstants "assetfindr/internal/app/integration/constants"
	integrationModel "assetfindr/internal/app/integration/models"
	integrationRepo "assetfindr/internal/app/integration/repository"

	notificationUsecase "assetfindr/internal/app/notification/usecase"
	taskRepo "assetfindr/internal/app/task/repository"

	"github.com/jackc/pgtype"
	"github.com/peterstace/simplefeatures/geom"
	"gopkg.in/guregu/null.v4"

	userIdentityRepository "assetfindr/internal/app/user-identity/repository"
)

type TrackingUseCase struct {
	DB                  database.DBUsecase
	BQ                  bq.BQUsecase
	DBTimeScale         database.DBUsecase
	trackingRepo        repository.TrackingRepository
	integrationRepo     integrationRepo.IntegrationRepository
	assetVehicleRepo    assetRepo.AssetVehicleRepository
	assetAssignmentRepo assetRepo.AssetAssignmentRepository
	userRepo            userIdentityRepository.UserRepository
	assetRepo           assetRepo.AssetRepository
	alertRepo           integrationRepo.AlertRepository
	ticketRepo          taskRepo.TicketRepository
	notifUseCase        notificationUsecase.NotificationUseCase
	assetLinkedRepo     assetRepo.AssetLinkedRepository
}

func NewTrackingUseCase(
	DB database.DBUsecase,
	BQ bq.BQUsecase,
	DBTimeScale database.DBUsecase,
	trackingRepo repository.TrackingRepository,
	integrationRepo integrationRepo.IntegrationRepository,
	assetVehicleRepo assetRepo.AssetVehicleRepository,
	assetAssignmentRepo assetRepo.AssetAssignmentRepository,
	userRepo userIdentityRepository.UserRepository,
	assetRepo assetRepo.AssetRepository,
	alertRepo integrationRepo.AlertRepository,
	ticketRepo taskRepo.TicketRepository,
	notifUseCase notificationUsecase.NotificationUseCase,
	assetLinkedRepo assetRepo.AssetLinkedRepository,
) *TrackingUseCase {
	return &TrackingUseCase{
		DB:                  DB,
		BQ:                  BQ,
		DBTimeScale:         DBTimeScale,
		trackingRepo:        trackingRepo,
		integrationRepo:     integrationRepo,
		assetVehicleRepo:    assetVehicleRepo,
		assetAssignmentRepo: assetAssignmentRepo,
		userRepo:            userRepo,
		assetRepo:           assetRepo,
		alertRepo:           alertRepo,
		ticketRepo:          ticketRepo,
		notifUseCase:        notifUseCase,
		assetLinkedRepo:     assetLinkedRepo,
	}
}

func (uc *TrackingUseCase) GetTrackings(ctx context.Context, req dtos.TrackingListReq, assetID string) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}
	var start time.Time
	if req.Start != "" {
		start, err = time.Parse(time.RFC3339, req.Start)
		if err != nil {
			return nil, err
		}
	}
	var end time.Time
	if req.End != "" {
		end, err = time.Parse(time.RFC3339, req.End)
		if err != nil {
			return nil, err
		}
	}
	trackings, err := uc.trackingRepo.GetTrackings(ctx, uc.DBTimeScale.DB(), models.TrackingCondition{
		Where: models.TrackingWhere{
			ID:         req.ID,
			AssetID:    assetID,
			TargetCode: req.TargetCode,
			ClientID:   claim.GetLoggedInClientID(),
			Start:      start,
			End:        end,
		},
		Preload: models.TrackingPreload{
			Target: true,
		},
	})
	if err != nil {
		return nil, err
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: assetID,
		Data:        trackings,
	}, nil
}

func (uc *TrackingUseCase) GetLatestVehicleKMFromCanBus(ctx context.Context, assetID string) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	canBusData, err := uc.trackingRepo.GetLatestVehicleKMFromCanBus(ctx, uc.DBTimeScale.DB(), assetID, claim.GetLoggedInClientID())
	if err != nil {
		return nil, err
	}

	if canBusData.AssetID == "" {
		return &commonmodel.DetailResponse{
			Success:     true,
			Message:     "Success",
			ReferenceID: assetID,
			Data:        nil,
		}, nil
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: assetID,
		Data: dtos.GetLatestKMFromTracking{
			VehicleKM: int(canBusData.CanVehicleMileage.Float64),
		},
	}, nil
}

func (uc *TrackingUseCase) GetLatestVehicleKM(ctx context.Context, assetID string) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	tracking, err := uc.trackingRepo.GetLatestVehicleKM(ctx, uc.DBTimeScale.DB(), assetID, claim.GetLoggedInClientID())
	if err != nil {
		return nil, err
	}
	if tracking.ID == "" {
		return &commonmodel.DetailResponse{
			Success:     true,
			Message:     "Success",
			ReferenceID: assetID,
			Data:        nil,
		}, nil
	}

	preTracking, err := uc.trackingRepo.GetPreGPSVehicleMeter(ctx, uc.DBTimeScale.DB(), models.PreGPSVehicleMeterCondition{
		Where: models.PreGPSVehicleMeterWhere{
			IntegrationID: tracking.IntegrationID,
		},
	})
	if err != nil {
		return &commonmodel.DetailResponse{
			Success:     true,
			Message:     "Success",
			ReferenceID: assetID,
			Data:        nil,
		}, nil
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: assetID,
		Data: dtos.GetLatestKMFromTracking{
			VehicleKM: preTracking.FirstVehicleKM + (int(tracking.KM.Int64) - preTracking.FirstGPSKM),
		},
	}, nil
}

func (uc *TrackingUseCase) constructUpdateDeleteGeoFenceReference(req dtos.CreateGeoFence) ([]models.GeoFenceReference, []string, []string, error) {
	// create := []models.GeoFenceReference{}
	update := []models.GeoFenceReference{}
	deleteIDs := []string{}
	assetIDs := []string{}
	for _, val := range req.References {
		if val.IsDeleted {
			deleteIDs = append(deleteIDs, val.ID)
			continue
		}
		geoFenceReference := models.GeoFenceReference{}
		geoFenceReference.AssetID = val.AssetID
		if val.ID != "" {
			geoFenceReference.ID = val.ID
			update = append(update, geoFenceReference)
			assetIDs = append(assetIDs, val.AssetID)
			continue
		}
	}
	return update, deleteIDs, assetIDs, nil
}

func (uc *TrackingUseCase) constructCreateGeoFenceReference(req dtos.CreateGeoFence, geofenceID string) ([]models.GeoFenceReference, error) {
	create := []models.GeoFenceReference{}
	for _, val := range req.References {
		geoFenceReference := models.GeoFenceReference{}
		geoFenceReference.AssetID = val.AssetID
		geoFenceReference.GeoFenceID = geofenceID
		if !val.IsDeleted && val.ID == "" {
			create = append(create, geoFenceReference)
			continue
		}
	}
	return create, nil
}

func (uc *TrackingUseCase) GetLatestTrackingPosition(ctx context.Context, assetID string) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	tracking, err := uc.trackingRepo.GetLatestTrackingPosition(ctx, uc.DBTimeScale.DB(), assetID, claim.GetLoggedInClientID())
	if err != nil {
		return nil, err
	}
	if tracking.ID == "" {
		return &commonmodel.DetailResponse{
			Success:     true,
			Message:     "Success",
			ReferenceID: assetID,
			Data:        nil,
		}, nil
	}

	vehicleKM := null.Int{}

	if tracking.KM.Valid {
		firstVehicleKM, firstGPSKM := 0, 0
		preTracking, err := uc.trackingRepo.GetPreGPSVehicleMeter(ctx, uc.DBTimeScale.DB(), models.PreGPSVehicleMeterCondition{
			Where: models.PreGPSVehicleMeterWhere{
				IntegrationID: tracking.IntegrationID,
			},
		})
		if err == nil {
			firstVehicleKM, firstGPSKM = preTracking.FirstVehicleKM, preTracking.FirstGPSKM
		}

		vehicleKM = null.IntFrom(int64(firstVehicleKM + (int(tracking.KM.Int64) - firstGPSKM)))
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: assetID,
		Data: dtos.GetLatestTrackingPosition{
			VehicleKM:    vehicleKM,
			LatestUpdate: tracking.Time,
			Speed:        tracking.Speed,
			Lat:          tracking.Lat,
			Long:         tracking.Long,
		},
	}, nil
}

func (uc *TrackingUseCase) GetLatestTrackingPositionList(ctx context.Context, assetID string, req dtos.LatestListReq) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	trackings, err := uc.trackingRepo.GetLatestTrackingPositionList(ctx, uc.DBTimeScale.DB(), assetID, claim.GetLoggedInClientID(), req.Limit)
	if err != nil {
		return nil, err
	}

	if len(trackings) == 0 {
		return &commonmodel.DetailResponse{
			Success:     true,
			Message:     "Success",
			ReferenceID: assetID,
			Data:        nil,
		}, nil
	}

	firstVehicleKM, firstGPSKM := 0, 0
	preTracking, err := uc.trackingRepo.GetPreGPSVehicleMeter(ctx, uc.DBTimeScale.DB(), models.PreGPSVehicleMeterCondition{
		Where: models.PreGPSVehicleMeterWhere{
			IntegrationID: trackings[0].IntegrationID,
		},
	})
	if err == nil {
		firstVehicleKM, firstGPSKM = preTracking.FirstVehicleKM, preTracking.FirstGPSKM
	}

	resp := make([]dtos.GetLatestTrackingPosition, 0, req.Limit)
	for _, tracking := range trackings {
		vehicleKM := null.Int{}

		if tracking.KM.Valid {
			vehicleKM = null.IntFrom(int64(firstVehicleKM + (int(tracking.KM.Int64) - firstGPSKM)))
		}

		resp = append(resp, dtos.GetLatestTrackingPosition{
			VehicleKM:    vehicleKM,
			LatestUpdate: tracking.Time,
			Time:         tracking.Time,
			Speed:        tracking.Speed,
			Lat:          tracking.Lat,
			Long:         tracking.Long,
		})
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: assetID,
		Data:        resp,
	}, nil
}

func (uc *TrackingUseCase) GetLatestCanBusData(ctx context.Context, assetID string) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	canBusData, err := uc.trackingRepo.GetLatestCanBusData(ctx, uc.DBTimeScale.DB(), assetID, claim.GetLoggedInClientID())
	if err != nil {
		return nil, err
	}
	if canBusData.AssetID == "" {
		return &commonmodel.DetailResponse{
			Success:     true,
			Message:     "Success",
			ReferenceID: assetID,
			Data:        nil,
		}, nil
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: assetID,
		Data:        canBusData,
	}, nil
}

func (uc *TrackingUseCase) GetLatestGeneralData(ctx context.Context, assetID string) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	canBusData, err := uc.trackingRepo.GetLatestGeneralData(ctx, uc.DBTimeScale.DB(), assetID, claim.GetLoggedInClientID())
	if err != nil {
		return nil, err
	}
	if canBusData.AssetID == "" {
		return &commonmodel.DetailResponse{
			Success:     true,
			Message:     "Success",
			ReferenceID: assetID,
			Data:        nil,
		}, nil
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: assetID,
		Data:        canBusData,
	}, nil
}
func (uc *TrackingUseCase) GetLatestCompressorData(ctx context.Context, assetID string) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	compressorData, err := uc.trackingRepo.GetLatestCompressorData(ctx, uc.DBTimeScale.DB(), assetID, claim.GetLoggedInClientID())
	if err != nil {
		return nil, err
	}
	if compressorData.AssetID == "" {
		return &commonmodel.DetailResponse{
			Success:     true,
			Message:     "Success",
			ReferenceID: assetID,
			Data:        nil,
		}, nil
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: assetID,
		Data:        compressorData,
	}, nil
}

func (uc *TrackingUseCase) GetLatestTyreSensorData(ctx context.Context, assetID string, req dtos.GetLatestTrackingReq) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	tyreData, err := uc.trackingRepo.GetLatestTyreSensorData(ctx, uc.DBTimeScale.DB(), models.GetLatestTrackingParam{
		AssetID:       assetID,
		ClientID:      claim.GetLoggedInClientID(),
		InLastMinuets: req.InLastMinuets,
	})
	if err != nil {
		return nil, err
	}
	if tyreData.AssetID == "" {
		return &commonmodel.DetailResponse{
			Success:     true,
			Message:     "Success",
			ReferenceID: assetID,
			Data:        nil,
		}, nil
	}

	if !tyreData.IdentMacAddress.Valid || tyreData.IdentMacAddress.String == "" {
		tyreData.IdentMacAddress = null.NewString(tyreData.Ident, true)
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: assetID,
		Data:        tyreData,
	}, nil
}

func (uc *TrackingUseCase) GetLatestTyreChangerData(ctx context.Context, assetID string, req dtos.GetLatestTrackingReq) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	tyreChangerData, err := uc.trackingRepo.GetLatestTyreChangerData(ctx, uc.DBTimeScale.DB(), models.GetLatestTrackingParam{
		AssetID:       assetID,
		ClientID:      claim.GetLoggedInClientID(),
		InLastMinuets: req.InLastMinuets,
	})
	if err != nil {
		return nil, err
	}
	if tyreChangerData.AssetID == "" {
		return &commonmodel.DetailResponse{
			Success:     true,
			Message:     "Success",
			ReferenceID: assetID,
			Data:        nil,
		}, nil
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: assetID,
		Data:        tyreChangerData,
	}, nil
}

func (uc *TrackingUseCase) GetLatestTyreChangerStateData(ctx context.Context, assetID string, req dtos.GetLatestTrackingReq) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	tyreChangerStateData, err := uc.trackingRepo.GetLatestTyreChangerStateData(ctx, uc.DBTimeScale.DB(), models.GetLatestTrackingParam{
		AssetID:       assetID,
		ClientID:      claim.GetLoggedInClientID(),
		InLastMinuets: req.InLastMinuets,
	})
	if err != nil {
		return nil, err
	}
	if tyreChangerStateData.AssetID == "" {
		return &commonmodel.DetailResponse{
			Success:     true,
			Message:     "Success",
			ReferenceID: assetID,
			Data:        nil,
		}, nil
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: assetID,
		Data:        tyreChangerStateData,
	}, nil
}

func (uc *TrackingUseCase) GetLatestHMDigitalInputData(ctx context.Context, assetID string, req dtos.GetLatestTrackingReqV2) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	generalDigitalInputHM, err := uc.trackingRepo.GetLatestHMDigitalInputData(ctx, uc.DBTimeScale.DB(), models.GetLatestTrackingParam{
		AssetID:  assetID,
		MaxTime:  req.MaxTime,
		ClientID: claim.GetLoggedInClientID(),
	})
	if err != nil {
		return nil, err
	}
	if generalDigitalInputHM.AssetID == "" {
		return &commonmodel.DetailResponse{
			Success:     true,
			Message:     "Success",
			ReferenceID: assetID,
			Data:        nil,
		}, nil
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: assetID,
		Data:        generalDigitalInputHM,
	}, nil
}

func (uc *TrackingUseCase) GetLatestCanBusDataList(ctx context.Context, assetID string, req dtos.LatestListReq) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	canBusDatas, err := uc.trackingRepo.GetLatestCanBusDataList(ctx, uc.DBTimeScale.DB(), assetID, claim.GetLoggedInClientID(), req.Limit)
	if err != nil {
		return nil, err
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: assetID,
		Data:        canBusDatas,
	}, nil
}

func (uc *TrackingUseCase) GetLatestGeneralDataList(ctx context.Context, assetID string, req dtos.LatestListReq) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	generalDatas, err := uc.trackingRepo.GetLatestGeneralDataList(ctx, uc.DBTimeScale.DB(), assetID, claim.GetLoggedInClientID(), req.Limit)
	if err != nil {
		return nil, err
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: assetID,
		Data:        generalDatas,
	}, nil
}

func (uc *TrackingUseCase) GetLatestCompressorDataList(ctx context.Context, assetID string, req dtos.LatestListReq) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	compressorDatas, err := uc.trackingRepo.GetLatestCompressorDataList(ctx, uc.DBTimeScale.DB(), assetID, claim.GetLoggedInClientID(), req.Limit)
	if err != nil {
		return nil, err
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: assetID,
		Data:        compressorDatas,
	}, nil
}

func (uc *TrackingUseCase) GetLatestTyreSensorDataList(ctx context.Context, assetID string, req dtos.LatestListReq) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	compressorDatas, err := uc.trackingRepo.GetLatestTyreSensorDataList(ctx, uc.DBTimeScale.DB(), assetID, claim.GetLoggedInClientID(), req.Limit)
	if err != nil {
		return nil, err
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: assetID,
		Data:        compressorDatas,
	}, nil
}

func (uc *TrackingUseCase) GetLatestTyreChangerDataList(ctx context.Context, assetID string, req dtos.LatestListReq) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	tyreChangers, err := uc.trackingRepo.GetLatestTyreChangerDataList(ctx, uc.DBTimeScale.DB(), assetID, claim.GetLoggedInClientID(), req.Limit)
	if err != nil {
		return nil, err
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: assetID,
		Data:        tyreChangers,
	}, nil
}

func (uc *TrackingUseCase) GetLatestTyreChangerStateDataList(ctx context.Context, assetID string, req dtos.LatestListReq) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	tyreChangerStates, err := uc.trackingRepo.GetLatestTyreChangerStateDataList(ctx, uc.DBTimeScale.DB(), assetID, claim.GetLoggedInClientID(), req.Limit)
	if err != nil {
		return nil, err
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: assetID,
		Data:        tyreChangerStates,
	}, nil
}

func (uc *TrackingUseCase) CreateGeoFence(ctx context.Context, req dtos.CreateGeoFence) (*commonmodel.CreateResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}
	update, deleteIDs, _, err := uc.constructUpdateDeleteGeoFenceReference(req)
	if err != nil {
		return nil, err
	}
	tX, err := uc.DBTimeScale.WithCtx(ctx).BeginTx()
	if err != nil {
		return nil, err
	}
	defer tX.Rollback()

	if len(update) > 0 {
		for _, val := range update {
			err = uc.trackingRepo.UpdateGeoFenceReference(ctx, tX.DB(), val.ID, &val)
			if err != nil {
				return nil, err
			}
		}
	}
	if len(deleteIDs) > 0 {
		err = uc.trackingRepo.DeleteGeoFenceReferences(ctx, tX.DB(), deleteIDs)
		if err != nil {
			return nil, err
		}
	}
	var existingGeoFence *models.GeoFence
	if req.ID != "" {
		existingGeoFence, err = uc.trackingRepo.GetGeoFenceByID(ctx, tX.DB(), req.ID, claim.GetLoggedInClientID())
		if err != nil && !errorhandler.IsErrNotFound(err) {
			return nil, err
		}
	}

	seq := []float64{}
	for _, point := range req.Coordinates {
		seq = append(seq,
			point.Long,
			point.Lat,
		)
	}
	polygon := geom.NewPolygon([]geom.LineString{geom.NewLineString(geom.NewSequence(seq, geom.DimXY))})

	geoFence := &models.GeoFence{
		Name:                          req.Name,
		TypeCode:                      req.TypeCode,
		Description:                   req.Description,
		Color:                         req.Color,
		Height:                        req.Height,
		Perimeter:                     req.Perimeter,
		Area:                          req.Area,
		Coordinates:                   polygon.AsText(),
		AlertRecipientUserIDs:         req.AlertRecipientUserIDs,
		AlertMediaTargets:             req.AlertMediaTargets,
		NotifyAlertToAllAssetAssignee: req.NotifyAlertToAllAssetAssignee,
	}

	if existingGeoFence != nil && req.ID != "" {
		err = uc.trackingRepo.UpdateGeoFence(ctx, uc.DBTimeScale.DB(), existingGeoFence.ID, geoFence)
		if err != nil {
			return nil, err
		}
		create, err := uc.constructCreateGeoFenceReference(req, existingGeoFence.ID)
		if err != nil {
			return nil, err
		}
		if len(create) > 0 {
			err = uc.trackingRepo.CreateGeoFenceReferences(ctx, tX.DB(), create)
			if err != nil {
				return nil, err
			}
		}
		err = tX.Commit()
		if err != nil {
			return nil, err
		}
		return &commonmodel.CreateResponse{
			Success:     true,
			Message:     "Success",
			ReferenceID: existingGeoFence.ID,
			Data:        nil,
		}, nil
	}
	err = uc.trackingRepo.CreateGeoFence(ctx, uc.DBTimeScale.WithCtx(ctx).DB(), geoFence)
	if err != nil {
		return nil, err
	}
	create, err := uc.constructCreateGeoFenceReference(req, geoFence.ID)
	if err != nil {
		return nil, err
	}
	if len(create) > 0 {
		err = uc.trackingRepo.CreateGeoFenceReferences(ctx, tX.DB(), create)
		if err != nil {
			return nil, err
		}
	}
	err = tX.Commit()
	if err != nil {
		return nil, err
	}

	return &commonmodel.CreateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: geoFence.ID,
		Data:        nil,
	}, nil
}

func (uc *TrackingUseCase) DeleteGeoFence(ctx context.Context, id string) (*commonmodel.DeleteResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}
	geofence, err := uc.trackingRepo.GetGeoFence(ctx, uc.DBTimeScale.DB(), models.GeoFenceCondition{
		Where: models.GeoFenceWhere{
			ID:       id,
			ClientID: claim.GetLoggedInClientID(),
		},
	})
	if err != nil && !errorhandler.IsErrNotFound(err) {
		return nil, err
	}
	if geofence == nil {
		return nil, errorhandler.ErrBadRequest("geofence not exist or already deleted")
	}
	//begin transaction
	tX, err := uc.DBTimeScale.WithCtx(ctx).BeginTx()
	if err != nil {
		return nil, err
	}
	defer tX.Rollback()
	err = uc.trackingRepo.DeleteGeoFence(ctx, tX.DB(), id)
	if err != nil {
		return nil, err
	}
	err = uc.trackingRepo.DeleteGeoFenceReferenceByGeoFenceID(ctx, tX.DB(), id)
	if err != nil {
		return nil, err
	}
	err = tX.Commit()
	if err != nil {
		return nil, err
	}
	return &commonmodel.DeleteResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: id,
	}, nil
}

func (uc *TrackingUseCase) GetGeoFence(ctx context.Context, id string) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}
	geofence, err := uc.trackingRepo.GetGeoFence(ctx, uc.DBTimeScale.DB(), models.GeoFenceCondition{
		Where: models.GeoFenceWhere{
			ID:       id,
			ClientID: claim.GetLoggedInClientID(),
		},
		Preload: models.GeoFencePreload{
			Type: true,
		},
	})
	if err != nil {
		return nil, err
	}
	if geofence == nil {
		return nil, errorhandler.ErrBadRequest("geofence not exist")
	}

	geofenceReferences, err := uc.trackingRepo.GetGeoFenceReferences(ctx, uc.DBTimeScale.DB(), models.GeoFenceReferenceCondition{
		Where: models.GeoFenceReferenceWhere{
			GeoFenceID: id,
		},
	})
	if err != nil {
		return nil, err
	}

	assetIDs := []string{}
	for _, val := range geofenceReferences {
		assetIDs = append(assetIDs, val.AssetID)
	}
	assets, err := uc.assetRepo.GetAssets(ctx, uc.DB.DB(), assetModel.AssetCondition{
		Where: assetModel.AssetWhere{
			IDs:      assetIDs,
			ClientID: claim.GetLoggedInClientID(),
		},
	})
	if err != nil {
		return nil, err
	}
	assetMap := map[string]assetModel.Asset{}
	for _, val := range assets {
		assetMap[val.ID] = val
	}

	integration, err := uc.integrationRepo.GetIntegrations(ctx, uc.DB.DB(), integrationModel.IntegrationCondition{
		Where: integrationModel.IntegrationWhere{
			InternalReferenceIDs:      assetIDs,
			IntegrationTargetTypeCode: integrationConstants.INTEGRATION_TARGET_TYPE_GPS_ID_TRACKING,
			ClientID:                  claim.GetLoggedInClientID(),
		},
	})
	mapImei := map[string]string{}
	for _, val := range integration {
		//get imei
		identifier := val.IdentifierJSON
		imei, err := extractIMEI(identifier)
		if err != nil {
			return nil, err
		}
		mapImei[val.InternalReferenceID] = imei
	}

	resp := dtos.GetGeoFence{}
	resp.Set(*geofence, geofenceReferences, assetMap, mapImei)

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: id,
		Data:        resp,
	}, nil
}

func extractIMEI(jsonbData pgtype.JSONB) (string, error) {
	if jsonbData.Status != pgtype.Present {
		return "", fmt.Errorf("JSONB data is not present")
	}

	var data map[string]interface{}
	if err := json.Unmarshal(jsonbData.Bytes, &data); err != nil {
		return "", fmt.Errorf("error unmarshalling JSONB data: %w", err)
	}

	imei, ok := data["imei"].(string)
	if !ok {
		return "", fmt.Errorf("IMEI field is not a string or does not exist")
	}

	return imei, nil
}

func (uc *TrackingUseCase) GetGeoFences(ctx context.Context, req dtos.GeoFenceListReq) (*commonmodel.ListResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}
	count, geofences, err := uc.trackingRepo.GetGeoFenceList(ctx, uc.DBTimeScale.DB(), models.GetGeoFenceListParam{
		ListRequest: req.ListRequest,
		Cond: models.GeoFenceCondition{
			Where: models.GeoFenceWhere{
				ClientID: claim.GetLoggedInClientID(),
			},
			Preload: models.GeoFencePreload{
				Type: true,
			},
		},
	})
	if err != nil {
		return nil, err
	}

	geofencesID := []string{}
	for _, val := range geofences {
		geofencesID = append(geofencesID, val.ID)
	}
	geofenceReferences, err := uc.trackingRepo.GetGeoFenceReferences(ctx, uc.DBTimeScale.DB(), models.GeoFenceReferenceCondition{
		Where: models.GeoFenceReferenceWhere{
			GeoFenceIDs: geofencesID,
		},
	})
	if err != nil {
		return nil, err
	}
	geofenceReferenceMap := map[string][]models.GeoFenceReference{}
	for _, val := range geofenceReferences {
		if _, ok := geofenceReferenceMap[val.GeoFenceID]; !ok {
			geofenceReferenceMap[val.GeoFenceID] = []models.GeoFenceReference{}
		}
		geofenceReferenceMap[val.GeoFenceID] = append(geofenceReferenceMap[val.GeoFenceID], val)
	}
	assetIDs := []string{}
	for _, val := range geofenceReferences {
		assetIDs = append(assetIDs, val.AssetID)
	}
	assets, err := uc.assetRepo.GetAssets(ctx, uc.DB.DB(), assetModel.AssetCondition{
		Where: assetModel.AssetWhere{
			IDs:      assetIDs,
			ClientID: claim.GetLoggedInClientID(),
		},
	})
	if err != nil {
		return nil, err
	}
	assetMap := map[string]assetModel.Asset{}
	for _, val := range assets {
		assetMap[val.ID] = val
	}

	integration, err := uc.integrationRepo.GetIntegrations(ctx, uc.DB.DB(), integrationModel.IntegrationCondition{
		Where: integrationModel.IntegrationWhere{
			InternalReferenceIDs:      assetIDs,
			IntegrationTargetTypeCode: integrationConstants.INTEGRATION_TARGET_TYPE_GPS_ID_TRACKING,
			ClientID:                  claim.GetLoggedInClientID(),
		},
	})
	mapImei := map[string]string{}
	for _, val := range integration {
		//get imei
		identifier := val.IdentifierJSON
		imei, err := extractIMEI(identifier)
		if err != nil {
			return nil, err
		}
		mapImei[val.InternalReferenceID] = imei
	}

	resp := []dtos.GetGeoFence{}
	for _, val := range geofences {
		getGeoFence := dtos.GetGeoFence{}
		getGeoFence.Set(val, geofenceReferenceMap[val.ID], assetMap, mapImei)
		resp = append(resp, getGeoFence)
	}

	return &commonmodel.ListResponse{
		TotalRecords: count,
		PageSize:     req.PageSize,
		PageNo:       req.PageNo,
		Data:         resp,
	}, nil
}

func (uc *TrackingUseCase) SaveSenquipDataV1(ctx context.Context, req dtos.SendquipCompressorReq) (*commonmodel.CreateResponse, error) {
	integration, err := uc.integrationRepo.GetIntegration(context.Background(), uc.DB.DB(), integrationModel.IntegrationCondition{
		Where: integrationModel.IntegrationWhere{
			IntegrationTargetTypeCode: integrationConstants.INTEGRATION_TARGET_TYPE_SENDQUIP_GATEWAY,
			IdentifierJSON: map[string]string{
				"device_id": req.DeviceID,
			},
			Status: integrationConstants.INTEGRATION_STATUS_CODE_ACTIVE,
		},
	})
	if err != nil {
		commonlogger.Warnf("failed to get integration by device id, err:%v, ident:%s", err, req.DeviceID)
		return nil, err
	}

	if !req.IsCompressorDataValid() {
		return &commonmodel.CreateResponse{
			Success:     true,
			Message:     "Success",
			ReferenceID: "",
			Data:        nil,
		}, nil
	}

	var machineStatusByte, _ = base64.StdEncoding.DecodeString(req.Cp1.String)
	var machineStatus = string(machineStatusByte)

	compressorData := &models.CompressorSensorData{
		AssetID:                    integration.InternalReferenceID,
		DeviceID:                   req.DeviceID,
		Time:                       time.Unix(int64(req.Time), 0).In(time.UTC),
		IntegrationID:              integration.ID,
		CreatedAt:                  time.Now().In(time.UTC),
		ClientID:                   integration.ClientID,
		PressAirFeedPressure:       req.Mod1,
		PressAirExhaustTemperature: req.Mod2,
		PressRunTime:               req.Mod3,
		PressLoadTime:              req.Mod4,
		PressPhaseACurrent:         req.Mod5,
		PressPhaseBCurrent:         req.Mod6,
		PressPhaseCCurrent:         req.Mod7,
		PressRunState1:             req.Mod8,
		PressRunState2:             req.Mod9,
		PressOilFilterUsedTime:     req.Mod11,
		PressOilSeparatorUsedTime:  req.Mod12,
		PressAirFilterUsedTime:     req.Mod13,
		PressLubeOilUsedTime:       req.Mod14,
		PressLubeGreaseUsedTime:    req.Mod15,
		PressMachineStatus:         null.StringFrom(machineStatus),
		PressCurrentImbalance:      req.Cp10,
	}
	err = uc.trackingRepo.CreateCompressorSensor(ctx, uc.DBTimeScale.WithCtx(ctx).DB(), compressorData)
	if err != nil {
		return nil, err
	}

	go uc.trackingRepo.CreateCompressorSensorDataLake(ctx, uc.BQ.Conn(), compressorData)

	return &commonmodel.CreateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        nil,
	}, nil
}

func (uc *TrackingUseCase) SaveSenquipData(ctx context.Context, req dtos.SendquipReq) (*commonmodel.CreateResponse, error) {
	integration, err := uc.integrationRepo.GetIntegration(context.Background(), uc.DB.DB(), integrationModel.IntegrationCondition{
		Where: integrationModel.IntegrationWhere{
			IntegrationTargetTypeCode: integrationConstants.INTEGRATION_TARGET_TYPE_SENDQUIP_GATEWAY,
			IdentifierJSON: map[string]string{
				"device_id": req.DeviceID,
			},
			Status: integrationConstants.INTEGRATION_STATUS_CODE_ACTIVE,
		},
	})
	if err != nil {
		commonlogger.Warnf("failed to get integration by device id, err:%v, ident:%s", err, req.DeviceID)
		return nil, err
	}

	compressorData := &models.CompressorSensorData{
		AssetID:  integration.InternalReferenceID,
		DeviceID: req.DeviceID,
		Time:     time.Unix(int64(req.Time), 0).In(time.UTC),
		ClientID: integration.ClientID,
		// PressAirFeedPressure:       req.PressAirFeedPressure,
		// PressAirExhaustTemperature: req.PressAirExhaustTemperature,
		// PressRunTime:               req.PressRunTime,
		// PressLoadTime:              req.PressLoadTime,
		// PressPhaseACurrent:         req.PressPhaseACurrent,
		// PressPhaseBCurrent:         req.PressPhaseBCurrent,
		// PressPhaseCCurrent:         req.PressPhaseCCurrent,
		// PressRunState1:             req.PressRunState1,
		// PressRunState2:             req.PressRunState2,
		// PressOilFilterUsedTime:     req.PressOilFilterUsedTime,
		// PressOilSeparatorUsedTime:  req.PressOilSeparatorUsedTime,
		// PressAirFilterUsedTime:     req.PressAirFilterUsedTime,
		// PressLubeOilUsedTime:       req.PressLubeOilUsedTime,
		// PressLubeGreaseUsedTime:    req.PressLubeGreaseUsedTime,
	}
	err = uc.trackingRepo.CreateCompressorSensor(ctx, uc.DBTimeScale.WithCtx(ctx).DB(), compressorData)
	if err != nil {
		return nil, err
	}

	go uc.trackingRepo.CreateCompressorSensorDataLake(ctx, uc.BQ.Conn(), compressorData)

	return &commonmodel.CreateResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        nil,
	}, nil
}

func (uc *TrackingUseCase) CreateLogRawSensorDatalakeSendquip(data dtos.SendquipCompressorReq, msg string) {
	logRawSensorData := &models.LogRawSensorData{
		Time:       null.TimeFrom(time.Unix(int64(data.Time), 0).In(time.UTC)),
		Ident:      data.DeviceID,
		SourceCode: integrationConstants.INTEGRATION_TARGET_TYPE_SENDQUIP_GATEWAY,
		Data:       msg,
		CreatedAt:  time.Now().In(time.UTC),
	}
	err := uc.trackingRepo.CreateLogRawSensorDataLake(context.Background(), uc.BQ.Conn(), logRawSensorData)
	if err != nil {
		commonlogger.Errorf("failed to create low raw sensor data, err:%v, ident:%s", err, data.DeviceID)
	}
}

func (uc *TrackingUseCase) CreateLogRawSensorDatalake(msg string) {
	logRawSensorData := &models.LogRawSensorData{
		Ident:      "UNKNOWN",
		SourceCode: "UNKNOWN",
		Data:       msg,
		CreatedAt:  time.Now().In(time.UTC),
	}
	err := uc.trackingRepo.CreateLogRawSensorDataLake(context.Background(), uc.BQ.Conn(), logRawSensorData)
	if err != nil {
		commonlogger.Errorf("failed to create low raw sensor data, err:%v, msg:%s", err, msg)
	}
}
