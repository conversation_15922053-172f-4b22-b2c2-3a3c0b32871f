package routers

import (
	"assetfindr/internal/app/geo/handler"
	"assetfindr/pkg/middleware"

	"github.com/gin-gonic/gin"
)

func RegisterTrackingRoutes(route *gin.Engine, trackingHandler *handler.TrackingHandler) *gin.Engine {

	trackingRoutes := route.Group("/v1/trackings/:asset_id", middleware.TokenValidationMiddleware())
	{
		trackingRoutes.GET("", trackingHandler.GetTrackings)
		trackingRoutes.GET("/latest-km", trackingHandler.GetLatestVehicleKM)
		trackingRoutes.GET("/latest-position", trackingHandler.GetLatestTrackingPosition)
		trackingRoutes.GET("/latest-position/list", trackingHandler.GetLatestTrackingPositionList)
		trackingRoutes.GET("/can-bus", trackingHandler.GetLatestCanBusData)
		trackingRoutes.GET("/general", trackingHandler.GetLatestGeneralData)
		trackingRoutes.GET("/compressor", trackingHandler.GetLatestCompressorData)
		trackingRoutes.GET("/tyre-sensor", trackingHandler.GetLatestTyreSensorData)
		trackingRoutes.GET("/tyre-changer", trackingHandler.GetLatestTyreChangerData)
		trackingRoutes.GET("/tyre-changer-state", trackingHandler.GetLatestTyreChangerStateData)
		trackingRoutes.GET("/latest-total-hm-digital-input", trackingHandler.GetLatestHMDigitalInputData)
		trackingRoutes.GET("/can-bus/list", trackingHandler.GetLatestCanBusDataList)
		trackingRoutes.GET("/general/list", trackingHandler.GetLatestGeneralDataList)
		trackingRoutes.GET("/compressor/list", trackingHandler.GetLatestCompressorDataList)
		trackingRoutes.GET("/tyre-sensor/list", trackingHandler.GetLatestTyreSensorDataList)
		trackingRoutes.GET("/tyre-changer/list", trackingHandler.GetLatestTyreChangerDataList)
		trackingRoutes.GET("/tyre-changer-state/list", trackingHandler.GetLatestTyreChangerStateDataList)
	}
	geofenceRoutes := route.Group("/v1/geofences", middleware.TokenValidationMiddleware())
	{
		geofenceRoutes.POST("", trackingHandler.CreateGeoFence)
		geofenceRoutes.GET("/:id", trackingHandler.GetGeoFence)
		geofenceRoutes.GET("", trackingHandler.GetGeoFences)
		geofenceRoutes.GET("/in-out-histories", trackingHandler.GetGeoFenceInOutHistories)
		geofenceRoutes.DELETE("/:id", trackingHandler.DeleteGeoFence)
	}

	route.POST("/v1/trackings/sendquip", trackingHandler.CreateSendquipDataV2)
	route.POST("/v1/trackings/general", trackingHandler.CreateLogSensorData)
	route.POST("/v1/logs/testing", trackingHandler.TestSendquip)

	geofenceAdminRoutes := route.Group("/v1/admin/geofences", middleware.APITokenMiddleware)
	{
		geofenceAdminRoutes.POST("/checks", trackingHandler.CheckGeoFences)
	}

	return route
}
