package dtos

import (
	"fmt"

	integrationConstants "assetfindr/internal/app/integration/constants"

	"gopkg.in/guregu/null.v4"
)

type InsertSensorDataIdentReq struct {
	IdentImei null.String `json:"ident"`
	DeviceID  null.String `json:"deviceid"`

	PayloadText null.String `json:"payload.text"`
	Timestamp   float64     `json:"timestamp"`
}

func (r *InsertSensorDataIdentReq) GetIdentAndTag() (string, string, string, error) {
	if r.IdentImei.Valid {
		return r.IdentImei.String, "imei", integrationConstants.INTEGRATION_TARGET_TYPE_FLESPI_TRACKING, nil
	}

	if r.DeviceID.Valid {
		return r.DeviceID.String, "device_id", integrationConstants.INTEGRATION_TARGET_TYPE_SENDQUIP_GATEWAY, nil
	}

	return "", "", "", fmt.Errorf("ident not found")

}

type InsertSubSensorDataIdentReq struct {
	ID null.String `json:"id"`
}

func (r *InsertSubSensorDataIdentReq) GetIdentAndTag() (string, string, string, error) {
	if r.ID.Valid {
		return r.ID.String, "tyre_sensor_id", integrationConstants.INTEGRATION_TARGET_TYPE_SENDQUIP_GATEWAY, nil
	}

	return "", "", "", fmt.Errorf("ident not found")
}

type InsertSensorDataReq struct {
	Ident string  `json:"ident"`
	Time  float64 `json:"time"`

	// Compressor
	PressAirFeedPressure       null.Float  `json:"compressor.press_air_feed_pressure"`
	PressAirExhaustTemperature null.Float  `json:"compressor.press_air_exhaust_temperature"`
	PressRunTime               null.Float  `json:"compressor.press_run_time"`
	PressLoadTime              null.Float  `json:"compressor.press_load_time"`
	PressPhaseACurrent         null.Float  `json:"compressor.press_phase_a_current"`
	PressPhaseBCurrent         null.Float  `json:"compressor.press_phase_b_current"`
	PressPhaseCCurrent         null.Float  `json:"compressor.press_phase_c_current"`
	PressRunState1             null.Float  `json:"compressor.press_run_state_1"`
	PressRunState2             null.Float  `json:"compressor.press_run_state_2"`
	PressOilFilterUsedTime     null.Float  `json:"compressor.press_oil_filter_used_time"`
	PressOilSeparatorUsedTime  null.Float  `json:"compressor.press_oil_separator_used_time"`
	PressAirFilterUsedTime     null.Float  `json:"compressor.press_air_filter_used_time"`
	PressLubeOilUsedTime       null.Float  `json:"compressor.press_lube_oil_used_time"`
	PressLubeGreaseUsedTime    null.Float  `json:"compressor.press_lube_grease_used_time"`
	PressMachineStatus         null.String `json:"compressor.press_machine_status"`
	PressCurrentImbalance      null.Float  `json:"compressor.press_current_imbalance"`

	CanManualData    map[string]string `json:"-"`
	HasCanManualData bool              `json:"-"`

	CanAbsFailureIndicatorStatus          null.Bool  `json:"can_bus.can_abs_failure_indicator_status"`
	CanAdditionalFrontLightsStatus        null.Bool  `json:"can_bus.can_additional_front_lights_status"`
	CanAdditionalRearLightsStatus         null.Bool  `json:"can_bus.can_additional_rear_lights_status"`
	CanAirConditionStatus                 null.Bool  `json:"can_bus.can_air_condition_status"`
	CanAirbagIndicatorStatus              null.Bool  `json:"can_bus.can_airbag_indicator_status"`
	CanAutomaticRetarderStatus            null.Bool  `json:"can_bus.can_automatic_retarder_status"`
	CanBatteryIndicatorStatus             null.Bool  `json:"can_bus.can_battery_indicator_status"`
	CanCarClosedRemoteStatus              null.Bool  `json:"can_bus.can_car_closed_remote_status"`
	CanCarClosedStatus                    null.Bool  `json:"can_bus.can_car_closed_status"`
	CanCentralDifferential4HiStatus       null.Bool  `json:"can_bus.can_central_differential_4_hi_status"`
	CanCentralDifferential4LoStatus       null.Bool  `json:"can_bus.can_central_differential_4_lo_status"`
	CanCheckEngineIndicatorStatus         null.Bool  `json:"can_bus.can_check_engine_indicator_status"`
	CanCngStatus                          null.Bool  `json:"can_bus.can_cng_status"`
	CanConnectionState1                   null.Int   `json:"can_bus.can_connection_state_1"`
	CanConnectionState2                   null.Int   `json:"can_bus.can_connection_state_2"`
	CanConnectionState3                   null.Int   `json:"can_bus.can_connection_state_3"`
	CanCoolantLevelLowIndicatorStatus     null.Bool  `json:"can_bus.can_coolant_level_low_indicator_status"`
	CanCruiseStatus                       null.Bool  `json:"can_bus.can_cruise_status"`
	CanDriveGearStatus                    null.Bool  `json:"can_bus.can_drive_gear_status"`
	CanDriverSeatbeltIndicatorStatus      null.Bool  `json:"can_bus.can_driver_seatbelt_indicator_status"`
	CanDriverSeatbeltStatus               null.Bool  `json:"can_bus.can_driver_seatbelt_status"`
	CanDynamicIgnitionStatus              null.Bool  `json:"can_bus.can_dynamic_ignition_status"`
	CanElectricEngineStatus               null.Bool  `json:"can_bus.can_electric_engine_status"`
	CanElectronicPowerControlStatus       null.Bool  `json:"can_bus.can_electronic_power_control_status"`
	CanEngineIgnitionStatus               null.Bool  `json:"can_bus.can_engine_ignition_status"`
	CanEngineLoadLevel                    null.Int   `json:"can_bus.can_engine_load_level"`
	CanEngineLockStatus                   null.Bool  `json:"can_bus.can_engine_lock_status"`
	CanEngineMotorhours                   null.Float `json:"can_bus.can_engine_motorhours"`
	CanEngineRpm                          null.Int   `json:"can_bus.can_engine_rpm"`
	CanEngineTemperature                  null.Float `json:"can_bus.can_engine_temperature"`
	CanEngineWorkingStatus                null.Bool  `json:"can_bus.can_engine_working_status"`
	CanEpsIndicatorStatus                 null.Bool  `json:"can_bus.can_eps_indicator_status"`
	CanEspIndicatorStatus                 null.Bool  `json:"can_bus.can_esp_indicator_status"`
	CanEspStatus                          null.Bool  `json:"can_bus.can_esp_status"`
	CanFactoryArmedStatus                 null.Bool  `json:"can_bus.can_factory_armed_status"`
	CanFrontDifferentialStatus            null.Bool  `json:"can_bus.can_front_differential_status"`
	CanFrontFogLightsStatus               null.Bool  `json:"can_bus.can_front_fog_lights_status"`
	CanFrontLeftDoorStatus                null.Bool  `json:"can_bus.can_front_left_door_status"`
	CanFrontPassengerSeatbeltStatus       null.Bool  `json:"can_bus.can_front_passenger_seatbelt_status"`
	CanFrontPassengerStatus               null.Bool  `json:"can_bus.can_front_passenger_status"`
	CanFrontRightDoorStatus               null.Bool  `json:"can_bus.can_front_right_door_status"`
	CanFuelConsumed                       null.Float `json:"can_bus.can_fuel_consumed"`
	CanFuelLevelLowIndicatorStatus        null.Bool  `json:"can_bus.can_fuel_level_low_indicator_status"`
	CanGlowPlugIndicatorStatus            null.Bool  `json:"can_bus.can_glow_plug_indicator_status"`
	CanHandbrakeIndicatorStatus           null.Bool  `json:"can_bus.can_handbrake_indicator_status"`
	CanHandbrakeStatus                    null.Bool  `json:"can_bus.can_handbrake_status"`
	CanHighBeamStatus                     null.Bool  `json:"can_bus.can_high_beam_status"`
	CanHoodStatus                         null.Bool  `json:"can_bus.can_hood_status"`
	CanIgnitionKeyStatus                  null.Bool  `json:"can_bus.can_ignition_key_status"`
	CanInterlockActive                    null.Bool  `json:"can_bus.can_interlock_active"`
	CanLightSignalStatus                  null.Bool  `json:"can_bus.can_light_signal_status"`
	CanLightsFailureIndicatorStatus       null.Bool  `json:"can_bus.can_lights_failure_indicator_status"`
	CanLightsHazardLightsStatus           null.Bool  `json:"can_bus.can_lights_hazard_lights_status"`
	CanLowBeamStatus                      null.Bool  `json:"can_bus.can_low_beam_status"`
	CanMaintenanceRequiredStatus          null.Bool  `json:"can_bus.can_maintenance_required_status"`
	CanManualRetarderStatus               null.Bool  `json:"can_bus.can_manual_retarder_status"`
	CanModuleSleepMode                    null.Bool  `json:"can_bus.can_module_sleep_mode"`
	CanNeutralGearStatus                  null.Bool  `json:"can_bus.can_neutral_gear_status"`
	CanOilPressureIndicatorStatus         null.Bool  `json:"can_bus.can_oil_pressure_indicator_status"`
	CanOperatorPresentStatus              null.Bool  `json:"can_bus.can_operator_present_status"`
	CanParkingLightsStatus                null.Bool  `json:"can_bus.can_parking_lights_status"`
	CanParkingStatus                      null.Bool  `json:"can_bus.can_parking_status"`
	CanPassengerSeatbeltIndicatorStatus   null.Bool  `json:"can_bus.can_passenger_seatbelt_indicator_status"`
	CanPedalBrakeStatus                   null.Bool  `json:"can_bus.can_pedal_brake_status"`
	CanPedalClutchStatus                  null.Bool  `json:"can_bus.can_pedal_clutch_status"`
	CanPrivateStatus                      null.Bool  `json:"can_bus.can_private_status"`
	CanProgramID                          null.Int   `json:"can_bus.can_program_id"`
	CanPtoStatus                          null.Bool  `json:"can_bus.can_pto_status"`
	CanReadyToDriveIndicatorStatus        null.Bool  `json:"can_bus.can_ready_to_drive_indicator_status"`
	CanRearCentralPassengerSeatbeltStatus null.Bool  `json:"can_bus.can_rear_central_passenger_seatbelt_status"`
	CanRearDifferentialStatus             null.Bool  `json:"can_bus.can_rear_differential_status"`
	CanRearFogLightsStatus                null.Bool  `json:"can_bus.can_rear_fog_lights_status"`
	CanRearLeftDoorStatus                 null.Bool  `json:"can_bus.can_rear_left_door_status"`
	CanRearLeftPassengerSeatbeltStatus    null.Bool  `json:"can_bus.can_rear_left_passenger_seatbelt_status"`
	CanRearRightDoorStatus                null.Bool  `json:"can_bus.can_rear_right_door_status"`
	CanRearRightPassengerSeatbeltStatus   null.Bool  `json:"can_bus.can_rear_right_passenger_seatbelt_status"`
	CanReverseGearStatus                  null.Bool  `json:"can_bus.can_reverse_gear_status"`
	CanRoofOpenedStatus                   null.Bool  `json:"can_bus.can_roof_opened_status"`
	CanSootFilterIndicatorStatus          null.Bool  `json:"can_bus.can_soot_filter_indicator_status"`
	CanStandaloneEngine                   null.Bool  `json:"can_bus.can_standalone_engine"`
	CanStopIndicatorStatus                null.Bool  `json:"can_bus.can_stop_indicator_status"`
	CanThrottlePedalLevel                 null.Int   `json:"can_bus.can_throttle_pedal_level"`
	CanTirePressureLowStatus              null.Bool  `json:"can_bus.can_tire_pressure_low_status"`
	CanTrackerCountedFuelConsumed         null.Float `json:"can_bus.can_tracker_counted_fuel_consumed"`
	CanTrackerCountedMileage              null.Float `json:"can_bus.can_tracker_counted_mileage"`
	CanTrailerAxleLiftStatus1             null.Bool  `json:"can_bus.can_trailer_axle_lift_status_1"`
	CanTrailerAxleLiftStatus2             null.Bool  `json:"can_bus.can_trailer_axle_lift_status_2"`
	CanTripEngineMotorhours               null.Float `json:"can_bus.can_trip_engine_motorhours"`
	CanTrunkStatus                        null.Bool  `json:"can_bus.can_trunk_status"`
	CanVehicleBatteryChargingStatus       null.Bool  `json:"can_bus.can_vehicle_battery_charging_status"`
	CanVehicleMileage                     null.Float `json:"can_bus.can_vehicle_mileage"`
	CanVehicleSpeed                       null.Int   `json:"can_bus.can_vehicle_speed"`
	CanWarningIndicatorStatus             null.Bool  `json:"can_bus.can_warning_indicator_status"`
	CanWearBrakePadsIndicatorStatus       null.Bool  `json:"can_bus.can_wear_brake_pads_indicator_status"`
	CanWebastoStatus                      null.Bool  `json:"can_bus.can_webasto_status"`
	CanFuelLevel                          null.Float `json:"can_bus.can_fuel_level"`
	CanEngineOilLevel                     null.Float `json:"can_bus.can_engine_oil_level"`
	CanEngineTorque                       null.Float `json:"can_bus.can_engine_torque"`
	CanAmbientAirTemperature              null.Float `json:"can_bus.can_ambient_air_temperature"`
	CanEngineCoolantTemperature           null.Float `json:"can_bus.can_engine_coolant_temperature"`
	CanFuelConsumption                    null.Float `json:"can_bus.can_fuel_consumption"`
	CanFuelEconomy                        null.Float `json:"can_bus.can_fuel_economy"`
	CanMilStatus                          null.Bool  `json:"can_bus.can_mil_status"`
	CanWheelSpeed                         null.Float `json:"can_bus.can_wheel_speed"`
	AdblueLevel                           null.Float `json:"can_bus.adblue_level"`

	EngineCoolantTemperature                               null.Float `json:"can_bus.engine_coolant_temperature"`
	EngineFuel_1Temperature_1                              null.Float `json:"can_bus.engine_fuel_1_temperature_1"`
	EngineOilTemperature_1                                 null.Float `json:"can_bus.engine_oil_temperature_1"`
	EngineTurbocharger_1OilTemperature                     null.Float `json:"can_bus.engine_turbocharger_1_oil_temperature"`
	EngineIntercoolerTemperature                           null.Float `json:"can_bus.engine_intercooler_temperature"`
	EngineChargeAirCoolerThermostatOpening                 null.Float `json:"can_bus.engine_charge_air_cooler_thermostat_opening"`
	EngineFuelDeliveryPressure                             null.Float `json:"can_bus.engine_fuel_delivery_pressure"`
	EngineExtendedCrankcaseBlowByPressure                  null.Float `json:"can_bus.engine_extended_crankcase_blow_by_pressure"`
	EngineOilLevel                                         null.Float `json:"can_bus.engine_oil_level"`
	EngineOilPressure_1                                    null.Float `json:"can_bus.engine_oil_pressure_1"`
	EngineCrankcasePressure_1                              null.Float `json:"can_bus.engine_crankcase_pressure_1"`
	EngineCoolantPressure_1                                null.Float `json:"can_bus.engine_coolant_pressure_1"`
	EngineCoolantLevel_1                                   null.Float `json:"can_bus.engine_coolant_level_1"`
	EngineTotalHoursOfOperation                            null.Float `json:"can_bus.engine_total_hours_of_operation"`
	EngineTotalRevolutions                                 null.Float `json:"can_bus.engine_total_revolutions"`
	FrontAxleSpeed                                         null.Float `json:"can_bus.front_axle_speed"`
	RelativeSpeedFrontAxleLeftWheel                        null.Float `json:"can_bus.relative_speed_front_axle_left_wheel"`
	RelativeSpeedFrontAxleRightWheel                       null.Float `json:"can_bus.relative_speed_front_axle_right_wheel"`
	RelativeSpeedRearAxle_1LeftWheel                       null.Float `json:"can_bus.relative_speed_rear_axle_1_left_wheel"`
	RelativeSpeedRearAxle_1RightWheel                      null.Float `json:"can_bus.relative_speed_rear_axle_1_right_wheel"`
	RelativeSpeedRearAxle_2LeftWheel                       null.Float `json:"can_bus.relative_speed_rear_axle_2_left_wheel"`
	RelativeSpeedRearAxle_2RightWheel                      null.Float `json:"can_bus.relative_speed_rear_axle_2_right_wheel"`
	EngineFuelRate                                         null.Float `json:"can_bus.engine_fuel_rate"`
	EngineInstantaneousFuelEconomy                         null.Float `json:"can_bus.engine_instantaneous_fuel_economy"`
	EngineAverageFuelEconomy                               null.Float `json:"can_bus.engine_average_fuel_economy"`
	EngineThrottleValve_1Position_1                        null.Float `json:"can_bus.engine_throttle_valve_1_position_1"`
	EngineThrottleValve_2Position                          null.Float `json:"can_bus.engine_throttle_valve_2_position"`
	TransmissionDrivelineEngaged                           null.Float `json:"can_bus.transmission_driveline_engaged"`
	TransmissionTorqueConverterLockupEngaged               null.Float `json:"can_bus.transmission_torque_converter_lockup_engaged"`
	TransmissionShiftInProcess                             null.Float `json:"can_bus.transmission_shift_in_process"`
	TransmissionTorqueConverterLockupTransitionInProcess   null.Float `json:"can_bus.transmission_torque_converter_lockup_transition_in_process"`
	TransmissionOutputShaftSpeed                           null.Float `json:"can_bus.transmission_output_shaft_speed"`
	PercentClutchSlip                                      null.Float `json:"can_bus.percent_clutch_slip"`
	EngineMomentaryOverspeedEnable                         null.Float `json:"can_bus.engine_momentary_overspeed_enable"`
	ProgressiveShiftDisable                                null.Float `json:"can_bus.progressive_shift_disable"`
	MomentaryEngineMaximumPowerEnable                      null.Float `json:"can_bus.momentary_engine_maximum_power_enable"`
	TransmissionInputShaftSpeed                            null.Float `json:"can_bus.transmission_input_shaft_speed"`
	SourceAddressOfControllingDeviceForTransmissionControl null.Float `json:"can_bus.source_address_of_controlling_device_for_transmission_control"`
	EngineTorqueMode                                       null.Float `json:"can_bus.engine_torque_mode"`
	ActualEnginePercentTorqueFractional                    null.Float `json:"can_bus.actual_engine_percent_torque_fractional"`
	DriverSDemandEnginePercentTorque                       null.Float `json:"can_bus.driver_s_demand_engine_percent_torque"`
	ActualEnginePercentTorque                              null.Float `json:"can_bus.actual_engine_percent_torque"`
	EngineSpeed                                            null.Float `json:"can_bus.engine_speed"`
	SourceAddressOfControllingDeviceForEngineControl       null.Float `json:"can_bus.source_address_of_controlling_device_for_engine_control"`
	EngineStarterMode                                      null.Float `json:"can_bus.engine_starter_mode"`
	EngineDemandPercentTorque                              null.Float `json:"can_bus.engine_demand_percent_torque"`
	AcceleratorPedal_1LowIdleSwitch                        null.Float `json:"can_bus.accelerator_pedal_1_low_idle_switch"`
	AcceleratorPedalKickdownSwitch                         null.Float `json:"can_bus.accelerator_pedal_kickdown_switch"`
	RoadSpeedLimitStatus                                   null.Float `json:"can_bus.road_speed_limit_status"`
	AcceleratorPedal_2LowIdleSwitch                        null.Float `json:"can_bus.accelerator_pedal_2_low_idle_switch"`
	AcceleratorPedal_1Position                             null.Float `json:"can_bus.accelerator_pedal_1_position"`
	EnginePercentLoadAtCurrentSpeed                        null.Float `json:"can_bus.engine_percent_load_at_current_speed"`
	RemoteAcceleratorPedalPosition                         null.Float `json:"can_bus.remote_accelerator_pedal_position"`
	AcceleratorPedal_2Position                             null.Float `json:"can_bus.accelerator_pedal_2_position"`
	VehicleAccelerationRateLimitStatus                     null.Float `json:"can_bus.vehicle_acceleration_rate_limit_status"`
	MomentaryEngineMaximumPowerEnableFeedback              null.Float `json:"can_bus.momentary_engine_maximum_power_enable_feedback"`
	DpfThermalManagementActive                             null.Float `json:"can_bus.dpf_thermal_management_active"`
	ScrThermalManagementActive                             null.Float `json:"can_bus.scr_thermal_management_active"`
	ActualMaximumAvailableEnginePercentTorque              null.Float `json:"can_bus.actual_maximum_available_engine_percent_torque"`
	EstimatedPumpingPercentTorque                          null.Float `json:"can_bus.estimated_pumping_percent_torque"`
	TwoSpeedAxleSwitch                                     null.Float `json:"can_bus.two_speed_axle_switch"`
	ParkingBrakeSwitch                                     null.Float `json:"can_bus.parking_brake_switch"`
	CruiseControlPauseSwitch                               null.Float `json:"can_bus.cruise_control_pause_switch"`
	ParkBrakeReleaseInhibitRequest                         null.Float `json:"can_bus.park_brake_release_inhibit_request"`
	WheelBasedVehicleSpeed                                 null.Float `json:"can_bus.wheel_based_vehicle_speed"`
	CruiseControlActive                                    null.Float `json:"can_bus.cruise_control_active"`
	CruiseControlEnableSwitch                              null.Float `json:"can_bus.cruise_control_enable_switch"`
	BrakeSwitch                                            null.Float `json:"can_bus.brake_switch"`
	ClutchSwitch                                           null.Float `json:"can_bus.clutch_switch"`
	CruiseControlSetSwitch                                 null.Float `json:"can_bus.cruise_control_set_switch"`
	CruiseControlCoastDecelerateSwitch                     null.Float `json:"can_bus.cruise_control_coast_decelerate_switch"`
	CruiseControlResumeSwitch                              null.Float `json:"can_bus.cruise_control_resume_switch"`
	CruiseControlAccelerateSwitch                          null.Float `json:"can_bus.cruise_control_accelerate_switch"`
	CruiseControlSetSpeed                                  null.Float `json:"can_bus.cruise_control_set_speed"`
	PtoGovernorState                                       null.Float `json:"can_bus.pto_governor_state"`
	CruiseControlStates                                    null.Float `json:"can_bus.cruise_control_states"`
	EngineIdleIncrementSwitch                              null.Float `json:"can_bus.engine_idle_increment_switch"`
	EngineIdleDecrementSwitch                              null.Float `json:"can_bus.engine_idle_decrement_switch"`
	EngineDiagnosticTestModeSwitch                         null.Float `json:"can_bus.engine_diagnostic_test_mode_switch"`
	EngineShutdownOverrideSwitch                           null.Float `json:"can_bus.engine_shutdown_override_switch"`
	EngineExhaustGasRecirculation_1MassFlowRate            null.Float `json:"can_bus.engine_exhaust_gas_recirculation_1_mass_flow_rate"`
	EngineIntakeAirMassFlowRate                            null.Float `json:"can_bus.engine_intake_air_mass_flow_rate"`
	EngineExhaustGasRecirculation_2MassFlowRate            null.Float `json:"can_bus.engine_exhaust_gas_recirculation_2_mass_flow_rate"`
	TargetFreshAirMassFlow                                 null.Float `json:"can_bus.target_fresh_air_mass_flow"`
	AsrEngineControlActive                                 null.Float `json:"can_bus.asr_engine_control_active"`
	AsrBrakeControlActive                                  null.Float `json:"can_bus.asr_brake_control_active"`
	AntiLockBrakingAbsActive                               null.Float `json:"can_bus.anti_lock_braking_abs_active"`
	EbsBrakeSwitch                                         null.Float `json:"can_bus.ebs_brake_switch"`
	BrakePedalPosition                                     null.Float `json:"can_bus.brake_pedal_position"`
	AbsOffRoadSwitch                                       null.Float `json:"can_bus.abs_off_road_switch"`
	AsrOffRoadSwitch                                       null.Float `json:"can_bus.asr_off_road_switch"`
	AsrHillHolderSwitch                                    null.Float `json:"can_bus.asr_hill_holder_switch"`
	TractionControlOverrideSwitch                          null.Float `json:"can_bus.traction_control_override_switch"`
	AcceleratorInterlockSwitch                             null.Float `json:"can_bus.accelerator_interlock_switch"`
	EngineDerateSwitch                                     null.Float `json:"can_bus.engine_derate_switch"`
	EngineAuxiliaryShutdownSwitch                          null.Float `json:"can_bus.engine_auxiliary_shutdown_switch"`
	RemoteAcceleratorEnableSwitch                          null.Float `json:"can_bus.remote_accelerator_enable_switch"`
	EngineRetarderSelection                                null.Float `json:"can_bus.engine_retarder_selection"`
	AbsFullyOperational                                    null.Float `json:"can_bus.abs_fully_operational"`
	EbsRedWarningSignal                                    null.Float `json:"can_bus.ebs_red_warning_signal"`
	AbsEbsAmberWarningSignalPoweredVehicle                 null.Float `json:"can_bus.abs_ebs_amber_warning_signal_powered_vehicle"`
	AtcAsrInformationSignal                                null.Float `json:"can_bus.atc_asr_information_signal"`
	SourceAddressOfControllingDeviceForBrakeControl        null.Float `json:"can_bus.source_address_of_controlling_device_for_brake_control"`
	RailroadModeSwitch                                     null.Float `json:"can_bus.railroad_mode_switch"`
	HaltBrakeSwitch                                        null.Float `json:"can_bus.halt_brake_switch"`
	TrailerAbsStatus                                       null.Float `json:"can_bus.trailer_abs_status"`
	TractorMountedTrailerAbsWarningSignal                  null.Float `json:"can_bus.tractor_mounted_trailer_abs_warning_signal"`
	TransmissionClutch_1Pressure                           null.Float `json:"can_bus.transmission_clutch_1_pressure"`
	TransmissionOilLevel_1                                 null.Float `json:"can_bus.transmission_oil_level_1"`
	TransmissionFilterDifferentialPressure                 null.Float `json:"can_bus.transmission_filter_differential_pressure"`
	Transmission_1OilPressure                              null.Float `json:"can_bus.transmission_1_oil_pressure"`
	Transmission_1OilTemperature_1                         null.Float `json:"can_bus.transmission_1_oil_temperature_1"`
	TransmissionOilLevel_1HighLow                          null.Float `json:"can_bus.transmission_oil_level_1_high_low"`
	TransmissionOilLevel_1CountdownTimer                   null.Float `json:"can_bus.transmission_oil_level_1_countdown_timer"`
	TransmissionOilLevel_1MeasurementStatus                null.Float `json:"can_bus.transmission_oil_level_1_measurement_status"`
	PneumaticSupplyPressure                                null.Float `json:"can_bus.pneumatic_supply_pressure"`
	ParkingAndOrTrailerAirPressure                         null.Float `json:"can_bus.parking_and_or_trailer_air_pressure"`
	ServiceBrakeCircuit_1AirPressure                       null.Float `json:"can_bus.service_brake_circuit_1_air_pressure"`
	ServiceBrakeCircuit_2AirPressure                       null.Float `json:"can_bus.service_brake_circuit_2_air_pressure"`
	AuxiliaryEquipmentSupplyPressure                       null.Float `json:"can_bus.auxiliary_equipment_supply_pressure"`
	AirSuspensionSupplyPressure_1                          null.Float `json:"can_bus.air_suspension_supply_pressure_1"`
	AirCompressorStatus                                    null.Float `json:"can_bus.air_compressor_status"`
	PowertrainCircuitAirSupplyPressure                     null.Float `json:"can_bus.powertrain_circuit_air_supply_pressure"`
	EngineExhaust_1NOx_1                                   null.Float `json:"can_bus.engine_exhaust_1_n_ox_1"`
	EngineExhaust_1PercentOxygen_1                         null.Float `json:"can_bus.engine_exhaust_1_percent_oxygen_1"`
	EngineExhaust_1GasSensor_1PowerInRange                 null.Float `json:"can_bus.engine_exhaust_1_gas_sensor_1_power_in_range"`
	EngineExhaust_1GasSensor_1AtTemperature                null.Float `json:"can_bus.engine_exhaust_1_gas_sensor_1_at_temperature"`
	EngineExhaust_1NOx_1ReadingStable                      null.Float `json:"can_bus.engine_exhaust_1_n_ox_1_reading_stable"`
	EngineExhaust_1WideRangePercentOxygen_1ReadingStable   null.Float `json:"can_bus.engine_exhaust_1_wide_range_percent_oxygen_1_reading_stable"`
	EngineExhaust_1GasSensor_1HeaterPreliminaryFmi         null.Float `json:"can_bus.engine_exhaust_1_gas_sensor_1_heater_preliminary_fmi"`
	EngineExhaust_1GasSensor_1HeaterControl                null.Float `json:"can_bus.engine_exhaust_1_gas_sensor_1_heater_control"`
	EngineExhaust_1NOxSensor_1PreliminaryFmi               null.Float `json:"can_bus.engine_exhaust_1_n_ox_sensor_1_preliminary_fmi"`
	EngineExhaust_1NOxSensor_1SelfDiagnosisStatus          null.Float `json:"can_bus.engine_exhaust_1_n_ox_sensor_1_self_diagnosis_status"`
	EngineExhaust_1OxygenSensor_1PreliminaryFmi            null.Float `json:"can_bus.engine_exhaust_1_oxygen_sensor_1_preliminary_fmi"`
	EngineTripFuelHighResolution                           null.Float `json:"can_bus.engine_trip_fuel_high_resolution"`
	EngineTotalFuelUsedHighResolution                      null.Float `json:"can_bus.engine_total_fuel_used_high_resolution"`
	Aftertreatment_1ScrIntakeTemperature                   null.Float `json:"can_bus.aftertreatment_1_scr_intake_temperature"`
	Aftertreatment_1ScrIntakeTemperaturePreliminaryFmi     null.Float `json:"can_bus.aftertreatment_1_scr_intake_temperature_preliminary_fmi"`
	Aftertreatment_1ScrOutletTemperature                   null.Float `json:"can_bus.aftertreatment_1_scr_outlet_temperature"`
	Aftertreatment_1ScrOutletTemperaturePreliminaryFmi     null.Float `json:"can_bus.aftertreatment_1_scr_outlet_temperature_preliminary_fmi"`
	PoweredVehicleWeight                                   null.Float `json:"can_bus.powered_vehicle_weight"`
	GrossCombinationVehicleWeight                          null.Float `json:"can_bus.gross_combination_vehicle_weight"`
	GrossCombinationVehicleWeightConfidence                null.Float `json:"can_bus.gross_combination_vehicle_weight_confidence"`
	TotalVehicleDistanceHighResolution                     null.Float `json:"can_bus.total_vehicle_distance_high_resolution"`
	TripDistanceHighResolution                             null.Float `json:"can_bus.trip_distance_high_resolution"`
	Seconds                                                null.Float `json:"can_bus.seconds"`
	Minutes                                                null.Float `json:"can_bus.minutes"`
	Hours                                                  null.Float `json:"can_bus.hours"`
	Month                                                  null.Float `json:"can_bus.month"`
	Day                                                    null.Float `json:"can_bus.day"`
	Year                                                   null.Float `json:"can_bus.year"`
	LocalMinuteOffset                                      null.Float `json:"can_bus.local_minute_offset"`
	LocalHourOffset                                        null.Float `json:"can_bus.local_hour_offset"`
	BarometricPressure                                     null.Float `json:"can_bus.barometric_pressure"`
	CabInteriorTemperature                                 null.Float `json:"can_bus.cab_interior_temperature"`
	AmbientAirTemperature                                  null.Float `json:"can_bus.ambient_air_temperature"`
	EngineIntake_1AirTemperature                           null.Float `json:"can_bus.engine_intake_1_air_temperature"`
	RoadSurfaceTemperature                                 null.Float `json:"can_bus.road_surface_temperature"`
	Aftertreatment_1DieselParticulateFilterIntakePressure  null.Float `json:"can_bus.aftertreatment_1_diesel_particulate_filter_intake_pressure"`
	EngineIntakeManifold_1Pressure                         null.Float `json:"can_bus.engine_intake_manifold_1_pressure"`
	EngineIntakeManifold_1Temperature                      null.Float `json:"can_bus.engine_intake_manifold_1_temperature"`
	EngineIntakeAirPressure                                null.Float `json:"can_bus.engine_intake_air_pressure"`
	EngineAirFilter_1DifferentialPressure                  null.Float `json:"can_bus.engine_air_filter_1_differential_pressure"`
	EngineExhaustTemperature                               null.Float `json:"can_bus.engine_exhaust_temperature"`
	EngineCoolantFilterDifferentialPressure                null.Float `json:"can_bus.engine_coolant_filter_differential_pressure"`

	PositionAltitude   null.Int   `json:"gps.position_altitude"`
	PositionDirection  null.Int   `json:"gps.position_direction"`
	PositionHdop       null.Float `json:"gps.position_hdop"`
	PositionLatitude   null.Float `json:"gps.position_latitude"`
	PositionLongitude  null.Float `json:"gps.position_longitude"`
	PositionPdop       null.Float `json:"gps.position_pdop"`
	PositionSatellites null.Int   `json:"gps.position_satellites"`
	PositionSpeed      null.Int   `json:"gps.position_speed"`
	PositionValid      null.Bool  `json:"gps.position_valid"`
	VehicleMileage     null.Float `json:"gps.vehicle_mileage"`

	BatteryCurrent             null.Float `json:"general.battery_current"`
	BatteryVoltage             null.Float `json:"general.battery_voltage"`
	GsmSignalLevel             null.Int   `json:"general.gsm_signal_level"`
	GsmOperatorCode            null.Int   `json:"general.gsm_operator_code"`
	MovementStatus             null.Bool  `json:"general.movement_status"`
	ExternalPowersourceVoltage null.Float `json:"general.external_powersource_voltage"`
	SdStatus                   null.Bool  `json:"general.sd_status"`
	XAccelaration              null.Float `json:"general.x_acceleration"`
	YAccelaration              null.Float `json:"general.y_acceleration"`
	ZAccelaration              null.Float `json:"general.z_acceleration"`
	PtoDriveEngagementEnum     null.Int   `json:"general.pto_drive_engagement_enum"`
	FuelConsumed               null.Float `json:"general.fuel_consumed"`
	DigitalInput               null.Bool  `json:"general.digital_input"`
	DigitalOutput              null.Bool  `json:"general.digital_output"`
	UnplugDetection            null.Bool  `json:"general.unplug_detection"`

	TyreIdentMacAddress null.String `json:"tyre.ident_mac_address"`
	TyreBatteryVoltage  null.Float  `json:"tyre.battery_voltage"`
	TyreBatteryPercent  null.Float  `json:"tyre.battery_percent"`
	TyrePressure        null.Float  `json:"tyre.pressure"`
	TyreTemperature     null.Float  `json:"tyre.temperature"`
	TyrePosition        null.Int    `json:"-"`
	TyreParentAssetID   null.String `json:"-"`
	TyreRowPosition     null.Int    `json:"-"`
	// Add more data
}

type CanManualSensorDataReq struct {
	CanEngineTemperature                   null.Float `json:"can_bus.can_engine_temperature"`
	EngineCoolantTemperature               null.Float `json:"can_bus.engine_coolant_temperature"`
	EngineFuel_1Temperature_1              null.Float `json:"can_bus.engine_fuel_1_temperature_1"`
	EngineOilTemperature_1                 null.Float `json:"can_bus.engine_oil_temperature_1"`
	EngineTurbocharger_1OilTemperature     null.Float `json:"can_bus.engine_turbocharger_1_oil_temperature"`
	EngineIntercoolerTemperature           null.Float `json:"can_bus.engine_intercooler_temperature"`
	EngineChargeAirCoolerThermostatOpening null.Float `json:"can_bus.engine_charge_air_cooler_thermostat_opening"`
	EngineFuelDeliveryPressure             null.Float `json:"can_bus.engine_fuel_delivery_pressure"`
	EngineExtendedCrankcaseBlowByPressure  null.Float `json:"can_bus.engine_extended_crankcase_blow_by_pressure"`
	EngineOilLevel                         null.Float `json:"can_bus.engine_oil_level"`
	EngineOilPressure_1                    null.Float `json:"can_bus.engine_oil_pressure_1"`
	EngineCrankcasePressure_1              null.Float `json:"can_bus.engine_crankcase_pressure_1"`
	EngineCoolantPressure_1                null.Float `json:"can_bus.engine_coolant_pressure_1"`
	EngineCoolantLevel_1                   null.Float `json:"can_bus.engine_coolant_level_1"`
	EngineTotalHoursOfOperation            null.Float `json:"can_bus.engine_total_hours_of_operation"`
	EngineTotalRevolutions                 null.Float `json:"can_bus.engine_total_revolutions"`

	FrontAxleSpeed                                         null.Float `json:"can_bus.front_axle_speed"`
	RelativeSpeedFrontAxleLeftWheel                        null.Float `json:"can_bus.relative_speed_front_axle_left_wheel"`
	RelativeSpeedFrontAxleRightWheel                       null.Float `json:"can_bus.relative_speed_front_axle_right_wheel"`
	RelativeSpeedRearAxle_1LeftWheel                       null.Float `json:"can_bus.relative_speed_rear_axle_1_left_wheel"`
	RelativeSpeedRearAxle_1RightWheel                      null.Float `json:"can_bus.relative_speed_rear_axle_1_right_wheel"`
	RelativeSpeedRearAxle_2LeftWheel                       null.Float `json:"can_bus.relative_speed_rear_axle_2_left_wheel"`
	RelativeSpeedRearAxle_2RightWheel                      null.Float `json:"can_bus.relative_speed_rear_axle_2_right_wheel"`
	EngineFuelRate                                         null.Float `json:"can_bus.engine_fuel_rate"`
	EngineInstantaneousFuelEconomy                         null.Float `json:"can_bus.engine_instantaneous_fuel_economy"`
	EngineAverageFuelEconomy                               null.Float `json:"can_bus.engine_average_fuel_economy"`
	EngineThrottleValve_1Position_1                        null.Float `json:"can_bus.engine_throttle_valve_1_position_1"`
	EngineThrottleValve_2Position                          null.Float `json:"can_bus.engine_throttle_valve_2_position"`
	TransmissionDrivelineEngaged                           null.Float `json:"can_bus.transmission_driveline_engaged"`
	TransmissionTorqueConverterLockupEngaged               null.Float `json:"can_bus.transmission_torque_converter_lockup_engaged"`
	TransmissionShiftInProcess                             null.Float `json:"can_bus.transmission_shift_in_process"`
	TransmissionTorqueConverterLockupTransitionInProcess   null.Float `json:"can_bus.transmission_torque_converter_lockup_transition_in_process"`
	TransmissionOutputShaftSpeed                           null.Float `json:"can_bus.transmission_output_shaft_speed"`
	PercentClutchSlip                                      null.Float `json:"can_bus.percent_clutch_slip"`
	EngineMomentaryOverspeedEnable                         null.Float `json:"can_bus.engine_momentary_overspeed_enable"`
	ProgressiveShiftDisable                                null.Float `json:"can_bus.progressive_shift_disable"`
	MomentaryEngineMaximumPowerEnable                      null.Float `json:"can_bus.momentary_engine_maximum_power_enable"`
	TransmissionInputShaftSpeed                            null.Float `json:"can_bus.transmission_input_shaft_speed"`
	SourceAddressOfControllingDeviceForTransmissionControl null.Float `json:"can_bus.source_address_of_controlling_device_for_transmission_control"`
	EngineTorqueMode                                       null.Float `json:"can_bus.engine_torque_mode"`
	ActualEnginePercentTorqueFractional                    null.Float `json:"can_bus.actual_engine_percent_torque_fractional"`
	DriverSDemandEnginePercentTorque                       null.Float `json:"can_bus.driver_s_demand_engine_percent_torque"`
	ActualEnginePercentTorque                              null.Float `json:"can_bus.actual_engine_percent_torque"`
	EngineSpeed                                            null.Float `json:"can_bus.engine_speed"`
	SourceAddressOfControllingDeviceForEngineControl       null.Float `json:"can_bus.source_address_of_controlling_device_for_engine_control"`
	EngineStarterMode                                      null.Float `json:"can_bus.engine_starter_mode"`
	EngineDemandPercentTorque                              null.Float `json:"can_bus.engine_demand_percent_torque"`
	AcceleratorPedal_1LowIdleSwitch                        null.Float `json:"can_bus.accelerator_pedal_1_low_idle_switch"`
	AcceleratorPedalKickdownSwitch                         null.Float `json:"can_bus.accelerator_pedal_kickdown_switch"`
	RoadSpeedLimitStatus                                   null.Float `json:"can_bus.road_speed_limit_status"`
	AcceleratorPedal_2LowIdleSwitch                        null.Float `json:"can_bus.accelerator_pedal_2_low_idle_switch"`
	AcceleratorPedal_1Position                             null.Float `json:"can_bus.accelerator_pedal_1_position"`
	EnginePercentLoadAtCurrentSpeed                        null.Float `json:"can_bus.engine_percent_load_at_current_speed"`
	RemoteAcceleratorPedalPosition                         null.Float `json:"can_bus.remote_accelerator_pedal_position"`
	AcceleratorPedal_2Position                             null.Float `json:"can_bus.accelerator_pedal_2_position"`
	VehicleAccelerationRateLimitStatus                     null.Float `json:"can_bus.vehicle_acceleration_rate_limit_status"`
	MomentaryEngineMaximumPowerEnableFeedback              null.Float `json:"can_bus.momentary_engine_maximum_power_enable_feedback"`
	DpfThermalManagementActive                             null.Float `json:"can_bus.dpf_thermal_management_active"`
	ScrThermalManagementActive                             null.Float `json:"can_bus.scr_thermal_management_active"`
	ActualMaximumAvailableEnginePercentTorque              null.Float `json:"can_bus.actual_maximum_available_engine_percent_torque"`
	EstimatedPumpingPercentTorque                          null.Float `json:"can_bus.estimated_pumping_percent_torque"`
	TwoSpeedAxleSwitch                                     null.Float `json:"can_bus.two_speed_axle_switch"`
	ParkingBrakeSwitch                                     null.Float `json:"can_bus.parking_brake_switch"`
	CruiseControlPauseSwitch                               null.Float `json:"can_bus.cruise_control_pause_switch"`
	ParkBrakeReleaseInhibitRequest                         null.Float `json:"can_bus.park_brake_release_inhibit_request"`
	WheelBasedVehicleSpeed                                 null.Float `json:"can_bus.wheel_based_vehicle_speed"`
	CruiseControlActive                                    null.Float `json:"can_bus.cruise_control_active"`
	CruiseControlEnableSwitch                              null.Float `json:"can_bus.cruise_control_enable_switch"`
	BrakeSwitch                                            null.Float `json:"can_bus.brake_switch"`
	ClutchSwitch                                           null.Float `json:"can_bus.clutch_switch"`
	CruiseControlSetSwitch                                 null.Float `json:"can_bus.cruise_control_set_switch"`
	CruiseControlCoastDecelerateSwitch                     null.Float `json:"can_bus.cruise_control_coast_decelerate_switch"`
	CruiseControlResumeSwitch                              null.Float `json:"can_bus.cruise_control_resume_switch"`
	CruiseControlAccelerateSwitch                          null.Float `json:"can_bus.cruise_control_accelerate_switch"`
	CruiseControlSetSpeed                                  null.Float `json:"can_bus.cruise_control_set_speed"`
	PtoGovernorState                                       null.Float `json:"can_bus.pto_governor_state"`
	CruiseControlStates                                    null.Float `json:"can_bus.cruise_control_states"`
	EngineIdleIncrementSwitch                              null.Float `json:"can_bus.engine_idle_increment_switch"`
	EngineIdleDecrementSwitch                              null.Float `json:"can_bus.engine_idle_decrement_switch"`
	EngineDiagnosticTestModeSwitch                         null.Float `json:"can_bus.engine_diagnostic_test_mode_switch"`
	EngineShutdownOverrideSwitch                           null.Float `json:"can_bus.engine_shutdown_override_switch"`
	EngineExhaustGasRecirculation_1MassFlowRate            null.Float `json:"can_bus.engine_exhaust_gas_recirculation_1_mass_flow_rate"`
	EngineIntakeAirMassFlowRate                            null.Float `json:"can_bus.engine_intake_air_mass_flow_rate"`
	EngineExhaustGasRecirculation_2MassFlowRate            null.Float `json:"can_bus.engine_exhaust_gas_recirculation_2_mass_flow_rate"`
	TargetFreshAirMassFlow                                 null.Float `json:"can_bus.target_fresh_air_mass_flow"`
	AsrEngineControlActive                                 null.Float `json:"can_bus.asr_engine_control_active"`
	AsrBrakeControlActive                                  null.Float `json:"can_bus.asr_brake_control_active"`
	AntiLockBrakingAbsActive                               null.Float `json:"can_bus.anti_lock_braking_abs_active"`
	EbsBrakeSwitch                                         null.Float `json:"can_bus.ebs_brake_switch"`
	BrakePedalPosition                                     null.Float `json:"can_bus.brake_pedal_position"`
	AbsOffRoadSwitch                                       null.Float `json:"can_bus.abs_off_road_switch"`
	AsrOffRoadSwitch                                       null.Float `json:"can_bus.asr_off_road_switch"`
	AsrHillHolderSwitch                                    null.Float `json:"can_bus.asr_hill_holder_switch"`
	TractionControlOverrideSwitch                          null.Float `json:"can_bus.traction_control_override_switch"`
	AcceleratorInterlockSwitch                             null.Float `json:"can_bus.accelerator_interlock_switch"`
	EngineDerateSwitch                                     null.Float `json:"can_bus.engine_derate_switch"`
	EngineAuxiliaryShutdownSwitch                          null.Float `json:"can_bus.engine_auxiliary_shutdown_switch"`
	RemoteAcceleratorEnableSwitch                          null.Float `json:"can_bus.remote_accelerator_enable_switch"`
	EngineRetarderSelection                                null.Float `json:"can_bus.engine_retarder_selection"`
	AbsFullyOperational                                    null.Float `json:"can_bus.abs_fully_operational"`
	EbsRedWarningSignal                                    null.Float `json:"can_bus.ebs_red_warning_signal"`
	AbsEbsAmberWarningSignalPoweredVehicle                 null.Float `json:"can_bus.abs_ebs_amber_warning_signal_powered_vehicle"`
	AtcAsrInformationSignal                                null.Float `json:"can_bus.atc_asr_information_signal"`
	SourceAddressOfControllingDeviceForBrakeControl        null.Float `json:"can_bus.source_address_of_controlling_device_for_brake_control"`
	RailroadModeSwitch                                     null.Float `json:"can_bus.railroad_mode_switch"`
	HaltBrakeSwitch                                        null.Float `json:"can_bus.halt_brake_switch"`
	TrailerAbsStatus                                       null.Float `json:"can_bus.trailer_abs_status"`
	TractorMountedTrailerAbsWarningSignal                  null.Float `json:"can_bus.tractor_mounted_trailer_abs_warning_signal"`
	TransmissionClutch_1Pressure                           null.Float `json:"can_bus.transmission_clutch_1_pressure"`
	TransmissionOilLevel_1                                 null.Float `json:"can_bus.transmission_oil_level_1"`
	TransmissionFilterDifferentialPressure                 null.Float `json:"can_bus.transmission_filter_differential_pressure"`
	Transmission_1OilPressure                              null.Float `json:"can_bus.transmission_1_oil_pressure"`
	Transmission_1OilTemperature_1                         null.Float `json:"can_bus.transmission_1_oil_temperature_1"`
	TransmissionOilLevel_1HighLow                          null.Float `json:"can_bus.transmission_oil_level_1_high_low"`
	TransmissionOilLevel_1CountdownTimer                   null.Float `json:"can_bus.transmission_oil_level_1_countdown_timer"`
	TransmissionOilLevel_1MeasurementStatus                null.Float `json:"can_bus.transmission_oil_level_1_measurement_status"`
	PneumaticSupplyPressure                                null.Float `json:"can_bus.pneumatic_supply_pressure"`
	ParkingAndOrTrailerAirPressure                         null.Float `json:"can_bus.parking_and_or_trailer_air_pressure"`
	ServiceBrakeCircuit_1AirPressure                       null.Float `json:"can_bus.service_brake_circuit_1_air_pressure"`
	ServiceBrakeCircuit_2AirPressure                       null.Float `json:"can_bus.service_brake_circuit_2_air_pressure"`
	AuxiliaryEquipmentSupplyPressure                       null.Float `json:"can_bus.auxiliary_equipment_supply_pressure"`
	AirSuspensionSupplyPressure_1                          null.Float `json:"can_bus.air_suspension_supply_pressure_1"`
	AirCompressorStatus                                    null.Float `json:"can_bus.air_compressor_status"`
	PowertrainCircuitAirSupplyPressure                     null.Float `json:"can_bus.powertrain_circuit_air_supply_pressure"`
	EngineExhaust_1NOx_1                                   null.Float `json:"can_bus.engine_exhaust_1_n_ox_1"`
	EngineExhaust_1PercentOxygen_1                         null.Float `json:"can_bus.engine_exhaust_1_percent_oxygen_1"`
	EngineExhaust_1GasSensor_1PowerInRange                 null.Float `json:"can_bus.engine_exhaust_1_gas_sensor_1_power_in_range"`
	EngineExhaust_1GasSensor_1AtTemperature                null.Float `json:"can_bus.engine_exhaust_1_gas_sensor_1_at_temperature"`
	EngineExhaust_1NOx_1ReadingStable                      null.Float `json:"can_bus.engine_exhaust_1_n_ox_1_reading_stable"`
	EngineExhaust_1WideRangePercentOxygen_1ReadingStable   null.Float `json:"can_bus.engine_exhaust_1_wide_range_percent_oxygen_1_reading_stable"`
	EngineExhaust_1GasSensor_1HeaterPreliminaryFmi         null.Float `json:"can_bus.engine_exhaust_1_gas_sensor_1_heater_preliminary_fmi"`
	EngineExhaust_1GasSensor_1HeaterControl                null.Float `json:"can_bus.engine_exhaust_1_gas_sensor_1_heater_control"`
	EngineExhaust_1NOxSensor_1PreliminaryFmi               null.Float `json:"can_bus.engine_exhaust_1_n_ox_sensor_1_preliminary_fmi"`
	EngineExhaust_1NOxSensor_1SelfDiagnosisStatus          null.Float `json:"can_bus.engine_exhaust_1_n_ox_sensor_1_self_diagnosis_status"`
	EngineExhaust_1OxygenSensor_1PreliminaryFmi            null.Float `json:"can_bus.engine_exhaust_1_oxygen_sensor_1_preliminary_fmi"`
	EngineTripFuelHighResolution                           null.Float `json:"can_bus.engine_trip_fuel_high_resolution"`
	EngineTotalFuelUsedHighResolution                      null.Float `json:"can_bus.engine_total_fuel_used_high_resolution"`
	Aftertreatment_1ScrIntakeTemperature                   null.Float `json:"can_bus.aftertreatment_1_scr_intake_temperature"`
	Aftertreatment_1ScrIntakeTemperaturePreliminaryFmi     null.Float `json:"can_bus.aftertreatment_1_scr_intake_temperature_preliminary_fmi"`
	Aftertreatment_1ScrOutletTemperature                   null.Float `json:"can_bus.aftertreatment_1_scr_outlet_temperature"`
	Aftertreatment_1ScrOutletTemperaturePreliminaryFmi     null.Float `json:"can_bus.aftertreatment_1_scr_outlet_temperature_preliminary_fmi"`
	PoweredVehicleWeight                                   null.Float `json:"can_bus.powered_vehicle_weight"`
	GrossCombinationVehicleWeight                          null.Float `json:"can_bus.gross_combination_vehicle_weight"`
	GrossCombinationVehicleWeightConfidence                null.Float `json:"can_bus.gross_combination_vehicle_weight_confidence"`
	TotalVehicleDistanceHighResolution                     null.Float `json:"can_bus.total_vehicle_distance_high_resolution"`
	TripDistanceHighResolution                             null.Float `json:"can_bus.trip_distance_high_resolution"`
	Seconds                                                null.Float `json:"can_bus.seconds"`
	Minutes                                                null.Float `json:"can_bus.minutes"`
	Hours                                                  null.Float `json:"can_bus.hours"`
	Month                                                  null.Float `json:"can_bus.month"`
	Day                                                    null.Float `json:"can_bus.day"`
	Year                                                   null.Float `json:"can_bus.year"`
	LocalMinuteOffset                                      null.Float `json:"can_bus.local_minute_offset"`
	LocalHourOffset                                        null.Float `json:"can_bus.local_hour_offset"`
	BarometricPressure                                     null.Float `json:"can_bus.barometric_pressure"`
	CabInteriorTemperature                                 null.Float `json:"can_bus.cab_interior_temperature"`
	AmbientAirTemperature                                  null.Float `json:"can_bus.ambient_air_temperature"`
	EngineIntake_1AirTemperature                           null.Float `json:"can_bus.engine_intake_1_air_temperature"`
	RoadSurfaceTemperature                                 null.Float `json:"can_bus.road_surface_temperature"`
	Aftertreatment_1DieselParticulateFilterIntakePressure  null.Float `json:"can_bus.aftertreatment_1_diesel_particulate_filter_intake_pressure"`
	EngineIntakeManifold_1Pressure                         null.Float `json:"can_bus.engine_intake_manifold_1_pressure"`
	EngineIntakeManifold_1Temperature                      null.Float `json:"can_bus.engine_intake_manifold_1_temperature"`
	EngineIntakeAirPressure                                null.Float `json:"can_bus.engine_intake_air_pressure"`
	EngineAirFilter_1DifferentialPressure                  null.Float `json:"can_bus.engine_air_filter_1_differential_pressure"`
	EngineExhaustTemperature                               null.Float `json:"can_bus.engine_exhaust_temperature"`
	EngineCoolantFilterDifferentialPressure                null.Float `json:"can_bus.engine_coolant_filter_differential_pressure"`
}

func (r *InsertSensorDataReq) IsNeedToCheckAlert() bool {
	return r.BatteryVoltage.Valid || r.PositionSpeed.Valid || r.TyrePressure.Valid
}

func (r *InsertSensorDataReq) IsCompressorDataValid() bool {
	return r.PressAirFeedPressure.Valid ||
		r.PressAirExhaustTemperature.Valid ||
		r.PressRunTime.Valid ||
		r.PressLoadTime.Valid ||
		r.PressPhaseACurrent.Valid ||
		r.PressPhaseBCurrent.Valid ||
		r.PressPhaseCCurrent.Valid ||
		r.PressRunState1.Valid ||
		r.PressRunState2.Valid ||
		r.PressOilFilterUsedTime.Valid ||
		r.PressOilSeparatorUsedTime.Valid ||
		r.PressAirFilterUsedTime.Valid ||
		r.PressLubeOilUsedTime.Valid ||
		r.PressLubeGreaseUsedTime.Valid ||
		r.PressMachineStatus.Valid ||
		r.PressCurrentImbalance.Valid
}
func (r *InsertSensorDataReq) IsTyreSensorDataValid() bool {
	return r.TyreIdentMacAddress.Valid ||
		r.TyreBatteryVoltage.Valid ||
		r.TyreBatteryPercent.Valid ||
		r.TyrePressure.Valid ||
		r.TyreTemperature.Valid
}

func (r *InsertSensorDataReq) IsCanBusDataValid() bool {
	return r.HasCanManualData || r.CanAbsFailureIndicatorStatus.Valid ||
		r.CanAdditionalFrontLightsStatus.Valid ||
		r.CanAdditionalRearLightsStatus.Valid ||
		r.CanAirConditionStatus.Valid ||
		r.CanAirbagIndicatorStatus.Valid ||
		r.CanAutomaticRetarderStatus.Valid ||
		r.CanBatteryIndicatorStatus.Valid ||
		r.CanCarClosedRemoteStatus.Valid ||
		r.CanCarClosedStatus.Valid ||
		r.CanCentralDifferential4HiStatus.Valid ||
		r.CanCentralDifferential4LoStatus.Valid ||
		r.CanCheckEngineIndicatorStatus.Valid ||
		r.CanCngStatus.Valid ||
		r.CanConnectionState1.Valid ||
		r.CanConnectionState2.Valid ||
		r.CanConnectionState3.Valid ||
		r.CanCoolantLevelLowIndicatorStatus.Valid ||
		r.CanCruiseStatus.Valid ||
		r.CanDriveGearStatus.Valid ||
		r.CanDriverSeatbeltIndicatorStatus.Valid ||
		r.CanDriverSeatbeltStatus.Valid ||
		r.CanDynamicIgnitionStatus.Valid ||
		r.CanElectricEngineStatus.Valid ||
		r.CanElectronicPowerControlStatus.Valid ||
		r.CanEngineIgnitionStatus.Valid ||
		r.CanEngineLoadLevel.Valid ||
		r.CanEngineLockStatus.Valid ||
		r.CanEngineMotorhours.Valid ||
		r.CanEngineRpm.Valid ||
		r.CanEngineTemperature.Valid ||
		r.CanEngineWorkingStatus.Valid ||
		r.CanEpsIndicatorStatus.Valid ||
		r.CanEspIndicatorStatus.Valid ||
		r.CanEspStatus.Valid ||
		r.CanFactoryArmedStatus.Valid ||
		r.CanFrontDifferentialStatus.Valid ||
		r.CanFrontFogLightsStatus.Valid ||
		r.CanFrontLeftDoorStatus.Valid ||
		r.CanFrontPassengerSeatbeltStatus.Valid ||
		r.CanFrontPassengerStatus.Valid ||
		r.CanFrontRightDoorStatus.Valid ||
		r.CanFuelConsumed.Valid ||
		r.CanFuelLevelLowIndicatorStatus.Valid ||
		r.CanGlowPlugIndicatorStatus.Valid ||
		r.CanHandbrakeIndicatorStatus.Valid ||
		r.CanHandbrakeStatus.Valid ||
		r.CanHighBeamStatus.Valid ||
		r.CanHoodStatus.Valid ||
		r.CanIgnitionKeyStatus.Valid ||
		r.CanInterlockActive.Valid ||
		r.CanLightSignalStatus.Valid ||
		r.CanLightsFailureIndicatorStatus.Valid ||
		r.CanLightsHazardLightsStatus.Valid ||
		r.CanLowBeamStatus.Valid ||
		r.CanMaintenanceRequiredStatus.Valid ||
		r.CanManualRetarderStatus.Valid ||
		r.CanModuleSleepMode.Valid ||
		r.CanNeutralGearStatus.Valid ||
		r.CanOilPressureIndicatorStatus.Valid ||
		r.CanOperatorPresentStatus.Valid ||
		r.CanParkingLightsStatus.Valid ||
		r.CanParkingStatus.Valid ||
		r.CanPassengerSeatbeltIndicatorStatus.Valid ||
		r.CanPedalBrakeStatus.Valid ||
		r.CanPedalClutchStatus.Valid ||
		r.CanPrivateStatus.Valid ||
		r.CanProgramID.Valid ||
		r.CanPtoStatus.Valid ||
		r.CanReadyToDriveIndicatorStatus.Valid ||
		r.CanRearCentralPassengerSeatbeltStatus.Valid ||
		r.CanRearDifferentialStatus.Valid ||
		r.CanRearFogLightsStatus.Valid ||
		r.CanRearLeftDoorStatus.Valid ||
		r.CanRearLeftPassengerSeatbeltStatus.Valid ||
		r.CanRearRightDoorStatus.Valid ||
		r.CanRearRightPassengerSeatbeltStatus.Valid ||
		r.CanReverseGearStatus.Valid ||
		r.CanRoofOpenedStatus.Valid ||
		r.CanSootFilterIndicatorStatus.Valid ||
		r.CanStandaloneEngine.Valid ||
		r.CanStopIndicatorStatus.Valid ||
		r.CanThrottlePedalLevel.Valid ||
		r.CanTirePressureLowStatus.Valid ||
		r.CanTrackerCountedFuelConsumed.Valid ||
		r.CanTrackerCountedMileage.Valid ||
		r.CanTrailerAxleLiftStatus1.Valid ||
		r.CanTrailerAxleLiftStatus2.Valid ||
		r.CanTripEngineMotorhours.Valid ||
		r.CanTrunkStatus.Valid ||
		r.CanVehicleBatteryChargingStatus.Valid ||
		r.CanVehicleMileage.Valid ||
		r.CanVehicleSpeed.Valid ||
		r.CanWarningIndicatorStatus.Valid ||
		r.CanWearBrakePadsIndicatorStatus.Valid ||
		r.CanWebastoStatus.Valid ||
		r.CanFuelLevel.Valid ||
		r.CanEngineOilLevel.Valid ||
		r.CanEngineTorque.Valid ||
		r.CanAmbientAirTemperature.Valid ||
		r.CanEngineCoolantTemperature.Valid ||
		r.CanFuelConsumption.Valid ||
		r.CanFuelEconomy.Valid ||
		r.CanMilStatus.Valid ||
		r.AdblueLevel.Valid ||
		r.CanWheelSpeed.Valid ||
		r.RelativeSpeedFrontAxleLeftWheel.Valid ||
		r.RelativeSpeedFrontAxleRightWheel.Valid ||
		r.RelativeSpeedRearAxle_1LeftWheel.Valid ||
		r.RelativeSpeedRearAxle_1RightWheel.Valid ||
		r.RelativeSpeedRearAxle_2LeftWheel.Valid ||
		r.RelativeSpeedRearAxle_2RightWheel.Valid ||
		r.EngineFuelRate.Valid ||
		r.EngineInstantaneousFuelEconomy.Valid ||
		r.EngineAverageFuelEconomy.Valid ||
		r.EngineThrottleValve_1Position_1.Valid ||
		r.EngineThrottleValve_2Position.Valid ||
		r.TransmissionDrivelineEngaged.Valid ||
		r.TransmissionTorqueConverterLockupEngaged.Valid ||
		r.TransmissionShiftInProcess.Valid ||
		r.TransmissionTorqueConverterLockupTransitionInProcess.Valid ||
		r.TransmissionOutputShaftSpeed.Valid ||
		r.PercentClutchSlip.Valid ||
		r.EngineMomentaryOverspeedEnable.Valid ||
		r.ProgressiveShiftDisable.Valid ||
		r.MomentaryEngineMaximumPowerEnable.Valid ||
		r.TransmissionInputShaftSpeed.Valid ||
		r.SourceAddressOfControllingDeviceForTransmissionControl.Valid ||
		r.EngineTorqueMode.Valid ||
		r.ActualEnginePercentTorqueFractional.Valid ||
		r.DriverSDemandEnginePercentTorque.Valid ||
		r.ActualEnginePercentTorque.Valid ||
		r.EngineSpeed.Valid ||
		r.SourceAddressOfControllingDeviceForEngineControl.Valid ||
		r.EngineStarterMode.Valid ||
		r.EngineDemandPercentTorque.Valid ||
		r.AcceleratorPedal_1LowIdleSwitch.Valid ||
		r.AcceleratorPedalKickdownSwitch.Valid ||
		r.RoadSpeedLimitStatus.Valid ||
		r.AcceleratorPedal_2LowIdleSwitch.Valid ||
		r.AcceleratorPedal_1Position.Valid ||
		r.EnginePercentLoadAtCurrentSpeed.Valid ||
		r.RemoteAcceleratorPedalPosition.Valid ||
		r.AcceleratorPedal_2Position.Valid ||
		r.VehicleAccelerationRateLimitStatus.Valid ||
		r.MomentaryEngineMaximumPowerEnableFeedback.Valid ||
		r.DpfThermalManagementActive.Valid ||
		r.ScrThermalManagementActive.Valid ||
		r.ActualMaximumAvailableEnginePercentTorque.Valid ||
		r.EstimatedPumpingPercentTorque.Valid ||
		r.TwoSpeedAxleSwitch.Valid ||
		r.ParkingBrakeSwitch.Valid ||
		r.CruiseControlPauseSwitch.Valid ||
		r.ParkBrakeReleaseInhibitRequest.Valid ||
		r.WheelBasedVehicleSpeed.Valid ||
		r.CruiseControlActive.Valid ||
		r.CruiseControlEnableSwitch.Valid ||
		r.BrakeSwitch.Valid ||
		r.ClutchSwitch.Valid ||
		r.CruiseControlSetSwitch.Valid ||
		r.CruiseControlCoastDecelerateSwitch.Valid ||
		r.CruiseControlResumeSwitch.Valid ||
		r.CruiseControlAccelerateSwitch.Valid ||
		r.CruiseControlSetSpeed.Valid ||
		r.PtoGovernorState.Valid ||
		r.CruiseControlStates.Valid ||
		r.EngineIdleIncrementSwitch.Valid ||
		r.EngineIdleDecrementSwitch.Valid ||
		r.EngineDiagnosticTestModeSwitch.Valid ||
		r.EngineShutdownOverrideSwitch.Valid ||
		r.EngineExhaustGasRecirculation_1MassFlowRate.Valid ||
		r.EngineIntakeAirMassFlowRate.Valid ||
		r.EngineExhaustGasRecirculation_2MassFlowRate.Valid ||
		r.TargetFreshAirMassFlow.Valid ||
		r.AsrEngineControlActive.Valid ||
		r.AsrBrakeControlActive.Valid ||
		r.AntiLockBrakingAbsActive.Valid ||
		r.EbsBrakeSwitch.Valid ||
		r.BrakePedalPosition.Valid ||
		r.AbsOffRoadSwitch.Valid ||
		r.AsrOffRoadSwitch.Valid ||
		r.AsrHillHolderSwitch.Valid ||
		r.TractionControlOverrideSwitch.Valid ||
		r.AcceleratorInterlockSwitch.Valid ||
		r.EngineDerateSwitch.Valid ||
		r.EngineAuxiliaryShutdownSwitch.Valid ||
		r.RemoteAcceleratorEnableSwitch.Valid ||
		r.EngineRetarderSelection.Valid ||
		r.AbsFullyOperational.Valid ||
		r.EbsRedWarningSignal.Valid ||
		r.AbsEbsAmberWarningSignalPoweredVehicle.Valid ||
		r.AtcAsrInformationSignal.Valid ||
		r.SourceAddressOfControllingDeviceForBrakeControl.Valid ||
		r.RailroadModeSwitch.Valid ||
		r.HaltBrakeSwitch.Valid ||
		r.TrailerAbsStatus.Valid ||
		r.TractorMountedTrailerAbsWarningSignal.Valid ||
		r.TransmissionClutch_1Pressure.Valid ||
		r.TransmissionOilLevel_1.Valid ||
		r.TransmissionFilterDifferentialPressure.Valid ||
		r.Transmission_1OilPressure.Valid ||
		r.Transmission_1OilTemperature_1.Valid ||
		r.TransmissionOilLevel_1HighLow.Valid ||
		r.TransmissionOilLevel_1CountdownTimer.Valid ||
		r.TransmissionOilLevel_1MeasurementStatus.Valid ||
		r.PneumaticSupplyPressure.Valid ||
		r.ParkingAndOrTrailerAirPressure.Valid ||
		r.ServiceBrakeCircuit_1AirPressure.Valid ||
		r.ServiceBrakeCircuit_2AirPressure.Valid ||
		r.AuxiliaryEquipmentSupplyPressure.Valid ||
		r.AirSuspensionSupplyPressure_1.Valid ||
		r.AirCompressorStatus.Valid ||
		r.PowertrainCircuitAirSupplyPressure.Valid ||
		r.EngineExhaust_1NOx_1.Valid ||
		r.EngineExhaust_1PercentOxygen_1.Valid ||
		r.EngineExhaust_1GasSensor_1PowerInRange.Valid ||
		r.EngineExhaust_1GasSensor_1AtTemperature.Valid ||
		r.EngineExhaust_1NOx_1ReadingStable.Valid ||
		r.EngineExhaust_1WideRangePercentOxygen_1ReadingStable.Valid ||
		r.EngineExhaust_1GasSensor_1HeaterPreliminaryFmi.Valid ||
		r.EngineExhaust_1GasSensor_1HeaterControl.Valid ||
		r.EngineExhaust_1NOxSensor_1PreliminaryFmi.Valid ||
		r.EngineExhaust_1NOxSensor_1SelfDiagnosisStatus.Valid ||
		r.EngineExhaust_1OxygenSensor_1PreliminaryFmi.Valid ||
		r.EngineTripFuelHighResolution.Valid ||
		r.EngineTotalFuelUsedHighResolution.Valid ||
		r.Aftertreatment_1ScrIntakeTemperature.Valid ||
		r.Aftertreatment_1ScrIntakeTemperaturePreliminaryFmi.Valid ||
		r.Aftertreatment_1ScrOutletTemperature.Valid ||
		r.Aftertreatment_1ScrOutletTemperaturePreliminaryFmi.Valid ||
		r.PoweredVehicleWeight.Valid ||
		r.GrossCombinationVehicleWeight.Valid ||
		r.GrossCombinationVehicleWeightConfidence.Valid ||
		r.TotalVehicleDistanceHighResolution.Valid ||
		r.TripDistanceHighResolution.Valid ||
		r.Seconds.Valid ||
		r.Minutes.Valid ||
		r.Hours.Valid ||
		r.Month.Valid ||
		r.Day.Valid ||
		r.Year.Valid ||
		r.LocalMinuteOffset.Valid ||
		r.LocalHourOffset.Valid ||
		r.BarometricPressure.Valid ||
		r.CabInteriorTemperature.Valid ||
		r.AmbientAirTemperature.Valid ||
		r.EngineIntake_1AirTemperature.Valid ||
		r.RoadSurfaceTemperature.Valid ||
		r.Aftertreatment_1DieselParticulateFilterIntakePressure.Valid ||
		r.EngineIntakeManifold_1Pressure.Valid ||
		r.EngineIntakeManifold_1Temperature.Valid ||
		r.EngineIntakeAirPressure.Valid ||
		r.EngineAirFilter_1DifferentialPressure.Valid ||
		r.EngineExhaustTemperature.Valid ||
		r.EngineCoolantFilterDifferentialPressure.Valid
}

func (r *InsertSensorDataReq) IsGpsDataValid() bool {
	return r.PositionAltitude.Valid ||
		r.PositionDirection.Valid ||
		r.PositionHdop.Valid ||
		r.PositionLatitude.Valid ||
		r.PositionLongitude.Valid ||
		r.PositionPdop.Valid ||
		r.PositionSatellites.Valid ||
		r.PositionSpeed.Valid ||
		r.PositionValid.Valid ||
		r.VehicleMileage.Valid
}

func (r *InsertSensorDataReq) IsGeneralDataValid() bool {
	return r.BatteryCurrent.Valid ||
		r.BatteryVoltage.Valid ||
		r.GsmSignalLevel.Valid ||
		r.GsmOperatorCode.Valid ||
		r.MovementStatus.Valid ||
		r.SdStatus.Valid ||
		r.ExternalPowersourceVoltage.Valid ||
		r.XAccelaration.Valid ||
		r.YAccelaration.Valid ||
		r.ZAccelaration.Valid ||
		r.PtoDriveEngagementEnum.Valid ||
		r.FuelConsumed.Valid ||
		r.DigitalInput.Valid ||
		r.DigitalOutput.Valid ||
		r.UnplugDetection.Valid
}
