package dtos

import (
	assetModel "assetfindr/internal/app/asset/models"
	"assetfindr/internal/app/geo/models"
	"assetfindr/pkg/common/commonmodel"
	"fmt"

	"time"

	"github.com/peterstace/simplefeatures/geom"
	"gopkg.in/guregu/null.v4"
)

type TrackingListReq struct {
	commonmodel.ListRequest
	ID         string `form:"id"`
	TargetCode string `form:"target_code"`
	Start      string `form:"start"`
	End        string `form:"end"`
}

type GetLatestKMFromTracking struct {
	VehicleKM int `json:"vehicle_km"`
}

type GetGeoFence struct {
	ID                            string              `json:"id"`
	References                    []GeoFenceReference `json:"references,omitempty"`
	Coordinates                   GeoArea             `json:"coordinates"`
	Name                          string              `json:"name"`
	TypeCode                      string              `json:"type_code"`
	Type                          models.GeoFenceType `json:"type"`
	Description                   string              `json:"description"`
	Color                         string              `json:"color"`
	Height                        float64             `json:"height"`
	Perimeter                     float64             `json:"perimeter"`
	Area                          float64             `json:"area"`
	CreatedAt                     time.Time           `json:"created_at"`
	UpdateAt                      time.Time           `json:"updated_at"`
	AlertRecipientUserIDs         []string            `json:"alert_recipient_user_ids"`
	AlertMediaTargets             []string            `json:"alert_media_targets"`
	NotifyAlertToAllAssetAssignee null.Bool           `json:"notify_alert_to_all_asset_assignee"`
}

func (g *GetGeoFence) Set(geoFence models.GeoFence, references []models.GeoFenceReference, assetMap map[string]assetModel.Asset, mapImei map[string]string) {
	g.ID = geoFence.ID
	g.Name = geoFence.Name
	g.TypeCode = geoFence.TypeCode
	g.Type = geoFence.Type
	g.Description = geoFence.Description
	g.Color = geoFence.Color
	g.Height = geoFence.Height
	g.Perimeter = geoFence.Perimeter
	g.Area = geoFence.Area
	polygon, _ := geom.UnmarshalWKT(geoFence.Coordinates)
	geom := &commonmodel.GeoArea{Polygon: polygon.MustAsPolygon()}
	coord := geom.Coordinates()
	var geoArea GeoArea
	for i := 0; i < coord[0].Length(); i++ {
		point := coord[0].GetXY(i)
		geoArea = append(geoArea, commonmodel.GeoPoint{Lat: point.Y, Long: point.X})
	}
	g.Coordinates = geoArea
	g.CreatedAt = geoFence.CreatedAt
	g.UpdateAt = geoFence.UpdatedAt
	if len(references) > 0 {
		reference := make([]GeoFenceReference, 0)
		for _, ref := range references {
			var r GeoFenceReference
			r.Set(ref, assetMap[ref.AssetID], mapImei[ref.AssetID])
			reference = append(reference, r)
		}
		g.References = reference
	}
	g.AlertRecipientUserIDs = geoFence.AlertRecipientUserIDs
	g.AlertMediaTargets = geoFence.AlertMediaTargets
	g.NotifyAlertToAllAssetAssignee = geoFence.NotifyAlertToAllAssetAssignee
}

type GetLatestTrackingPosition struct {
	VehicleKM    null.Int   `json:"vehicle_km"`
	LatestUpdate time.Time  `json:"latest_update"`
	Time         time.Time  `json:"time"`
	Speed        null.Int   `json:"speed"`
	Lat          null.Float `json:"lat"`
	Long         null.Float `json:"long"`
}

type CreateGeoFence struct {
	ID                            string              `json:"id"`
	References                    []GeoFenceReference `json:"references"`
	Coordinates                   GeoArea             `json:"coordinates"`
	Name                          string              `json:"name"`
	TypeCode                      string              `json:"type_code"`
	Description                   string              `json:"description"`
	Color                         string              `json:"color"`
	Height                        float64             `json:"height"`
	Perimeter                     float64             `json:"perimeter"`
	Area                          float64             `json:"area"`
	AlertRecipientUserIDs         []string            `json:"alert_recipient_user_ids"`
	AlertMediaTargets             []string            `json:"alert_media_targets"`
	NotifyAlertToAllAssetAssignee null.Bool           `json:"notify_alert_to_all_asset_assignee"`
}

type GeoFenceReference struct {
	ID                string `json:"id"`
	AssetID           string `json:"asset_id" binding:"required"`
	IsDeleted         bool   `json:"is_deleted" binding:"required"`
	Label             string `json:"label"`
	IMEI              string `json:"imei"`
	AssetCategoryCode string `json:"asset_category_code"`
}

func (g *GeoFenceReference) Set(geoFenceReference models.GeoFenceReference, asset assetModel.Asset, imei string) {
	g.ID = geoFenceReference.ID
	g.AssetID = geoFenceReference.AssetID
	g.IsDeleted = false
	g.Label = asset.ReferenceNumber
	g.AssetCategoryCode = asset.AssetCategoryCode
	if g.Label == "" {
		g.Label = asset.SerialNumber
	}
	g.IMEI = imei
}

type GeoArea []commonmodel.GeoPoint

func (a GeoArea) Validate() error {
	if len(a) <= 2 {
		return fmt.Errorf("area should have minimum 3 point")
	}

	if a[0].Lat != a[len(a)-1].Lat || a[0].Long != a[len(a)-1].Long {
		return fmt.Errorf("start and end point must be the same")
	}

	return nil
}

type GeoFenceListReq struct {
	commonmodel.ListRequest
	ID       string `form:"id"`
	TypeCode string `form:"status_code"`
}

type LatestListReq struct {
	Limit int `form:"limit"`
}

func (r *LatestListReq) Normalize() {
	if r.Limit <= 0 {
		r.Limit = 500
	}
}
