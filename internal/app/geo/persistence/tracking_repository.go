package persistence

import (
	"assetfindr/cmd/flespimqttservice/model"
	"assetfindr/internal/app/geo/models"
	"assetfindr/internal/app/geo/repository"
	"assetfindr/internal/constants"
	"assetfindr/internal/errorhandler"
	"assetfindr/internal/infrastructure/bq"
	"assetfindr/internal/infrastructure/database"
	"assetfindr/pkg/common/commonlogger"
	"context"
	"fmt"
	"os"
	"strings"
	"time"

	"google.golang.org/api/iterator"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type trackingRepository struct{}

func NewTrackingRepository() repository.TrackingRepository {
	return &trackingRepository{}
}

func (r *trackingRepository) GetTrackings(ctx context.Context, dB database.DBI, cond models.TrackingCondition) ([]models.Tracking, error) {
	trackings := []models.Tracking{}
	query := dB.GetOrm().Model(&trackings)

	enrichTrackingQueryWithWhere(query, cond.Where)
	enrichTrackingQueryWithPreload(query, cond.Preload)

	if len(cond.Columns) > 0 {
		query.Select(cond.Columns)
	}

	query.Order("geo_location_histories.time ASC")

	err := query.Find(&trackings).Error
	if err != nil {
		return nil, err
	}

	return trackings, nil
}

func (r *trackingRepository) GetLatestVehicleKM(ctx context.Context, dB database.DBI, assetID, clientID string) (*models.Tracking, error) {
	tracking := models.Tracking{}
	query := dB.GetOrm().
		Model(&tracking).
		Preload("Target").
		Where("client_id = ?", clientID).
		Where("asset_id = ?", assetID).
		Limit(1).
		Order("time DESC")
	err := query.Find(&tracking).Error
	if err != nil {
		return nil, err
	}

	return &tracking, nil
}

func (r *trackingRepository) GetLatestVehicleKMFromCanBus(ctx context.Context, dB database.DBI, assetID, clientID string) (*models.CanBusSensorData, error) {
	canBusData := models.CanBusSensorData{}
	query := dB.GetOrm().
		Model(&canBusData).
		Where("client_id = ?", clientID).
		Where("asset_id = ?", assetID).
		Where("can_vehicle_mileage IS NOT NULL").
		Limit(1).
		Order("time DESC")
	err := query.Find(&canBusData).Error
	if err != nil {
		return nil, err
	}

	return &canBusData, nil
}

func (r *trackingRepository) GetLatestTrackingPosition(ctx context.Context, dB database.DBI, assetID, clientID string) (*models.Tracking, error) {
	tracking := models.Tracking{}
	query := dB.GetOrm().
		Model(&tracking).
		Preload("Target").
		Where("asset_id = ?", assetID).
		Limit(1).
		Order("time DESC")

	if clientID != "" {
		query.Where("client_id = ?", clientID)
	}
	err := query.Find(&tracking).Error
	if err != nil {
		return nil, err
	}

	return &tracking, nil
}

func (r *trackingRepository) GetLatestTrackingPositionList(ctx context.Context, dB database.DBI, assetID, clientID string, limit int) ([]models.Tracking, error) {
	trackings := []models.Tracking{}
	query := dB.GetOrm().
		Model(&models.Tracking{}).
		Preload("Target").
		Where("asset_id = ?", assetID).
		Limit(limit).
		Order("time DESC")

	if clientID != "" {
		query.Where("client_id = ?", clientID)
	}
	err := query.Find(&trackings).Error
	if err != nil {
		return nil, err
	}

	return trackings, nil
}

func enrichTrackingQueryWithPreload(query *gorm.DB, preload models.TrackingPreload) {
	if preload.Target {
		query.Preload("Target")
	}
}

func enrichTrackingQueryWithWhere(query *gorm.DB, where models.TrackingWhere) {
	if where.ID != "" {
		query.Where("geo_location_histories.id = ?", where.ID)
	}
	if where.ClientID != "" {
		query.Where("geo_location_histories.client_id = ?", where.ClientID)
	}
	if where.TargetCode != "" {
		query.Where("geo_location_histories.target_code = ?", where.TargetCode)
	}
	if where.AssetID != "" {
		query.Where("geo_location_histories.asset_id = ?", where.AssetID)
	}

	if len(where.AssetIDs) > 0 {
		query.Where("geo_location_histories.asset_id IN ?", where.AssetIDs)
	}
	if !where.Start.IsZero() {
		query.Where("geo_location_histories.time >= ?", where.Start)
	}
	if !where.End.IsZero() {
		query.Where("geo_location_histories.time <= ?", where.End)
	}

	if where.NotNullLatLong {
		query.Where("geo_location_histories.lat IS NOT NULL AND geo_location_histories.lat IS NOT NULL")
	}
}

func (r *trackingRepository) CreateTrackings(ctx context.Context, dB database.DBI, trackings []models.Tracking) error {
	return dB.GetTx().Create(&trackings).Error
}

func (r *trackingRepository) CreatePreGPSVehicleMeter(ctx context.Context, dB database.DBI, preGPSVehicleMeter *models.PreGPSVehicleMeter) error {
	return dB.GetTx().Create(preGPSVehicleMeter).Error
}

func enrichPreGPSVehicleMeterQueryWithWhere(query *gorm.DB, where models.PreGPSVehicleMeterWhere) {
	if where.ID != "" {
		query.Where("id = ?", where.ID)
	} // ID

	if where.IntegrationID != "" {
		query.Where("integration_id = ?", where.IntegrationID)
	} // IntegrationID

	if where.ClientID != "" {
		query.Where("client_id = ?", where.ClientID)
	} // ClientID

	if where.WithOrmDeleted {
		query.Unscoped()
	}
}

func enrichPreGPSVehicleMeterQueryWithPreload(query *gorm.DB, preload models.PreGPSVehicleMeterPreload) {

}

func (r *trackingRepository) GetPreGPSVehicleMeter(ctx context.Context, dB database.DBI, cond models.PreGPSVehicleMeterCondition) (*models.PreGPSVehicleMeter, error) {
	location := models.PreGPSVehicleMeter{}
	query := dB.GetOrm().Model(&location)

	enrichPreGPSVehicleMeterQueryWithWhere(query, cond.Where)
	enrichPreGPSVehicleMeterQueryWithPreload(query, cond.Preload)

	if len(cond.Columns) > 0 {
		query.Select(cond.Columns)
	}

	err := query.First(&location).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errorhandler.ErrDataNotFound("PRE_GPS_VEHICLE_METER")
		}

		return nil, err
	}

	return &location, nil
}

func (r *trackingRepository) UpdatePreGPSVehicleMeter(ctx context.Context, dB database.DBI, id string, data *models.PreGPSVehicleMeter) error {
	return dB.GetTx().
		Model(&models.PreGPSVehicleMeter{}).
		Where("id = ?", id).
		Updates(data).
		Error
}

func (r *trackingRepository) CreateGeoFence(ctx context.Context, dB database.DBI, geoFence *models.GeoFence) error {
	return dB.GetTx().Create(geoFence).Error
}

func (r *trackingRepository) UpdateGeoFence(ctx context.Context, dB database.DBI, id string, geoFence *models.GeoFence) error {
	return dB.GetTx().
		Model(&models.GeoFence{}).
		Where("id = ?", id).
		Updates(geoFence).Error
}

func (r *trackingRepository) GetGeoFenceByAssetID(ctx context.Context, dB database.DBI, assetID string, clientID string) (*models.GeoFence, error) {
	tracking := models.GeoFence{}
	query := dB.GetOrm().
		Model(&tracking).
		Select("*", "ST_AsText(coordinates) AS coordinates").
		Joins(`INNER JOIN geo_geofence_references gfr ON gfr.geofence_id = geo_geofences.id AND gfr.asset_id = ? AND gfr.deleted_at IS NULL`, assetID)

	if clientID != "" {
		query.Where("geo_geofences.client_id = ?", clientID)
	}
	err := query.First(&tracking).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errorhandler.ErrDataNotFound("geofence")
		}
		return nil, err
	}

	return &tracking, nil
}

func (r *trackingRepository) GetGeoFencesByAssetID(ctx context.Context, dB database.DBI, assetID string) ([]models.GeoFence, error) {
	trackings := []models.GeoFence{}
	query := dB.GetOrm().
		Model(&trackings).
		Select("geo_geofences.*", "ST_AsText(coordinates) AS coordinates").
		Joins(`INNER JOIN geo_geofence_references gfr ON gfr.geofence_id = geo_geofences.id AND gfr.asset_id = ? AND gfr.deleted_at IS NULL`, assetID)

	err := query.Find(&trackings).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errorhandler.ErrDataNotFound("geofence")
		}
		return nil, err
	}

	return trackings, nil
}
func (r *trackingRepository) GetAllGeoFences(ctx context.Context, dB database.DBI) ([]models.GeoFence, error) {
	geoFences := []models.GeoFence{}
	query := dB.GetOrm().
		Model(&geoFences).
		Select("*", "ST_AsText(coordinates) AS coordinates").
		Preload("References")

	err := query.Find(&geoFences).Error
	if err != nil {
		return nil, err
	}

	return geoFences, nil
}
func (r *trackingRepository) GetGeoFenceByID(ctx context.Context, dB database.DBI, id string, clientID string) (*models.GeoFence, error) {
	tracking := models.GeoFence{}
	query := dB.GetOrm().
		Model(&tracking).
		Select("*", "ST_AsText(coordinates) AS coordinates").
		Where("id = ? AND client_id = ?", id, clientID).
		Preload("Type")
	err := query.First(&tracking).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errorhandler.ErrDataNotFound("geofence")
		}
		return nil, err
	}

	return &tracking, nil
}

func (r *trackingRepository) GetGeoFenceByAssetIDs(ctx context.Context, dB database.DBI, assetIDs []string, id string, clientID string) (*models.GeoFence, error) {
	tracking := models.GeoFence{}
	query := dB.GetTx().
		Model(&tracking).
		Select("geo_geofences.*", "ST_AsText(coordinates) AS coordinates").
		Joins(`INNER JOIN geo_geofence_references gfr ON gfr.geofence_id = geo_geofences.id AND gfr.asset_id IN ? AND gfr.deleted_at IS NULL`, assetIDs).
		Where("geo_geofences.client_id = ? AND geo_geofences.id = ?", clientID, id)
	err := query.First(&tracking).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errorhandler.ErrDataNotFound("geofence")
		}
		return nil, err
	}

	return &tracking, nil
}

func (r *trackingRepository) DeleteGeoFenceReferences(ctx context.Context, dB database.DBI, ids []string) error {
	return dB.GetTx().Delete(&models.GeoFenceReference{}, ids).Error
}

func (r *trackingRepository) DeleteGeoFenceReferenceByGeoFenceID(ctx context.Context, dB database.DBI, id string) error {
	return dB.GetTx().Where("geofence_id = ?", id).Delete(&models.GeoFenceReference{}).Error
}

func (r *trackingRepository) DeleteGeoFence(ctx context.Context, dB database.DBI, id string) error {
	return dB.GetTx().Where("id = ?", id).Delete(&models.GeoFence{}).Error
}

func (r *trackingRepository) UpdateGeoFenceReference(ctx context.Context, dB database.DBI, id string, geoFenceReference *models.GeoFenceReference) error {
	return dB.GetTx().
		Model(&models.GeoFenceReference{}).
		Where("id = ?", id).
		Updates(geoFenceReference).
		Error
}

func (r *trackingRepository) CreateGeoFenceReferences(ctx context.Context, dB database.DBI, geoFenceReferences []models.GeoFenceReference) error {
	return dB.GetTx().Create(&geoFenceReferences).Error
}

func enrichGeoFenceQueryWithPreload(query *gorm.DB, preload models.GeoFencePreload) {
	if preload.Type {
		query.Preload("Type")
	}
}

func enrichGeoFenceQueryWithWhere(query *gorm.DB, where models.GeoFenceWhere) {
	if len(where.IDs) > 0 {
		query.Where("id IN ?", where.IDs)
	}
	if where.ID != "" {
		query.Where("id = ?", where.ID)
	} // ID

	if where.ClientID != "" {
		query.Where("client_id = ?", where.ClientID)
	} // ClientID

	if where.TypeCode != "" {
		query.Where("type_code = ?", where.TypeCode)
	} // TypeCode
}

func (r *trackingRepository) GetGeoFence(ctx context.Context, dB database.DBI, cond models.GeoFenceCondition) (*models.GeoFence, error) {
	geofence := models.GeoFence{}
	query := dB.GetOrm().Model(&geofence).Select("*", "ST_AsText(coordinates) AS coordinates")

	enrichGeoFenceQueryWithWhere(query, cond.Where)
	enrichGeoFenceQueryWithPreload(query, cond.Preload)

	if len(cond.Columns) > 0 {
		query.Select(cond.Columns)
	}

	if cond.IsForUpdate {
		query.Clauses(clause.Locking{Strength: "UPDATE"})
	}

	err := query.First(&geofence).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errorhandler.ErrDataNotFound("geofence")
		}

		return nil, err
	}

	return &geofence, nil
}

func (r *trackingRepository) GetGeoFences(ctx context.Context, dB database.DBI, cond models.GeoFenceCondition) ([]models.GeoFence, error) {
	geofences := []models.GeoFence{}
	query := dB.GetOrm().Model(&geofences).Select("*", "ST_AsText(coordinates) AS coordinates")

	enrichGeoFenceQueryWithWhere(query, cond.Where)
	enrichGeoFenceQueryWithPreload(query, cond.Preload)

	if len(cond.Columns) > 0 {
		query.Select(cond.Columns)
	}

	if cond.IsForUpdate {
		query.Clauses(clause.Locking{Strength: "UPDATE"})
	}

	err := query.Find(&geofences).Error
	if err != nil {
		return nil, err
	}

	return geofences, nil
}

func (r *trackingRepository) GetGeoFenceList(ctx context.Context, dB database.DBI, param models.GetGeoFenceListParam) (int, []models.GeoFence, error) {
	var totalRecords int64
	geofences := []models.GeoFence{}
	query := dB.GetTx().Model(&geofences).Select("*", "ST_AsText(coordinates) AS coordinates")
	enrichGeoFenceQueryWithWhere(query, param.Cond.Where)
	enrichGeoFenceQueryWithPreload(query, param.Cond.Preload)

	if param.SearchKeyword != "" {
		query.Where("LOWER(name) LIKE LOWER(?)", "%"+param.SearchKeyword+"%")
	}

	err := query.Count(&totalRecords).Error
	if err != nil {
		return 0, nil, err
	}

	if totalRecords <= 0 {
		return int(totalRecords), nil, nil
	}

	offset := (param.PageNo - 1) * param.PageSize
	err = query.Offset(offset).Limit(param.PageSize).Find(&geofences).Error
	if err != nil {
		return 0, nil, err
	}

	return int(totalRecords), geofences, nil

}

func enrichGeoFenceReferenceQueryWithPreload(query *gorm.DB, preload models.GeoFenceReferencePreload) {
	if preload.GeoFence {
		query.Preload("GeoFence")
	}
}

func enrichGeoFenceReferenceQueryWithWhere(query *gorm.DB, where models.GeoFenceReferenceWhere) {
	if len(where.IDs) > 0 {
		query.Where("id IN ?", where.IDs)
	}
	if where.ID != "" {
		query.Where("id = ?", where.ID)
	}
	if where.GeoFenceID != "" {
		query.Where("geofence_id = ?", where.GeoFenceID)
	}
	if len(where.GeoFenceIDs) > 0 {
		query.Where("geofence_id IN ?", where.GeoFenceIDs)
	}
	if where.ClientID != "" {
		query.Where("client_id = ?", where.ClientID)
	}
	if len(where.AssetIDs) > 0 {
		query.Where("asset_id IN ?", where.IDs)
	}
	if where.AssetID != "" {
		query.Where("asset_id = ?", where.ID)
	}
}

func (r *trackingRepository) GetGeoFenceReference(ctx context.Context, dB database.DBI, cond models.GeoFenceReferenceCondition) (*models.GeoFenceReference, error) {
	geofenceReference := models.GeoFenceReference{}
	query := dB.GetOrm().Model(&geofenceReference)

	enrichGeoFenceReferenceQueryWithWhere(query, cond.Where)
	enrichGeoFenceReferenceQueryWithPreload(query, cond.Preload)

	if len(cond.Columns) > 0 {
		query.Select(cond.Columns)
	}

	if cond.IsForUpdate {
		query.Clauses(clause.Locking{Strength: "UPDATE"})
	}

	err := query.First(&geofenceReference).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errorhandler.ErrDataNotFound("geofence reference")
		}

		return nil, err
	}

	return &geofenceReference, nil
}

func (r *trackingRepository) GetGeoFenceReferences(ctx context.Context, dB database.DBI, cond models.GeoFenceReferenceCondition) ([]models.GeoFenceReference, error) {
	geofenceReferences := []models.GeoFenceReference{}
	query := dB.GetOrm().Model(&geofenceReferences)

	enrichGeoFenceReferenceQueryWithWhere(query, cond.Where)
	enrichGeoFenceReferenceQueryWithPreload(query, cond.Preload)

	if len(cond.Columns) > 0 {
		query.Select(cond.Columns)
	}

	if cond.IsForUpdate {
		query.Clauses(clause.Locking{Strength: "UPDATE"})
	}

	err := query.Find(&geofenceReferences).Error
	if err != nil {
		return nil, err
	}

	return geofenceReferences, nil
}

func (r *trackingRepository) GetGeofenceInOutHistoryList(ctx context.Context, dB database.DBI, param models.GetGeofenceInOutHistoryListParam) (string, []models.GeofenceInOutHistory, error) {
	var totalRecords int64
	GeofenceInOutHistorys := []models.GeofenceInOutHistory{}
	query := dB.GetTx().Model(&GeofenceInOutHistorys)

	enrichGeofenceInOutHistoryQueryWithWhere(query, param.Cond.Where)
	enrichGeofenceInOutHistoryQueryWithPreload(query, param.Cond.Preload)

	err := query.Count(&totalRecords).Error
	if err != nil {
		return "", nil, err
	}

	if totalRecords <= 0 {
		return "", GeofenceInOutHistorys, nil
	}

	if param.NextCursor != "" {
		query.Where("geo_geofence_in_out_history.created_at < ?", param.NextCursor)
	}

	query.Order("geo_geofence_in_out_history.created_at DESC")

	err = query.Limit(param.PageSize).Find(&GeofenceInOutHistorys).Error
	if err != nil {
		return "", nil, err
	}

	if len(GeofenceInOutHistorys) > 0 {
		nextCursor := GeofenceInOutHistorys[len(GeofenceInOutHistorys)-1].Time.Format(time.RFC3339)
		return strings.Replace(nextCursor, "+", "%2b", 1), GeofenceInOutHistorys, nil
	}

	return "", GeofenceInOutHistorys, nil
}

func (r *trackingRepository) CreateGeofenceInOutHistory(ctx context.Context, dB database.DBI, GeofenceInOutHistory *models.GeofenceInOutHistory) error {
	return dB.GetTx().Create(GeofenceInOutHistory).Error
}

func enrichGeofenceInOutHistoryQueryWithWhere(query *gorm.DB, where models.GeofenceInOutHistoryWhere) {
	if where.AssetID != "" {
		query.Where("asset_id = ?", where.AssetID)
	} // AssetID

	if where.ClientID != "" {
		query.Where("client_id = ?", where.ClientID)
	} // AssetID

	if where.IsLatest {
		query.Order("created_at DESC")
	} // IsLatest

	if where.StartDate != "" {
		query.Where("created_at >= ?", where.StartDate)
	} // StartDate

	if where.EndDate != "" {
		query.Where("created_at < ?", where.EndDate)
	} // EndDate

	if where.GeofenceID != "" {
		query.Where("geofence_id = ?", where.GeofenceID)
	} // GeofenceID
}

func enrichGeofenceInOutHistoryQueryWithPreload(query *gorm.DB, preload models.GeofenceInOutHistoryPreload) {
}

func (r *trackingRepository) GetGeofenceInOutHistory(ctx context.Context, dB database.DBI, condition models.GeofenceInOutHistoryCondition) (*models.GeofenceInOutHistory, error) {
	GeofenceInOutHistory := &models.GeofenceInOutHistory{}
	query := dB.GetOrm().
		Model(&models.GeofenceInOutHistory{})

	enrichGeofenceInOutHistoryQueryWithWhere(query, condition.Where)
	enrichGeofenceInOutHistoryQueryWithPreload(query, condition.Preload)

	err := query.First(GeofenceInOutHistory).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errorhandler.ErrDataNotFound("GEO_GEOFENCE_IN_OUT_HISTORY")
		}

		return nil, err
	}

	return GeofenceInOutHistory, nil
}

func (r *trackingRepository) GetGeofenceInOutHistorys(ctx context.Context, dB database.DBI, condition models.GeofenceInOutHistoryCondition) ([]models.GeofenceInOutHistory, error) {
	geofenceInOutHistorys := []models.GeofenceInOutHistory{}
	query := dB.GetOrm().Model(&models.GeofenceInOutHistory{})

	enrichGeofenceInOutHistoryQueryWithWhere(query, condition.Where)
	enrichGeofenceInOutHistoryQueryWithPreload(query, condition.Preload)

	err := query.Find(&geofenceInOutHistorys).Error
	if err != nil {
		return nil, err
	}

	return geofenceInOutHistorys, nil
}

func (r *trackingRepository) CreateGeoRouteFence(ctx context.Context, dB database.DBI, geoRouteFence *models.GeoRouteFence) error {
	return dB.GetTx().Create(geoRouteFence).Error
}

func (r *trackingRepository) GetGeoRouteFence(ctx context.Context, dB database.DBI) (*models.GeoRouteFence, error) {
	geoROuteFence := models.GeoRouteFence{}
	query := dB.GetTx().
		Model(&geoROuteFence).
		Select("*", "ST_AsText(route) AS route")
	err := query.First(&geoROuteFence).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errorhandler.ErrDataNotFound("geo route fence")
		}
		return nil, err
	}

	return &geoROuteFence, nil
}

func (r *trackingRepository) CreateCanBusSensor(ctx context.Context, dB database.DBI, canBusData *models.CanBusSensorData) error {
	return dB.GetTx().Create(canBusData).Error
}

func (r *trackingRepository) GetLatestCanBusData(ctx context.Context, dB database.DBI, assetID, clientID string) (*models.CanBusSensorData, error) {
	canBusSensorData := models.CanBusSensorData{}
	query := dB.GetOrm().
		Model(&canBusSensorData).
		Where("asset_id = ?", assetID).
		Limit(1).
		Order("time DESC")

	if clientID != "" {
		query.Where("client_id = ?", clientID)
	}
	err := query.Find(&canBusSensorData).Error
	if err != nil {
		return nil, err
	}

	return &canBusSensorData, nil
}

func (r *trackingRepository) GetLatestCompressorData(ctx context.Context, dB database.DBI, assetID, clientID string) (*models.CompressorSensorData, error) {
	canBusSensorData := models.CompressorSensorData{}
	query := dB.GetOrm().
		Model(&canBusSensorData).
		Where("asset_id = ?", assetID).
		Limit(1).
		Order("time DESC")

	if clientID != "" {
		query.Where("client_id = ?", clientID)
	}
	err := query.Find(&canBusSensorData).Error
	if err != nil {
		return nil, err
	}

	return &canBusSensorData, nil
}

func (r *trackingRepository) CreateCompressorSensor(ctx context.Context, dB database.DBI, compressorData *models.CompressorSensorData) error {
	return dB.GetTx().Create(compressorData).Error
}

func (r *trackingRepository) CreateCompressorSensorDataLake(ctx context.Context, bQ bq.BQI, compressorData *models.CompressorSensorData) error {
	tableName := "compressor_sensor_data"
	if os.Getenv(constants.ENV_APP_ENV) == "local" {
		tableName = "testing_local_rizqo_compressor_sensor_data"
	}
	inserter := bQ.GetConn().Dataset("sensor_data").Table(tableName).Inserter()
	items := []*models.CompressorSensorData{
		compressorData,
	}
	var err error
	for attempt := 0; attempt < 5; attempt++ {
		err = inserter.Put(context.Background(), items)
		if err == nil {
			return nil
		}

		time.Sleep(time.Duration(attempt) * time.Second)
	}

	commonlogger.Errorf("failed to insert compressor data err: %v", err)

	return err
}

func (r *trackingRepository) CreateGeneralSensor(ctx context.Context, dB database.DBI, generalSensorData *models.GeneralSensor) error {
	return dB.GetTx().Create(generalSensorData).Error
}

func (r *trackingRepository) CreateGeneralSensorDataLake(ctx context.Context, bQ bq.BQI, generalSensorData *models.GeneralSensor) error {
	tableName := "general_sensor_data"
	if os.Getenv(constants.ENV_APP_ENV) == "local" {
		tableName = "testing_local_rizqo_general_sensor_data"
	}
	inserter := bQ.GetConn().Dataset("sensor_data").Table(tableName).Inserter()
	items := []*models.GeneralSensor{
		generalSensorData,
	}
	var err error
	for attempt := 0; attempt < 5; attempt++ {
		err = inserter.Put(context.Background(), items)
		if err == nil {
			return nil
		}

		time.Sleep(time.Duration(attempt) * time.Second)
	}

	commonlogger.Errorf("failed to insert general sensor data err: %v", err)

	return err
}

func (r *trackingRepository) GetLatestGeneralSensorDataLakeByDistinctIDs(ctx context.Context, bQ bq.BQI) ([]models.GeneralSensorBQ, error) {
	datasetName := "sensor_data"
	tableName := "general_sensor_data"
	if os.Getenv(constants.ENV_APP_ENV) == "local" {
		tableName = "testing_local_rizqo_general_sensor_data"
	}

	query := fmt.Sprintf("SELECT AS VALUE ARRAY_AGG(t ORDER BY time DESC LIMIT 1)[OFFSET(0)] FROM `%s.%s` t WHERE integration_id IS NOT NULL AND integration_id != "+`""`+" GROUP BY integration_id", datasetName, tableName)

	iter, err := bQ.GetConn().Query(query).Read(ctx)
	if err != nil {
		commonlogger.Errorf("failed to read general sensor data err: %v", err)
		return nil, err
	}

	var results []models.GeneralSensorBQ
	for {
		row := models.GeneralSensorBQ{}
		err := iter.Next(&row)
		if err == iterator.Done {
			break
		}
		if err != nil {
			return results, err
		}

		results = append(results, row)
	}

	return results, err
}

func (r *trackingRepository) GetLatestGeneralData(ctx context.Context, dB database.DBI, assetID, clientID string) (*models.GeneralSensor, error) {
	generalSensorData := models.GeneralSensor{}
	query := dB.GetOrm().
		Model(&generalSensorData).
		Where("asset_id = ?", assetID).
		Limit(1).
		Order("time DESC")

	if clientID != "" {
		query.Where("client_id = ?", clientID)
	}
	err := query.Find(&generalSensorData).Error
	if err != nil {
		return nil, err
	}

	return &generalSensorData, nil
}

func (r *trackingRepository) GetLatestCanBusDataList(ctx context.Context, dB database.DBI, assetID, clientID string, limit int) ([]models.CanBusSensorData, error) {
	canBusSensorData := []models.CanBusSensorData{}
	query := dB.GetOrm().
		Model(&models.CanBusSensorData{}).
		Where("asset_id = ?", assetID).
		Limit(limit).
		Order("time DESC")

	if clientID != "" {
		query.Where("client_id = ?", clientID)
	}
	err := query.Find(&canBusSensorData).Error
	if err != nil {
		return nil, err
	}

	return canBusSensorData, nil
}
func (r *trackingRepository) GetLatestCompressorDataList(ctx context.Context, dB database.DBI, assetID, clientID string, limit int) ([]models.CompressorSensorData, error) {
	canBusSensorData := []models.CompressorSensorData{}
	query := dB.GetOrm().
		Model(&models.CompressorSensorData{}).
		Where("asset_id = ?", assetID).
		Limit(limit).
		Order("time DESC")

	if clientID != "" {
		query.Where("client_id = ?", clientID)
	}
	err := query.Find(&canBusSensorData).Error
	if err != nil {
		return nil, err
	}

	return canBusSensorData, nil
}
func (r *trackingRepository) GetLatestGeneralDataList(ctx context.Context, dB database.DBI, assetID, clientID string, limit int) ([]models.GeneralSensor, error) {
	generalSensorData := []models.GeneralSensor{}
	query := dB.GetOrm().
		Model(&models.GeneralSensor{}).
		Where("asset_id = ?", assetID).
		Limit(limit).
		Order("time DESC")

	if clientID != "" {
		query.Where("client_id = ?", clientID)
	}
	err := query.Find(&generalSensorData).Error
	if err != nil {
		return nil, err
	}

	return generalSensorData, nil
}

func (r *trackingRepository) CreateLogRawSensorDataLake(ctx context.Context, bQ bq.BQI, logRawSensorData *models.LogRawSensorData) error {
	tableName := "log_raw_sensor_data"
	if os.Getenv(constants.ENV_APP_ENV) == "local" {
		tableName = "testing_local_rizqo_log_raw_sensor_data"
	}
	inserter := bQ.GetConn().Dataset("sensor_data").Table(tableName).Inserter()
	items := []*models.LogRawSensorData{
		logRawSensorData,
	}
	var err error
	for attempt := 0; attempt < 5; attempt++ {
		err = inserter.Put(context.Background(), items)
		if err == nil {
			return nil
		}

		time.Sleep(time.Duration(attempt) * time.Second)
	}

	commonlogger.Errorf("failed to insert loq raw sensor data err: %v", err)

	return err
}

func (r *trackingRepository) CreateLogRawSensorDatasLake(ctx context.Context, bQ bq.BQI, logRawSensorDatas []*models.LogRawSensorData) error {
	tableName := "log_raw_sensor_data"
	if os.Getenv(constants.ENV_APP_ENV) == "local" {
		tableName = "testing_local_rizqo_log_raw_sensor_data"
	}
	inserter := bQ.GetConn().Dataset("sensor_data").Table(tableName).Inserter()

	var err error
	for attempt := 0; attempt < 5; attempt++ {
		err = inserter.Put(context.Background(), logRawSensorDatas)
		if err == nil {
			return nil
		}

		time.Sleep(time.Duration(attempt) * time.Second)
	}

	commonlogger.Errorf("failed to insert loq raw sensor data err: %v", err)

	return err
}

func (r *trackingRepository) CreateGpsSensorDataLake(ctx context.Context, bQ bq.BQI, gpsSensorData *models.GpsSensor) error {
	tableName := "gps_sensor_data_v2"
	if os.Getenv(constants.ENV_APP_ENV) == "local" {
		tableName = "testing_local_rizqo_gps_sensor_data_v2"
	}
	inserter := bQ.GetConn().Dataset("sensor_data").Table(tableName).Inserter()
	items := []*models.GpsSensor{
		gpsSensorData,
	}
	var err error
	for attempt := 0; attempt < 5; attempt++ {
		err = inserter.Put(context.Background(), items)
		if err == nil {
			return nil
		}

		time.Sleep(time.Duration(attempt) * time.Second)
	}

	commonlogger.Errorf("failed to insert general sensor data err: %v", err)

	return err
}

func (r *trackingRepository) CreateCanBusSensorDataLake(ctx context.Context, bQ bq.BQI, canBusSensorData *models.CanBusSensorData) error {
	tableName := "can_bus_sensor_data_v2"
	if os.Getenv(constants.ENV_APP_ENV) == "local" {
		tableName = "testing_local_rizqo_can_bus_sensor_data_v2"
	}

	inserter := bQ.GetConn().Dataset("sensor_data").Table(tableName).Inserter()
	items := []*models.CanBusSensorData{
		canBusSensorData,
	}
	var err error
	for attempt := 0; attempt < 5; attempt++ {
		err = inserter.Put(context.Background(), items)
		if err == nil {
			return nil
		}

		time.Sleep(time.Duration(attempt) * time.Second)
	}

	commonlogger.Errorf("failed to insert can bus sensor data err: %v", err)

	return err
}

func (r *trackingRepository) CreateFlespiSensorDataLake(ctx context.Context, bQ bq.BQI, flespiSensorData *model.FlespiData) error {
	tableName := "flespi_data_stream"
	if os.Getenv(constants.ENV_APP_ENV) == "local" {
		tableName = "testing_local_rizqo"
	}
	inserter := bQ.GetConn().Dataset("logs").Table(tableName).Inserter()
	items := []*model.FlespiData{
		flespiSensorData,
	}
	var err error
	for attempt := 0; attempt < 5; attempt++ {
		err = inserter.Put(context.Background(), items)
		if err == nil {
			return nil
		}

		time.Sleep(time.Duration(attempt) * time.Second)
	}

	commonlogger.Errorf("failed to insert into bigquery after retry, last err: %v", err)
	return err
}

func (r *trackingRepository) CreateTyreSensor(ctx context.Context, dB database.DBI, tyreSensorData *models.TyreSensor) error {
	return dB.GetTx().Create(tyreSensorData).Error
}

func (r *trackingRepository) CreateTyreSensorDataLake(ctx context.Context, bQ bq.BQI, tyreSensorData *models.TyreSensor) error {
	tableName := "tyre_sensor_data"
	if os.Getenv(constants.ENV_APP_ENV) == "local" {
		tableName = "testing_local_rizqo_tyre_sensor_data"
	}
	inserter := bQ.GetConn().Dataset("sensor_data").Table(tableName).Inserter()
	items := []*models.TyreSensor{
		tyreSensorData,
	}
	var err error
	for attempt := 0; attempt < 5; attempt++ {
		err = inserter.Put(context.Background(), items)
		if err == nil {
			return nil
		}

		time.Sleep(time.Duration(attempt) * time.Second)
	}

	commonlogger.Errorf("failed to insert tyre sensor data err: %v", err)

	return err
}

func enrichGetLatestTrackingDataWithWhere(query *gorm.DB, req models.GetLatestTrackingParam) {
	if req.AssetID != "" {
		query.Where("asset_id = ?", req.AssetID)
	} // AssetID

	if req.ClientID != "" {
		query.Where("client_id = ?", req.ClientID)
	} // ClientID

	if req.InLastMinuets > 0 {
		query.Where("time >  NOW() - INTERVAL '?'", gorm.Expr(fmt.Sprintf("%d minutes", req.InLastMinuets)))
	} // InLastMinuets
}

func (r *trackingRepository) GetLatestTyreSensorData(ctx context.Context, dB database.DBI, req models.GetLatestTrackingParam) (*models.TyreSensor, error) {
	tyreSensorData := models.TyreSensor{}
	query := dB.GetOrm().
		Model(&tyreSensorData).
		Limit(1).
		Order("time DESC")

	enrichGetLatestTrackingDataWithWhere(query, req)
	err := query.Find(&tyreSensorData).Error
	if err != nil {
		return nil, err
	}

	return &tyreSensorData, nil
}

func (r *trackingRepository) GetLatestTyreSensorDataList(ctx context.Context, dB database.DBI, assetID, clientID string, limit int) ([]models.TyreSensor, error) {
	tyreSensorData := []models.TyreSensor{}
	query := dB.GetOrm().
		Model(&models.TyreSensor{}).
		Where("asset_id = ?", assetID).
		Limit(limit).
		Order("time DESC")

	if clientID != "" {
		query.Where("client_id = ?", clientID)
	}
	err := query.Find(&tyreSensorData).Error
	if err != nil {
		return nil, err
	}

	return tyreSensorData, nil
}

func (r *trackingRepository) GetLatestTyreSensorDataLakeByDistinctIDs(ctx context.Context, bQ bq.BQI) ([]models.TyreSensorBQ, error) {
	datasetName := "sensor_data"
	tableName := "tyre_sensor_data"
	if os.Getenv(constants.ENV_APP_ENV) == "local" {
		tableName = "testing_local_rizqo_tyre_sensor_data"
	}

	query := fmt.Sprintf("SELECT integration_id, (ARRAY_AGG(STRUCT(asset_id, time, created_at) ORDER BY time DESC LIMIT 1))[OFFSET(0)].* FROM `%s.%s` t WHERE integration_id IS NOT NULL AND integration_id != "+`""`+" GROUP BY integration_id", datasetName, tableName)

	iter, err := bQ.GetConn().Query(query).Read(ctx)
	if err != nil {
		commonlogger.Errorf("failed to read tyre sensor data err: %v", err)
		return nil, err
	}

	var results []models.TyreSensorBQ
	for {
		row := models.TyreSensorBQ{}
		err := iter.Next(&row)
		if err == iterator.Done {
			break
		}
		if err != nil {
			return results, err
		}

		results = append(results, row)
	}

	return results, err
}

func (r *trackingRepository) IncreaseTyreChangerHM(ctx context.Context, dB database.DBI, tyreChangerHM *models.TyreChangerHM) (int, error) {
	model := models.TyreChangerHM{}
	results := dB.GetTx().
		Model(&model).Clauses(clause.Returning{Columns: []clause.Column{{Name: "hm"}}}).
		Where("integration_id = ?", tyreChangerHM.IntegrationID).
		Updates(map[string]interface{}{
			"time": tyreChangerHM.Time,
			"hm":   gorm.Expr("hm + 1"),
		})
	if results.Error != nil {
		return 0, results.Error
	}

	if results.RowsAffected == 0 {
		err := r.CreateTyreChangerHM(ctx, dB, tyreChangerHM)
		if err != nil {
			return 0, err
		}
	}

	return model.HM, nil
}

func (r *trackingRepository) GetLastetTyreChangerHM(ctx context.Context, dB database.DBI, integrationID string) (int, error) {
	model := models.TyreChangerHM{}
	err := dB.GetTx().Model(&model).Where("integration_id = ?", integrationID).First(&model).Error
	if err != nil {
		return 0, err
	}

	return model.HM, err

}

func (r *trackingRepository) CreateTyreChangerHM(ctx context.Context, dB database.DBI, tyreChangerHM *models.TyreChangerHM) error {
	return dB.GetTx().Create(tyreChangerHM).Error
}

func (r *trackingRepository) CreateTyreChanger(ctx context.Context, dB database.DBI, tyreSensorData *models.TyreChanger) error {
	return dB.GetTx().Create(tyreSensorData).Error
}

func (r *trackingRepository) CreateTyreChangerDataLake(ctx context.Context, bQ bq.BQI, tyreSensorData *models.TyreChanger) error {
	tableName := "tyre_changer_data"
	if os.Getenv(constants.ENV_APP_ENV) == "local" {
		tableName = "testing_local_rizqo_tyre_changer_data"
	}
	inserter := bQ.GetConn().Dataset("sensor_data").Table(tableName).Inserter()
	items := []*models.TyreChanger{
		tyreSensorData,
	}
	var err error
	for attempt := 0; attempt < 5; attempt++ {
		err = inserter.Put(context.Background(), items)
		if err == nil {
			return nil
		}

		time.Sleep(time.Duration(attempt) * time.Second)
	}

	commonlogger.Errorf("failed to insert tyre changer data err: %v", err)

	return err
}

func (r *trackingRepository) CreateTyreChangerState(ctx context.Context, dB database.DBI, tyreChangerStateData *models.TyreChangerState) error {
	return dB.GetTx().Create(tyreChangerStateData).Error
}

func (r *trackingRepository) CreateTyreChangerStateDataLake(ctx context.Context, bQ bq.BQI, tyreChangerStateData *models.TyreChangerState) error {
	tableName := "tyre_changer_state_data"
	if os.Getenv(constants.ENV_APP_ENV) == "local" {
		tableName = "testing_local_rizqo_tyre_changer_state_data"
	}
	inserter := bQ.GetConn().Dataset("sensor_data").Table(tableName).Inserter()
	items := []*models.TyreChangerState{
		tyreChangerStateData,
	}
	var err error
	for attempt := 0; attempt < 5; attempt++ {
		err = inserter.Put(context.Background(), items)
		if err == nil {
			return nil
		}

		time.Sleep(time.Duration(attempt) * time.Second)
	}

	commonlogger.Errorf("failed to insert tyre changer state data err: %v", err)

	return err
}

func (r *trackingRepository) GetLatestTyreChangerData(ctx context.Context, dB database.DBI, req models.GetLatestTrackingParam) (*models.TyreChanger, error) {
	tyreChangerData := models.TyreChanger{}
	query := dB.GetOrm().
		Model(&tyreChangerData).
		Limit(1).
		Order("time DESC")

	enrichGetLatestTrackingDataWithWhere(query, req)
	err := query.Find(&tyreChangerData).Error
	if err != nil {
		return nil, err
	}

	return &tyreChangerData, nil
}

func (r *trackingRepository) GetLatestTyreChangerDataList(ctx context.Context, dB database.DBI, assetID, clientID string, limit int) ([]models.TyreChanger, error) {
	tyreChangerData := []models.TyreChanger{}
	query := dB.GetOrm().
		Model(&models.TyreChanger{}).
		Where("asset_id = ?", assetID).
		Limit(limit).
		Order("time DESC")

	if clientID != "" {
		query.Where("client_id = ?", clientID)
	}
	err := query.Find(&tyreChangerData).Error
	if err != nil {
		return nil, err
	}

	return tyreChangerData, nil
}

func (r *trackingRepository) GetLatestTyreChangerStateData(ctx context.Context, dB database.DBI, req models.GetLatestTrackingParam) (*models.TyreChangerState, error) {
	tyreChangerStateData := models.TyreChangerState{}
	query := dB.GetOrm().
		Model(&tyreChangerStateData).
		Limit(1).
		Order("time DESC")

	enrichGetLatestTrackingDataWithWhere(query, req)
	err := query.Find(&tyreChangerStateData).Error
	if err != nil {
		return nil, err
	}

	return &tyreChangerStateData, nil
}

func (r *trackingRepository) GetLatestTyreChangerStateDataList(ctx context.Context, dB database.DBI, assetID, clientID string, limit int) ([]models.TyreChangerState, error) {
	tyreChangerStateData := []models.TyreChangerState{}
	query := dB.GetOrm().
		Model(&models.TyreChangerState{}).
		Where("asset_id = ?", assetID).
		Limit(limit).
		Order("time DESC")

	if clientID != "" {
		query.Where("client_id = ?", clientID)
	}
	err := query.Find(&tyreChangerStateData).Error
	if err != nil {
		return nil, err
	}

	return tyreChangerStateData, nil
}

func (r *trackingRepository) GetLatestHMDigitalInputData(ctx context.Context, dB database.DBI, req models.GetLatestTrackingParam) (*models.GeneralSensorDigitalInputHM, error) {
	tyreChangerData := models.GeneralSensorDigitalInputHM{}

	var preHm float64
	if err := dB.GetTx().
		Raw(`SELECT pre_hm FROM ssr_digital_input_pre_hm sdiph WHERE asset_id = ?`, req.AssetID).
		Scan(&preHm).Error; err != nil {
		commonlogger.Errorf("error get digital input pre HM err: %v", err)
	}

	maxTime := time.Now()
	if req.MaxTime.Valid {
		maxTime = req.MaxTime.Time
	}

	query := dB.GetTx().Raw(`
        WITH time_diffs AS (
    SELECT
        asset_id,
        time,
        digital_input,
        CASE 
            WHEN  LAG(digital_input) OVER (ORDER BY time) = TRUE THEN 
                EXTRACT(EPOCH FROM (time - LAG(time) OVER (ORDER BY time)))
            ELSE 0
        END AS diff_in_seconds
    FROM ssr_general_sensor_data
    WHERE digital_input IS NOT NULL AND asset_id = ? AND client_id = ? AND time <= ?
)
    SELECT
        asset_id,
        time,
        digital_input,
        SUM(diff_in_seconds) OVER (ORDER BY time)/3600 + ? AS total_hm
    FROM time_diffs ORDER BY time DESC LIMIT 1;
    `, req.AssetID, req.ClientID, maxTime, preHm)

	err := query.Scan(&tyreChangerData).Error
	if err != nil {
		return nil, err
	}

	return &tyreChangerData, nil
}

func enrichLatestIntegrationDataWithWhere(query *gorm.DB, where models.LatestIntegrationDataWhere) {
	if where.IntegrationID != "" {
		query.Where("integration_id = ?", where.IntegrationID)
	} // IntegrationID

	if len(where.IntegrationIDs) > 0 {
		query.Where("integration_id IN ?", where.IntegrationIDs)
	} // IntegrationIDs
}

func (r *trackingRepository) UpsertLatestIntegrationData(ctx context.Context, dB database.DBI, latestIntegrationData *models.LatestIntegrationData) error {
	var latestIntegrationDataEditableColumns []string = []string{
		"integration_id",
		"data_json",
		"client_id",
		"updated_at",
	}

	return dB.GetTx().Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "integration_id"}},
		DoUpdates: clause.AssignmentColumns(latestIntegrationDataEditableColumns),
	}).Create(latestIntegrationData).Error
}

func (r *trackingRepository) GetLatestIntegrationDataList(ctx context.Context, dB database.DBI) ([]models.LatestIntegrationData, error) {
	latestIntegrationData := []models.LatestIntegrationData{}
	query := dB.GetOrm().
		Model(&models.LatestIntegrationData{}).
		Order("updated_at DESC")

	err := query.Find(&latestIntegrationData).Error
	if err != nil {
		return nil, err
	}

	return latestIntegrationData, nil
}

func (r *trackingRepository) GetLatestIntegrationDataListWithParam(ctx context.Context, dB database.DBI, param models.GetLatestIntegrationDataListParam) (int, []models.LatestIntegrationData, error) {
	var totalRecords int64
	latestIntegrationData := []models.LatestIntegrationData{}
	query := dB.GetOrm().
		Model(&models.LatestIntegrationData{})

	enrichLatestIntegrationDataWithWhere(query, param.Cond.Where)

	err := query.Count(&totalRecords).Error
	if err != nil {
		return 0, nil, err
	}

	if totalRecords <= 0 {
		return int(totalRecords), nil, nil
	}

	query.Order("updated_at DESC")
	offset := (param.PageNo - 1) * param.PageSize
	err = query.Offset(offset).Limit(param.PageSize).Error
	if err != nil {
		return 0, nil, err
	}

	return int(totalRecords), latestIntegrationData, nil
}
