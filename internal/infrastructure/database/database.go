package database

import (
	"assetfindr/internal/constants"
	"assetfindr/pkg/common/commonlogger"
	"strconv"

	"github.com/spf13/viper"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"gorm.io/plugin/dbresolver"
)

var (
	DB           *gorm.DB
	TimeseriesDB *gorm.DB
	err          error
)

// DbConnection create database connection
func DbConnection(masterDSN, replicaDSN string) error {

	var db = DB
	logMode := viper.GetBool(constants.CONFIG_DB_MASTER_LOG_MODE)
	debug := viper.GetBool(constants.CONFIG_DB_MASTER_DEBUG)
	loglevel := logger.Info

	commonlogger.Infof("Datababse log mod is " + strconv.FormatBool(logMode))

	if logMode {
		loglevel = logger.Silent
	}

	db, err = gorm.Open(postgres.Open(masterDSN), &gorm.Config{
		Logger:                 logger.Default.LogMode(loglevel),
		SkipDefaultTransaction: true,
	})

	commonlogger.Infof("Datababse debug mod is " + strconv.FormatBool(debug))
	commonlogger.Infof(masterDSN)

	if !debug {

		db.Use(dbresolver.Register(dbresolver.Config{
			Replicas: []gorm.Dialector{
				postgres.Open(replicaDSN),
			},
			Policy: dbresolver.RandomPolicy{},
		}))
	}
	if err != nil {
		commonlogger.Fatalf("Db connection error", err)
		return err
	} else {
		commonlogger.Infof("Connected to database!")
	}

	DB = db
	return nil
}

// GetDB connection
func GetDB() *gorm.DB {
	return DB
}

func TimeseriesDbConnect(dsn string) error {
	logMode := viper.GetBool(constants.CONFIG_DB_MASTER_LOG_MODE)
	debug := viper.GetBool(constants.CONFIG_DB_MASTER_DEBUG)
	loglevel := logger.Info

	commonlogger.Infof("Timeseries datababse log mod is " + strconv.FormatBool(logMode))

	if logMode {
		loglevel = logger.Silent
	}

	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{
		Logger:                 logger.Default.LogMode(loglevel),
		SkipDefaultTransaction: true,
	})
	if err != nil {
		commonlogger.Fatalf("Timeseries Db connection error", err)
		return err
	}

	commonlogger.Infof("Connected to timeseries database!")
	commonlogger.Infof("Timeseries datababse debug mod is " + strconv.FormatBool(debug))

	if !debug {
		db.Use(dbresolver.Register(dbresolver.Config{
			Policy: dbresolver.RandomPolicy{},
		}))
	}

	TimeseriesDB = db
	return nil
}
