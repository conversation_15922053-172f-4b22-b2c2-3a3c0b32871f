package constants

const (
	CONFIG_DB_MASTER_HOST     = "DATABASE_MASTER.HOST"
	CONFIG_DB_MASTER_PASSWORD = "DATABASE_MASTER.PASSWORD"
	CONFIG_DB_MASTER_NAME     = "DATABASE_MASTER.NAME"
	CONFIG_DB_MASTER_USER     = "DATABASE_MASTER.USER"
	CONFIG_DB_MASTER_PORT     = "DATABASE_MASTER.PORT"
	CONFIG_DB_MASTER_SSL_MODE = "DATABASE_MASTER.SSL_MODE"
	CONFIG_DB_MASTER_LOG_MODE = "DATABASE_MASTER.LOG_MODE"
	CONFIG_DB_MASTER_DEBUG    = "DATABASE_MASTER.DEBUG"

	CONFIG_TIMESERIES_DB_HOST     = "TIMESERIES_DB.HOST"
	CONFIG_TIMESERIES_DB_NAME     = "TIMESERIES_DB.NAME"
	CONFIG_TIMESERIES_DB_USER     = "TIMESERIES_DB.USER"
	CONFIG_TIMESERIES_DB_PORT     = "TIMESERIES_DB.PORT"
	CONFIG_TIMESERIES_DB_SSL_MODE = "TIMESERIES_DB.SSL_MODE"
	CONFIG_TIMESERIES_DB_LOG_MODE = "TIMESERIES_DB.LOG_MODE"
	CONFIG_TIMESERIES_DB_DEBUG    = "TIMESERIES_DB.DEBUG"

	CONFIG_DB_REPLICA_HOST     = "DATABASE_REPLICA.HOST"
	CONFIG_DB_REPLICA_PASSWORD = "DATABASE_REPLICA.PASSWORD"
	CONFIG_DB_REPLICA_NAME     = "DATABASE_REPLICA.NAME"
	CONFIG_DB_REPLICA_USER     = "DATABASE_REPLICA.USER"
	CONFIG_DB_REPLICA_PORT     = "DATABASE_REPLICA.PORT"
	CONFIG_DB_REPLICA_SSL_MODE = "DATABASE_REPLICA.SSL_MODE"

	CONFIG_SERVER_LOG_LEVEL     = "SERVER.LOG_LEVEL"
	CONFIG_SERVER_LOG_FILE      = "SERVER.LOG_FILE"
	CONFIG_SERVER_HOST          = "SERVER.HOST"
	CONFIG_SERVER_PORT          = "SERVER.PORT"
	CONFIG_SERVER_DEBUG         = "SERVER.DEBUG"
	CONFIG_SERVER_ALLOWED_HOSTS = "SERVER.ALLOWED_HOSTS"

	CONFIG_JWT_SECRET_KEY  = "JWT_SECRET_KEY"
	CONFIG_SERVER_TIMEZONE = "SERVER.TIMEZONE"

	PUBLIC_GALLERY_JWT_SECRET_KEY = "PUBLIC_GALLERY_JWT_SECRET_KEY"

	CONFIG_GCP_BUCKET = "GCP_STORAGE.BUCKET"

	CONFIG_BQ_PROJECTID = "BIGQUERY_MASTER.PROJECTID"

	CONFIG_FLESPI_MQTT_TOPIC        = "FLESPI.MQTT_TOPIC"
	CONFIG_FLESPI_MQTT_TOPIC_BACKUP = "FLESPI.MQTT_TOPIC_BACKUP"

	CONFIG_SMTP_HOST   = "SMTP.HOST"
	CONFIG_SMTP_PORT   = "SMTP.PORT"
	CONFIG_SMTP_SENDER = "SMTP.SENDER"
)
