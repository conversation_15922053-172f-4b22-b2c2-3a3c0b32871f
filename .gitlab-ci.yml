variables:
  DOCKER_DRIVER: overlay2

stages:
  - migrate
  - job
  - deploy
  - vminstance_deploy

.migrate:abstract:
  stage: migrate
  image: assetfindr/migrate-cloud-sql-proxy:latest
  variables:
    DB_URL_MIGRATION: $DB_URL_MIGRATION
    DB_INSTANCE_NAME: $DB_INSTANCE_NAME
    GCP_SERVICE_KEY: $GCP_SERVICE_KEY
  script:
    - echo $GCP_SERVICE_KEY > service-account-key.json
    - cloud-sql-proxy -c service-account-key.json --quitquitquit $DB_INSTANCE_NAME & sleep 3
    - migrate -path migrations -database "$DB_URL_MIGRATION" up && curl -X POST localhost:9091/quitquitquit

.migrate-v2:abstract:
  stage: migrate
  image: assetfindr/migrate-cloud-sql-proxy:latest
  script:
    - migrate -path $DIR_PATH -database "$URL_MIGRATION" up

.auto-upsert-admin-permission:abstract:
  stage: job
  image: assetfindr/migrate-cloud-sql-proxy:latest
  script:
    - 'curl -X POST "$BASE_URL/v1/admin/user/permissions/groups/upsert-admin" --header "Authorization: Bearer $API_TOKEN"'

.deploy:abstract:
  stage: deploy
  image: google/cloud-sdk
  services:
    - docker:dind
  script:
    - echo $GCP_SERVICE_KEY > gcloud-service-key.json # Google Cloud service accounts
    - echo $GCP_SERVICE_KEY > gcloud-service-key.json.default # Google Cloud service accounts
    - echo "$FIREBASE_PRIVATE_KEY" > firebase-private-key.json.default # Firebase Private key
    - echo "$ASSETFINDR_ENVS" > .env.default # Environment Varibales
    - gcloud auth activate-service-account --key-file gcloud-service-key.json
    - gcloud config set project $GCP_PROJECT_ID
    - echo "Check GCP service name variable name $GCP_SERVICE_NAME"
    - gcloud builds submit . --config=cloudbuild.yml --substitutions=COMMIT_SHA="$CI_COMMIT_SHORT_SHA",_SERVICE_NAME="$GCP_SERVICE_NAME",_ARTIFACT_REPO=$GCP_ARTIFACT_REPO

.deploy-tag:abstract:
  stage: deploy
  image: google/cloud-sdk
  services:
    - docker:dind
  script:
    - echo $GCP_SERVICE_KEY > gcloud-service-key.json # Google Cloud service accounts
    - echo $GCP_SERVICE_KEY > gcloud-service-key.json.default # Google Cloud service accounts
    - echo "$FIREBASE_PRIVATE_KEY" > firebase-private-key.json.default # Firebase Private key
    - echo "$ASSETFINDR_ENVS" > .env.default # Environment Varibales
    - gcloud auth activate-service-account --key-file gcloud-service-key.json
    - gcloud config set project $GCP_PROJECT_ID
    - echo "Check GCP service name variable name $GCP_SERVICE_NAME"
    - gcloud builds submit . --config=cloudbuildv2.yml --substitutions=COMMIT_SHA="$CI_COMMIT_SHORT_SHA",_SERVICE_NAME="$GCP_SERVICE_NAME",_TAG="$DEPLOY_TAG",_ARTIFACT_REPO=$GCP_ARTIFACT_REPO

.traffic-to-tag:abstract:
  stage: deploy
  image: google/cloud-sdk
  services:
    - docker:dind
  script:
    - echo $GCP_SERVICE_KEY > gcloud-service-key.json
    - gcloud auth activate-service-account --key-file gcloud-service-key.json
    - gcloud config set project $GCP_PROJECT_ID
    - gcloud run services update-traffic $GCP_SERVICE_NAME --region asia-southeast1 --to-tags $DEPLOY_TAG=100

.deploy-flespi-mqtt:abstract:
  stage: deploy
  image: google/cloud-sdk
  services:
    - docker:dind
  script:
    - echo $GCP_SERVICE_KEY > gcloud-service-key.json # Google Cloud service accounts
    - echo $GCP_SERVICE_KEY > gcloud-service-key.json.default # Google Cloud service accounts
    - echo "$FIREBASE_PRIVATE_KEY" > firebase-private-key.json.default # Firebase Private key
    - echo "$ASSETFINDR_ENVS" > .env.default # Environment Varibales
    - gcloud auth activate-service-account --key-file gcloud-service-key.json
    - gcloud config set project $GCP_PROJECT_ID
    - echo "Check GCP service name variable name $FLESPI_MQTT_CLIENT_SERVICE_NAME"
    - gcloud builds submit . --config=cloudbuild-flespi-mqtt.yml --substitutions=COMMIT_SHA="$CI_COMMIT_SHORT_SHA",_SERVICE_NAME="$FLESPI_MQTT_CLIENT_SERVICE_NAME",_ARTIFACT_REPO=$GCP_ARTIFACT_REPO

.vminstance_deploy:abstract:
  stage: vminstance_deploy
  when: manual
  image: docker:stable
  services:
    - name: docker:dind
      command: ["--host=tcp://0.0.0.0:2375", "--tls=false", "--storage-driver=overlay2"]
  variables:
    DOCKER_HOST: "tcp://docker:2375"
    DOCKER_TLS_CERTDIR: ""
  script:
    - echo "Adding configuration files..."
    - echo $GCP_SERVICE_KEY > gcloud-service-key.json # Google Cloud service accounts
    - echo "$FIREBASE_PRIVATE_KEY" > firebase-private-key.json # Firebase Private key
    - echo "$ASSETFINDR_ENVS" > .env # Environment Varibales
    - echo "Adding VM host to known_hosts..."
    - mkdir -p ~/.ssh
    - ssh-keyscan -H $DEPLOY_VM_IP >> ~/.ssh/known_hosts
    - echo "Setting temp private key..."
    - echo "$DEPLOY_SSH_PRIVATE_KEY" > /tmp/ssh_private_key
    - chmod 600 /tmp/ssh_private_key
    - echo "Building docker image..."
    - docker build -t $BE_CONTAINER_NAME:$CI_COMMIT_SHORT_SHA .
    - docker save $BE_CONTAINER_NAME:$CI_COMMIT_SHORT_SHA | gzip > $BE_CONTAINER_NAME.tar.gz
    - echo "Copying image to VM..."
    - scp -v -i /tmp/ssh_private_key $BE_CONTAINER_NAME.tar.gz $DEPLOY_VM_USER@$DEPLOY_VM_IP:/home/<USER>/$BE_CONTAINER_NAME.tar.gz
    - echo "Deploying image on VM..."
    - ssh -i /tmp/ssh_private_key $DEPLOY_VM_USER@$DEPLOY_VM_IP "
        echo 'Checking for $BE_CONTAINER_NAME container...' &&
        if [ \"\$(docker ps -aq -f name=$BE_CONTAINER_NAME)\" ]; then 
          echo 'Removing existing $BE_CONTAINER_NAME container and image...'; 
          docker rm -f $BE_CONTAINER_NAME; 
          docker image prune -a --force;
        fi &&
        echo 'Loading image and run docker cotainer...' &&
        docker load < /home/<USER>/$BE_CONTAINER_NAME.tar.gz &&
        docker run -d --restart always --name $BE_CONTAINER_NAME -p $BE_HOST_PORT:8000 $BE_CONTAINER_NAME:$CI_COMMIT_SHORT_SHA
      "
    - echo "Deployment to VM complete."

.vminstance_deploy-flespi-mqtt:abstract:
  stage: vminstance_deploy
  when: manual
  image: docker:stable
  services:
    - name: docker:dind
      command: ["--host=tcp://0.0.0.0:2375", "--tls=false", "--storage-driver=overlay2"]
  variables:
    DOCKER_HOST: "tcp://docker:2375"
    DOCKER_TLS_CERTDIR: ""
  script:
    - echo "Adding configuration files..."
    - echo $GCP_SERVICE_KEY > gcloud-service-key.json # Google Cloud service accounts
    - echo "$FIREBASE_PRIVATE_KEY" > firebase-private-key.json # Firebase Private key
    - echo "$ASSETFINDR_ENVS" > .env # Environment Varibales
    - echo "Adding VM host to known_hosts..."
    - mkdir -p ~/.ssh
    - ssh-keyscan -H $DEPLOY_VM_IP >> ~/.ssh/known_hosts
    - echo "Setting temp private key..."
    - echo "$DEPLOY_SSH_PRIVATE_KEY" > /tmp/ssh_private_key
    - chmod 600 /tmp/ssh_private_key
    - echo "Building docker image..."
    - docker build -f Dockerfile-flespi-mqtt -t $FLESPI_MQTT_CONTAINER_NAME:$CI_COMMIT_SHORT_SHA .
    - docker save $FLESPI_MQTT_CONTAINER_NAME:$CI_COMMIT_SHORT_SHA | gzip > $FLESPI_MQTT_CONTAINER_NAME.tar.gz
    - echo "Copying image to VM..."
    - scp -v -i /tmp/ssh_private_key $FLESPI_MQTT_CONTAINER_NAME.tar.gz $DEPLOY_VM_USER@$DEPLOY_VM_IP:/home/<USER>/$FLESPI_MQTT_CONTAINER_NAME.tar.gz
    - echo "Deploying image on VM..."
    - ssh -i /tmp/ssh_private_key $DEPLOY_VM_USER@$DEPLOY_VM_IP "
        echo 'Checking for $FLESPI_MQTT_CONTAINER_NAME container...' &&
        if [ \"\$(docker ps -aq -f name=$FLESPI_MQTT_CONTAINER_NAME)\" ]; then 
          echo 'Removing existing $FLESPI_MQTT_CONTAINER_NAME container...'; 
          docker rm -f $FLESPI_MQTT_CONTAINER_NAME; 
          docker image prune -a --force;
        fi &&
        echo 'Loading image and run docker cotainer...' &&
        docker load < /home/<USER>/$FLESPI_MQTT_CONTAINER_NAME.tar.gz &&
        docker run -d --restart always --name $FLESPI_MQTT_CONTAINER_NAME -p $FLESPI_MQTT_HOST_PORT:8000 $FLESPI_MQTT_CONTAINER_NAME:$CI_COMMIT_SHORT_SHA
      "
    - echo "Deployment to VM complete."

# --- env: staging ----------------------------------------------------------------------
.job-rules:staging:
  rules:
    - if: $CI_COMMIT_BRANCH =~ /^(develop-.*|poc.*)$/

migrate:staging:
  extends:
    - .migrate-v2:abstract
  environment:
    name: staging
  variables:
    URL_MIGRATION: "$DB_URL_MIGRATION"
    DIR_PATH: migrations
  when: manual
  rules:
    - !reference [".job-rules:staging", "rules"]

migrate-timescale:staging:
  extends:
    - .migrate-v2:abstract
  environment:
    name: staging
  variables:
    URL_MIGRATION: "$TIMESCALE_URL_MIGRATION"
    DIR_PATH: migrations/timescale
  when: manual
  rules:
    - !reference [".job-rules:staging", "rules"]

deploy:staging:
  extends:
    - .vminstance_deploy:abstract
  environment:
    name: staging
  when: manual
  rules:
    - !reference [".job-rules:staging", "rules"]
  dependencies:
    - migrate:staging

deploy-flespi-mqtt:staging:
  extends:
    - .vminstance_deploy-flespi-mqtt:abstract
  environment:
    name: staging
  when: manual
  rules:
    - !reference [".job-rules:staging", "rules"]

auto-upsert-admin-permission:staging:
  extends:
    - .auto-upsert-admin-permission:abstract
  environment:
    name: staging
  variables:
    BASE_URL: "https://api-staging.assetfindr.com"
    API_TOKEN: "8Gvh3pqt1425"
  when: manual
  rules:
    - !reference [".job-rules:staging", "rules"]

# # Comment for better testing
# deploy-green:staging:
#   extends:
#     - .deploy-tag:abstract
#   environment:
#     name: staging
#   variables:
#     DEPLOY_TAG: green
#   when: manual
#   rules:
#     - !reference [".job-rules:staging", "rules"]

# deploy-blue:staging:
#   extends:
#     - .deploy-tag:abstract
#   environment:
#     name: staging
#   variables:
#     DEPLOY_TAG: blue
#   when: manual
#   rules:
#     - !reference [".job-rules:staging", "rules"]

# traffic-to-green:staging:
#   extends:
#     - .traffic-to-tag:abstract
#   environment:
#     name: staging
#   variables:
#     DEPLOY_TAG: green
#   when: manual

# traffic-to-blue:staging:
#   extends:
#     - .traffic-to-tag:abstract
#   environment:
#     name: staging
#   variables:
#     DEPLOY_TAG: blue
#   when: manual


# --- env: sandbox ----------------------------------------------------------------------
.job-rules:sandbox:
  rules:
    - if: '$CI_COMMIT_BRANCH == "new-main"'

migrate:sandbox:
  extends:
     - .migrate-v2:abstract
  environment:
    name: sandbox
  variables:
    URL_MIGRATION: "$DB_URL_MIGRATION"
    DIR_PATH: migrations
  when: manual
  rules:
    - !reference [".job-rules:sandbox", "rules"]

migrate-timescale:sandbox:
  extends:
    - .migrate-v2:abstract
  environment:
    name: sandbox
  variables:
    URL_MIGRATION: "$TIMESCALE_URL_MIGRATION"
    DIR_PATH: migrations/timescale
  when: manual
  rules:
    - !reference [".job-rules:sandbox", "rules"]

deploy:sandbox:
  extends:
    - .vminstance_deploy:abstract
  environment:
    name: sandbox
  when: manual
  rules:
    - !reference [".job-rules:sandbox", "rules"]
  dependencies:
    - migrate:sandbox

deploy-flespi-mqtt:sandbox:
  extends:
    - .vminstance_deploy-flespi-mqtt:abstract
  environment:
    name: sandbox
  when: manual
  rules:
    - !reference [".job-rules:sandbox", "rules"]

auto-upsert-admin-permission:sandbox:
  extends:
    - .auto-upsert-admin-permission:abstract
  environment:
    name: sandbox
  variables:
    BASE_URL: "https://api-sandbox.assetfindr.com"
    API_TOKEN: "8Gvh3pqt1425"
  when: manual
  rules:
    - !reference [".job-rules:sandbox", "rules"]

# --- env: production ----------------------------------------------------------------------
.job-rules:production:
  rules:
    - if: "$CI_COMMIT_TAG != null && $CI_COMMIT_TAG !~ /^iot-/"

.job-rules:iot-production:
  rules:
    - if: '$CI_COMMIT_TAG != null && $CI_COMMIT_TAG =~ /^iot-/'

migrate:production:
  extends:
    - .migrate:abstract
  environment:
    name: production
  when: manual
  rules:
    - !reference [".job-rules:production", "rules"]

migrate-timescale:production:
  extends:
    - .migrate-v2:abstract
  environment:
    name: production
  variables:
    URL_MIGRATION: "$TIMESCALE_URL_MIGRATION"
    DIR_PATH: migrations/timescale
  when: manual
  rules:
    - !reference [".job-rules:production", "rules"]
    - !reference [".job-rules:iot-production", "rules"]

deploy:production:
  extends:
    - .deploy:abstract
  environment:
    name: production
  when: manual
  rules:
    - !reference [".job-rules:production", "rules"]
  dependencies:
    - migrate:production

deploy-green:production:
  extends:
    - .deploy-tag:abstract
  environment:
    name: production
  variables:
    DEPLOY_TAG: green
  when: manual
  rules:
    - !reference [".job-rules:production", "rules"]
  dependencies:
    - migrate:production

deploy-blue:production:
  extends:
    - .deploy-tag:abstract
  environment:
    name: production
  variables:
    DEPLOY_TAG: blue
  when: manual
  rules:
    - !reference [".job-rules:production", "rules"]
  dependencies:
    - migrate:production

deploy-flespi-mqtt:production:
  extends:
    - .deploy-flespi-mqtt:abstract
  environment:
    name: production
  when: manual
  rules:
    - !reference [".job-rules:production", "rules"]
    - !reference [".job-rules:iot-production", "rules"]

traffic-to-green:production:
  extends:
    - .traffic-to-tag:abstract
  environment:
    name: production
  variables:
    DEPLOY_TAG: green
  when: manual
  rules:
    - !reference [".job-rules:production", "rules"]

traffic-to-blue:production:
  extends:
    - .traffic-to-tag:abstract
  environment:
    name: production
  variables:
    DEPLOY_TAG: blue
  when: manual
  rules:
    - !reference [".job-rules:production", "rules"]

auto-upsert-admin-permission:production:
  extends:
    - .auto-upsert-admin-permission:abstract
  environment:
    name: production
  variables:
    BASE_URL: "https://api.assetfindr.com"
    API_TOKEN: "8Gvh3pqt1425"
  when: manual
  rules:
    - !reference [".job-rules:production", "rules"]
