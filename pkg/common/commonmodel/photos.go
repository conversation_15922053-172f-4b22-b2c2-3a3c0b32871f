package commonmodel

import (
	"assetfindr/internal/app/storage/constants"
	"strings"
)

type PhotoReq struct {
	ID       string `json:"id"`
	Label    string `json:"label"`
	Path     string `json:"path"`
	IsDelete bool   `json:"is_delete"`
}

func (d PhotoReq) IsNew() bool {
	if d.ID == "" {
		return true
	}

	return false
}

func IsInTempFileLoc(name string) bool {
	return strings.HasPrefix(name, constants.TEMP_USER_UPLOAD_PREFIX)
}
