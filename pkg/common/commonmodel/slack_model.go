package commonmodel

type SlackMessagePayload struct {
	Blocks []Block `json:"blocks"`
}

type Block struct {
	Type     string         `json:"type"`
	Text     *TextElement		`json:"text,omitempty"`
	Elements []any  				`json:"elements,omitempty"`
}

type TextElement struct {
	Type string `json:"type"`
	Text string `json:"text"`
}

type RichTextList struct {
	Type     string             `json:"type"`
	Style    string             `json:"style"`
	Indent   int                `json:"indent"`
	Elements []RichTextSection  `json:"elements"`
}

type RichTextSection struct {
	Type     string        `json:"type"`
	Elements []TextElement `json:"elements"`
}


