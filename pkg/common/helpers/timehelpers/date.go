package timehelpers

import (
	"assetfindr/pkg/common/commonlogger"
	"time"
)

func ParseDateFilter(dateString string, isEndDate bool) (time.Time, error) {
	layout := "2006-01-02"

	parsedDate, err := time.ParseInLocation(layout, dateString, time.Local)
	if err != nil {
		return time.Time{}, err
	}

	if isEndDate {
		parsedDate = time.Date(
			parsedDate.Year(),
			parsedDate.Month(),
			parsedDate.Day(),
			23, 59, 59, 0, // hour, minute, second, nanosecond
			parsedDate.Location(),
		)
	}

	return parsedDate, nil
}

func ParseDateTime(dateString string) (time.Time, error) {
	layout := "2006-01-02 15:04:05"
	location, err := time.LoadLocation("Asia/Bangkok") // UTC+7
	if err != nil {
		commonlogger.Warnf("error parse date, load location %v", err)
		return time.Time{}, err
	}

	parsedDate, err := time.ParseInLocation(layout, dateString, location)
	if err != nil {
		commonlogger.Warnf("error parse date %v", err)
		return time.Time{}, err
	}
	return parsedDate, nil
}

func GetShiftCode(t time.Time) string {
	// force to UTC+8
	utc8 := time.FixedZone("UTC+8", 8*60*60)
	localTime := t.In(utc8)

	println(localTime.String())

	outboundTime := time.Date(0, 1, 1, localTime.Hour(), localTime.Minute(), localTime.Second(), 0, utc8)

	// new shift boundaries
	dayStart := time.Date(0, 1, 1, 6, 59, 59, 0, utc8) // 06:59:59
	dayEnd := time.Date(0, 1, 1, 18, 59, 59, 0, utc8)  // 18:59:59

	if outboundTime.After(dayEnd) || outboundTime.Before(dayStart) {
		return "NS"
	}
	return "DS"
}
