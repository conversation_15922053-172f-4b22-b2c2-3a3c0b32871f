package useridentityservice

import (
	assetPersistence "assetfindr/internal/app/asset/persistence"
	financePersistence "assetfindr/internal/app/finance/presistence"
	financeUseCase "assetfindr/internal/app/finance/usecase"
	integrationPersistence "assetfindr/internal/app/integration/presistence"
	inventoryPersistence "assetfindr/internal/app/inventory/persistence"
	notificationPersistence "assetfindr/internal/app/notification/presistence"
	notificationUsecase "assetfindr/internal/app/notification/usecase"
	taskPersistence "assetfindr/internal/app/task/persistence"
	userHandlr "assetfindr/internal/app/user-identity/handler"
	userPersistence "assetfindr/internal/app/user-identity/persistence"
	userRouters "assetfindr/internal/app/user-identity/routers"
	userUsecase "assetfindr/internal/app/user-identity/usecase"
	"assetfindr/internal/infrastructure/cloudStorage"
	"assetfindr/internal/infrastructure/database"

	storagePersistence "assetfindr/internal/app/storage/persistence"
	storageUsecase "assetfindr/internal/app/storage/usecase"

	"github.com/gin-gonic/gin"

	"assetfindr/internal/infrastructure/email"
)

func InitializeUser(dbUsecase database.DBUsecase, router *gin.Engine) error {
	// Respository
	userRepository := userPersistence.NewUserRepository()
	notificationRepo := notificationPersistence.NewAssetTyreRepository()
	emailRepo := notificationPersistence.NewEmailRepository(email.GetEmailService())
	storageRepository := storagePersistence.NewStorageRepository(cloudStorage.Bucket)
	attachmentRepository := storagePersistence.NewAttachmentRepository()
	permissionRepository := userPersistence.NewPermissionRepository()
	financeRepo := financePersistence.NewFinanceRepository()
	partnerRepo := userPersistence.NewPartnerRepository()
	contactRepo := userPersistence.NewContactRepository()
	partnerContactRepo := userPersistence.NewPartnerContactRepository()
	departmentRepo := userPersistence.NewDepartmentRepository()
	clientRepo := userPersistence.NewClientRepository()
	accurateRepo := integrationPersistence.NewAccurateRepository()
	integrationrepo := integrationPersistence.NewIntegrationRepository()
	locationRepo := assetPersistence.NewLocationRepository()
	assetRepo := assetPersistence.NewAssetRepository()
	assetTransactionRepo := assetPersistence.NewAssetTransactionRepository()
	assetTyreTreadRepo := assetPersistence.NewAssetTyreRepository()
	ticketRepo := taskPersistence.NewTicketRepository()
	packageRepo := inventoryPersistence.NewPackageRepository()
	analyticRepo := userPersistence.NewAnalyticRepository()
	assetInspectionFindingRepo := assetPersistence.NewAssetInspectionFindingRepository()

	// Use case
	attachmentUseCase := storageUsecase.NewAttachmentUseCase(dbUsecase, attachmentRepository, storageRepository)
	partnerUseCase := userUsecase.NewPartnerUseCase(
		dbUsecase,
		partnerRepo,
		contactRepo,
		partnerContactRepo,
		accurateRepo,
		integrationrepo,
		assetRepo,
		assetTransactionRepo,
		assetTyreTreadRepo,
		ticketRepo,
		packageRepo,
		attachmentUseCase,
	)
	departmentUseCase := userUsecase.NewDepartmentUseCase(dbUsecase, departmentRepo, userRepository, ticketRepo)
	financeUseCase := financeUseCase.NewFinanceUsecase(
		dbUsecase,
		financeRepo,
	)
	notificationUc := notificationUsecase.NewNotificationUsecase(
		dbUsecase,
		notificationRepo,
		emailRepo,
		userRepository,
	)

	userUseCase := userUsecase.NewUserUseCase(
		dbUsecase,
		userRepository,
		permissionRepository,
		attachmentUseCase,
		financeUseCase,
		clientRepo,
		departmentRepo,
		locationRepo,
		assetPersistence.NewCustomAssetCategoryRepository(),
		assetInspectionFindingRepo,
		assetPersistence.NewVehicleTargetTyreRemovalRepository(),
	)
	permissionUseCase := userUsecase.NewPermissionUseCase(dbUsecase, permissionRepository, userRepository)
	userUseCase.SetPermissionUseCase(permissionUseCase)
	userUseCase.SetNotifUseCase(&notificationUc)
	clientUsecase := userUsecase.NewClientUseCase(
		dbUsecase, userRepository, attachmentUseCase,
		permissionRepository,
		financeUseCase,
		clientRepo,
	)
	analyticUsecase := userUsecase.NewAnalyticUseCase(
		dbUsecase,
		analyticRepo,
	)

	// Handler
	departmentHandler := userHandlr.NewDepartmentHandler(
		departmentUseCase,
	)
	partnerHandler := userHandlr.NewPartnerHandler(
		partnerUseCase,
	)
	userHandler := userHandlr.NewUserHandler(
		userUseCase,
		permissionUseCase,
	)
	permissionHandler := userHandlr.NewPermissionHandler(
		permissionUseCase,
	)
	clientHandler := userHandlr.NewClientHandler(
		clientUsecase,
	)
	analyticHandler := userHandlr.NewAnalyticHandler(
		analyticUsecase,
	)

	userRouters.RegisterPartnerRoutes(router, partnerHandler)
	userRouters.RegisterDepartmentRoutes(router, departmentHandler)
	userRouters.RegisterUserIdentityRoutes(router, userHandler)
	userRouters.RegisterPermissionRoutes(router, permissionHandler)
	userRouters.RegisterClientRoutes(router, clientHandler)
	userRouters.RegisterAnalyticRoutes(router, analyticHandler)
	return nil
}
