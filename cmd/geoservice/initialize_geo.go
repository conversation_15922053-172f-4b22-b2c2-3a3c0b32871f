package geoservice

import (
	"assetfindr/internal/infrastructure/bq"
	"assetfindr/internal/infrastructure/database"
	"assetfindr/internal/infrastructure/email"

	"github.com/gin-gonic/gin"

	trackingHandlr "assetfindr/internal/app/geo/handler"
	trackingRouters "assetfindr/internal/app/geo/routers"
	trackingUsecase "assetfindr/internal/app/geo/usecase"

	assetPersistence "assetfindr/internal/app/asset/persistence"
	trackingPersistence "assetfindr/internal/app/geo/persistence"
	integrationPersistence "assetfindr/internal/app/integration/presistence"
	notificationPersistence "assetfindr/internal/app/notification/presistence"
	taskPersistence "assetfindr/internal/app/task/persistence"

	notificationUsecase "assetfindr/internal/app/notification/usecase"
	userIdentityPersistence "assetfindr/internal/app/user-identity/persistence"
)

func InitializeIntegration(bq bq.BQUsecase, dbUsecase, dbTimeScaleUsecase database.DBUsecase, router *gin.Engine) error {

	trackingRepository := trackingPersistence.NewTrackingRepository()

	assetVehicleRepo := assetPersistence.NewAssetVehicleRepository()
	assetRepo := assetPersistence.NewAssetRepository()
	assetAssignmentRepo := assetPersistence.NewAssetAssignmentRepository()
	ticketRepo := taskPersistence.NewTicketRepository()

	userRepo := userIdentityPersistence.NewUserRepository()
	integrationRepository := integrationPersistence.NewIntegrationRepository()
	alertRepo := integrationPersistence.NewAlertRepository()
	notificationRepo := notificationPersistence.NewAssetTyreRepository()
	emailRepo := notificationPersistence.NewEmailRepository(email.GetEmailService())
	notificationUc := notificationUsecase.NewNotificationUsecase(
		dbUsecase,
		notificationRepo,
		emailRepo,
		userRepo,
	)
	trackingUseCase := trackingUsecase.NewTrackingUseCase(
		dbUsecase,
		bq,
		dbTimeScaleUsecase,
		trackingRepository,
		integrationRepository,
		assetVehicleRepo,
		assetAssignmentRepo,
		userRepo,
		assetRepo,
		alertRepo,
		ticketRepo,
		notificationUc,
		assetPersistence.NewAssetLinkedRepository(),
	)
	trackingHandler := trackingHandlr.NewTrackingHandler(trackingUseCase)

	trackingRouters.RegisterTrackingRoutes(router, trackingHandler)
	return nil
}
