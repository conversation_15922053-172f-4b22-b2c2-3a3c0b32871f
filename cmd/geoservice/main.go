package geoservice

import (
	"assetfindr/internal/infrastructure/bq"
	"assetfindr/internal/infrastructure/database"

	"github.com/gin-gonic/gin"
)

func Register(router *gin.Engine) {

	err := InitializeIntegration(bq.NewBQUsecase(bq.BQ), database.NewGormDBUsecase(database.DB), database.NewGormDBTimeSeriesUsecase(database.TimeseriesDB), router)
	if err != nil {
		panic("failed to initialize application")
	}

}
