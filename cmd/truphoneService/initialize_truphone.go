package truphoneservice

import (
	integrationPersistence "assetfindr/internal/app/integration/presistence"
	notificationPersistence "assetfindr/internal/app/notification/presistence"
	"assetfindr/internal/app/truphone/presistence"

	"assetfindr/internal/app/truphone/handler"
	"assetfindr/internal/app/truphone/routers"
	"assetfindr/internal/app/truphone/usecase"
	"assetfindr/internal/infrastructure/database"
	"assetfindr/internal/infrastructure/email"

	"github.com/gin-gonic/gin"
)

func InitializeTruphone(dbUsecase database.DBUsecase, router *gin.Engine) error {
	emailRepo := notificationPersistence.NewEmailRepository(email.GetEmailService())
	integrationRepo := integrationPersistence.NewIntegrationRepository()
	truphoneWebhookRepo := presistence.NewTruphoneWebhookRepository()

	truphoneUseCase := usecase.NewTruphoneUseCase(dbUsecase, emailRepo, integrationRepo)
	truphoneWebhookUseCase := usecase.NewTruphoneWebhookUseCase(truphoneWebhookRepo)

	truphoneHandler := handler.NewTruphoneHandler(truphoneUseCase)
	truphoneWebhookHandler := handler.NewTruphoneWebhookHandler(truphoneWebhookUseCase)

	routers.RegisterTruphoneRoutes(router, truphoneHandler)
	routers.RegisterTruphoneWebhookRoutes(router, truphoneWebhookHandler)

	return nil
}
